<?php

namespace App\Resources;

use App\ResourceDelegates\AffiliateDelegate;
use Common\Models\Affiliate;
use Core\Components\Resource\Classes\Resource;

class AffiliateResource extends Resource
{
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_DELETE);

    protected $table = 'affiliates';
    protected $model = Affiliate::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(AffiliateDelegate::class);
    }
}
