<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;

class CompanyDrawingNodeType extends Model
{
    protected $table = 'companyDrawingNodeTypes';
    protected $primaryKey = 'companyDrawingNodeTypeID';
    protected $fillable = [
        'drawingNodeTypeID', 'name', 'isInLegend'
    ];
    protected $casts = [
        'drawingNodeTypeID' => 'int',
        'isInLegend' => 'bool'
    ];

    public function drawingNodeType()
    {
        return $this->belongsTo(DrawingNodeType::class, 'drawingNodeTypeID', 'drawingNodeTypeID');
    }
}
