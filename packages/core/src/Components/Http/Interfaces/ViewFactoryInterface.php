<?php

namespace Core\Components\Http\Interfaces;

use Closure;
use Core\Components\Http\Classes\View;

/**
 * Interface ViewFactoryInterface
 *
 * @package Core\Components\Http\Interfaces
 */
interface ViewFactoryInterface
{
    /**
     * @param string $path
     * @param null|string $alias
     * @return $this
     */
    public function addPath($path, $alias = null);

    /**
     * @param string|array $pattern
     * @param string|Closure $builder
     * @param bool $regex
     * @return $this
     */
    public function builder($pattern, $builder, $regex = false);

    /**
     * @param ViewInterface $view
     */
    public function applyBuilders(ViewInterface $view): void;

    /**
     * @param string $name
     * @param string|Closure $helper
     * @return $this
     */
    public function helper($name, $helper);

    /**
     * @param string $name
     * @param null|string $path
     * @return bool
     */
    public function exists($name, $path = null);

    /**
     * @param string $name
     * @param array $data
     * @param null|string $path
     * @return View
     */
    public function fetch($name, array $data = [], $path = null);
}
