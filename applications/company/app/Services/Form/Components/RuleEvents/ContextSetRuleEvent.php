<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleEvents;

use App\Resources\Form\Item\Group\Rule\EventResource;
use App\Services\Form\Classes\Structure\Group\Rule\Event;
use App\Services\Form\Exceptions\ValidationException;

/**
 * Class ContextSetRuleEvent
 *
 * @package App\Services\Form\Components\RuleEvents
 */
class ContextSetRuleEvent extends Event
{
    /**
     * @var int Event type
     */
    protected int $type = EventResource::TYPE_CONTEXT_SET;

    /**
     * @var string|null Context key
     */
    protected ?string $key = null;

    /**
     * @var mixed|null Context value
     */
    protected mixed $value = null;

    /**
     * Set context key which to set value for
     *
     * @param string|null $key
     * @return $this
     */
    public function setKey(?string $key): self
    {
        $this->key = $key;
        return $this;
    }

    /**
     * Get context key
     *
     * @return string|null
     */
    public function getKey(): ?string
    {
        return $this->key;
    }

    /**
     * Set context value
     *
     * @param mixed $value
     * @return $this
     */
    public function setValue(mixed $value): self
    {
        $this->value = $value;
        return $this;
    }

    /**
     * Get value
     *
     * @return mixed
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    /**
     * Set the main param containing all the config options for event
     *
     * @param array $param
     * @throws \App\Services\Form\Exceptions\FormException
     */
    protected function setParam(array $param): void
    {
        $this->importFromArray([
            'key' => ['string', 'setKey'],
            'value' => ['mixed', 'setValue'],
        ], $param);
    }

    /**
     * Set params
     *
     * The first param will be sent to setParam() to fill in the data of this class. This event type stores it's info
     * in an array for the first param.
     *
     * @param array $params
     * @return $this
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function setParams(array $params): self
    {
        $this->importFromArray([
            0 => ['array', 'setParam']
        ], $params);
        return $this;
    }

    /**
     * Get main param config
     *
     * @return array
     */
    protected function getParam(): array
    {
        return $this->exportToArray([
            'key' => 'getKey',
            'value' => 'getValue'
        ]);
    }

    /**
     * Get params
     *
     * @param bool $export
     * @return array
     */
    public function getParams(bool $export = false): array
    {
        return [$this->getParam()];
    }

    /**
     * Validate event
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();

        if ($this->getKey() === null) {
            throw new ValidationException('Key is required [%s]', $this->getLocation());
        }
    }
}
