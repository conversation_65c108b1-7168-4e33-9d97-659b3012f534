<?php

namespace Core\Components\Http\Interfaces;

use Core\Components\Http\Classes\ContentType;
use Core\Components\Http\Classes\Input;
use Core\Components\Http\Classes\URI;

/**
 * Interface RequestInterface
 *
 * @package Core\Components\Http\Interfaces
 */
interface RequestInterface
{
    const IP_TYPE_V4 = 1;
    const IP_TYPE_V6 = 2;

    /**
     * @return string
     */
    public function method();

    /**
     * @return URI
     */
    public function uri();

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function get($key = null, $default = null);

    /**
     * @return Input
     */
    public function input();

    /**
     * @return IOInterface
     */
    public function io();

    /**
     * @return SessionInterface
     */
    public function session();

    /**
     * @param RouteInterface $route
     * @return mixed
     */
    public function setRoute(RouteInterface $route);

    /**
     * @return RouteInterface|null
     */
    public function route();

    /**
     * @return bool
     */
    public function isAjax();

    /**
     * @param string|array $media_types
     * @param null|string|ContentType $default
     * @return null|ContentType
     */
    public function prefers($media_types, $default = null): ?ContentType;

    /**
     * @param array $data
     * @return $this
     */
    public function setData(array $data);

    /**
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function setDatum($key, $value);

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function data($key = null, $default = null);

    /**
     * @param bool $ipv4_to_ipv6
     * @return array
     */
    public function ip(bool $ipv4_to_ipv6 = false);
}
