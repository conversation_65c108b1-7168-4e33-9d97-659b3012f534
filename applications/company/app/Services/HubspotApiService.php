<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\HubspotApi\Exceptions\HubspotApiException;
use App\Services\HubspotApi\Jobs\{ContactUpdateOrCreateJob, DealSyncContactsJob, DealUpdateOrCreateJob};
use App\Services\HubspotApi\Objects\{CompanyObject, ContactObject, DealObject};
use Brick\Math\{BigDecimal, RoundingMode};
use Carbon\Carbon;
use Common\Models\{Company, CompanyIntake, CompanySubscription, IntakeFeature, Registration, Timezone, User};
use Core\StaticAccessors\{App, Config};
use HubSpot\Discovery\Discovery;
use HubSpot\Factory;

/**
 * Class HubspotApiService
 *
 * @package App\Services
 */
class HubspotApiService
{
    /**
     * @var Discovery|null Hubspot client
     */
    protected ?Discovery $client = null;

    /**
     * @var ThrottlerService|null Throttler instance
     */
    protected ?ThrottlerService $throttler = null;

    /**
     * Get and cache Hubspot client
     *
     * @return Discovery
     */
    public function getClient(): Discovery
    {
        if ($this->client === null) {
            $this->client = Factory::createWithAccessToken(Config::get('marketing.hubspot.access_token'));
        }
        return $this->client;
    }

    /**
     * Get and cache throttler instance
     *
     * @return ThrottlerService
     */
    public function getThrottler(): ThrottlerService
    {
        if ($this->throttler === null) {
            $this->throttler = new ThrottlerService('hubspot-api', (int) Config::get('marketing.hubspot.queries_per_second', 10), 1000);
        }
        return $this->throttler;
    }

    /**
     * Determines of Hubspot integration is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return Config::get('marketing.enabled') && Config::get('marketing.hubspot.enabled');
    }

    /**
     * Get proper date format which API excepts
     *
     * @param Carbon|null $date
     * @return string|null
     */
    protected function getDateProperty(?Carbon $date): ?string
    {
        return $date?->format('Y-m-d');
    }

    /**
     * Get proper date time format which API excepts
     *
     * @param Carbon|null $date
     * @return string|null
     */
    protected function getDateTimeProperty(?Carbon $date): ?string
    {
        return $date?->format('Y-m-d\TH:i:s.v\Z');
    }

    /**
     * Get all active primary users by company id
     *
     * @param int $company_id
     * @return User[]
     */
    protected function getPrimaryUsersByCompanyID(int $company_id): array
    {
        return User::query()
            ->ofCompany($company_id)
            ->ofRole(User::ROLE_PRIMARY)
            ->active()
            ->notInvited()
            ->with(['primaryPhone'])
            ->get()
            ->all();
    }

    /**
     * Search for existing company in Hubpot and return associated id if found
     *
     * Searches by primary user emails to find a contact. If contacts are found and a single company association
     * is found, then it's id is returned.
     *
     * @param Company $company
     * @return string|null
     * @throws HubspotApiException
     */
    protected function findCompanyID(Company $company): ?string
    {
        $contacts = [];
        $contact_object = new ContactObject($this);
        foreach ($this->getPrimaryUsersByCompanyID($company->getKey()) as $user) {
            if (($contacts = $contact_object->search($user->userEmail)) === null) {
                continue;
            }
            break;
        }
        $company_ids = [];
        if (count($contacts) > 0) {
            foreach ($contacts as $contact) {
                $company_ids[] = $contact_object->getAssociatedCompanyID($contact->getID());
            }
        }
        if (count($company_ids) === 0) {
            return null;
        }
        $company_ids = array_unique($company_ids);
        if (count($company_ids) !== 1) {
            // @todo use more specific exception
            throw new HubspotApiException('Multiple company matches found for company id: %d', $company->getKey());
        }
        return $company_ids[0];
    }

    /**
     * Get properties for company object
     *
     * @param Company $company
     * @return array
     * @throws \Core\Exceptions\AppException
     */
    protected function getCompanyProperties(Company $company): array
    {
        /** @var DomainService $domain_service */
        $domain_service = App::get(DomainService::class);
        $brand = $domain_service->findByCompanyID($company->getKey())->brand;

        $domain = '';
        $website = '';
        if ($company->website !== null) {
            if (preg_match('#^(?:https?://)?([^/]+)#', $company->website, $match) === 1) {
                $domain = $match[1] ?? '';
            }
            $website = $company->website;
        }


        $properties = [
            'name' => $company->name, // hubspot provided
            'phone' => $company->primaryPhone->phoneNumber, // hubspot provided
            'domain' => $domain, // hubspot provided
            'website' => $website, // hubspot provided
            'brand' => $brand->getKey(), // custom
            'address' => $company->address ?? '', // hubspot provided
            'address2' => $company->address2 ?? '', // hubspot provided
            'city' => $company->city ?? '', // hubspot provided
            'state' => $company->state ?? '', // hubspot provided
            'zip' => $company->zip ?? '', // hubspot provided
            'country' => $company->state !== null ? CountryService::getByRegionCode($company->state) : '', // hubspot provided
            'hs_lead_status' => $company->status, // hubspot provided
            'reseller_name' => $company->reseller->name, // custom
            'hubspot_owner_id' => $company->successManager->hubspotCompanyOwnerID
        ];

        /** @var CompanyIntake $intake */
        $intake = $company->intake;
        if ($intake !== null) {
            $features = array_map(fn(IntakeFeature $feature): int => $feature->getKey(), $intake->features->all());
            $properties = array_merge($properties, [
                'ca_industry' => $intake->intakeIndustryID, // custom
                'number_of_users' => $intake->intakeUserCountID, // custom
                'intake_marketing_source' => $intake->intakeMarketingSourceID, // custom
                'feature_interest' => implode(';', $features) // custom
            ]);
        }

        return $properties;
    }

    /**
     * Push company to Hubspot
     *
     * If no company id is stored on the app company record, then we search for one in Hubspot to prevent duplicates.
     * If none found, company is created. Otherwise, we update the existing company with new values.
     *
     * @param Company $company
     * @param bool $push_contacts
     * @throws HubspotApiException
     * @throws \Core\Exceptions\AppException
     */
    public function pushCompany(Company $company, bool $push_contacts = true): void
    {
        $properties = $this->getCompanyProperties($company);

        $hs_company_id = $company->hubspotCompanyID;
        $hs_id_exists = $hs_company_id !== null;
        if ($hs_company_id === null && ($company_id = $this->findCompanyID($company)) !== null) {
            if (Company::query()->where('hubspotCompanyID', $company_id)->count() !== 0) {
                throw new HubspotApiException('Hubspot company id already assigned to another company');
            }
            $hs_company_id = $company_id;
        }
        $company_object = new CompanyObject($this);
        $new_company_status = null;
        if ($hs_company_id === null) {
            $company_entity = $company_object->create($properties);
            $hs_company_id = $company_entity->getID();
            $new_company_status = $company->status;
        } else {
            $company_entity = $company_object->find($hs_company_id, ['hs_lead_status']);
            if ($company_entity->getStatus() !== $company->status) {
                $new_company_status = $company->status;
            }
            $company_object->update($hs_company_id, $properties);
        }
        if (!$hs_id_exists) {
            $company->hubspotCompanyID = $hs_company_id;
            $company->save();
        }

        if ($push_contacts) {
            foreach ($this->getPrimaryUsersByCompanyID($company->getKey()) as $primary_user) {
                ContactUpdateOrCreateJob::enqueue($primary_user->getKey(), false);
            }
        }

        if (
            in_array($company->status, [Company::STATUS_TRIAL, Company::STATUS_ACTIVE, Company::STATUS_SUSPENDED, Company::STATUS_DORMANT]) ||
            ($company->status === Company::STATUS_SIGNUP && $company->signupStatus === Company::SIGNUP_STATUS_SUBSCRIPTION)
        ) {
            DealUpdateOrCreateJob::enqueue($company->getKey(), $new_company_status);
        }
    }

    /**
     * Search for existing contact by email and return id
     *
     * @param User $user
     * @return string|null
     */
    protected function findContactID(User $user): ?string
    {
        $contact = (new ContactObject($this))->search($user->userEmail)[0] ?? null;
        return $contact?->getID();
    }

    /**
     * Get properties for contact object
     *
     * @param User $user
     * @return array
     */
    protected function getContactProperties(User $user): array
    {
        /** @var Timezone $timezone */
        $timezone = $user->timezone ?? $user->company->_timezone;
        return [
            'firstname' => $user->userFirstName, // hubspot provided
            'lastname' => $user->userLastName, // hubspot provided
            'email' => $user->userEmail, // hubspot provided
            'phone' => $user->primaryPhone->phoneNumber, // hubspot provided
            'company' => $user->company->name, // hubspot provided
            'hs_lead_status' => $user->company->status, // hubspot provided
            'ca_industry' => $user->company->intake?->intakeIndustryID ?? '', // custom
            'time_zone' => $timezone->getKey(), // custom
            'hubspot_owner_id' => $user->company->successManager->hubspotContactOwnerID
        ];
    }

    /**
     * Push contact to Hubspot
     *
     * If no contact id is stored on app user record, then we search for an existing contact to prevent duplicates. If
     * none found, we create the contact. Otherwise, we update the existing record with new values.
     *
     * @param User $user
     * @param bool $sync_with_deal
     * @throws HubspotApiException
     */
    public function pushContact(User $user, bool $sync_with_deal = true): void
    {
        $company = $user->company()->first(['hubspotCompanyID', 'hubspotDealID']);
        if ($company->hubspotCompanyID === null) {
            throw new HubspotApiException('No Hubspot company id associated with user\'s company');
        }

        $properties = $this->getContactProperties($user);

        $hs_contact_id = $user->hubspotContactID;
        $hs_id_exists = $hs_contact_id !== null;
        $contact_object = new ContactObject($this);
        if ($hs_contact_id === null && ($contact_id = $this->findContactID($user)) !== null) {
            $company_id = $contact_object->getAssociatedCompanyID($contact_id);
            if ($company_id !== null && $company_id !== $company->hubspotCompanyID) {
                throw new HubspotApiException('Contact company id does not match company\'s hubspot id');
            } elseif ($company_id === null) {
                $contact_object->associateCompany($contact_id, $company->hubspotCompanyID);
            }
            $hs_contact_id = $contact_id;
        }
        if ($hs_contact_id === null) {
            $contact_entity = $contact_object->create($properties);
            $hs_contact_id = $contact_entity->getID();
            $contact_object->associateCompany($hs_contact_id, $company->hubspotCompanyID);
        } else {
            $contact_object->update($hs_contact_id, $properties);
        }
        if (!$hs_id_exists) {
            $user->hubspotContactID = $hs_contact_id;
            $user->save();
        }

        // if company has a deal, update it with latest contacts
        if ($sync_with_deal && $company->hubspotDealID !== null) {
            DealSyncContactsJob::enqueue($user->companyID);
        }
    }

    /**
     * Get properties for deal object
     *
     * @param Company $company
     * @param int|null $new_company_status
     * @return array
     * @throws HubspotApiException
     */
    protected function getDealProperties(Company $company, ?int $new_company_status): array
    {
        /** @var Timezone $timezone */
        $timezone = $company->_timezone;

        $current_subscription = $company->currentSubscription ? true : false;
        if ($current_subscription) {
            $subscription = $company->currentSubscription;
            $interval = match($subscription->intervalUnit) {
                CompanySubscription::INTERVAL_UNIT_MONTH => '1',
                CompanySubscription::INTERVAL_UNIT_YEAR => '12'
            };

            $amount = BigDecimal::of($subscription->price);
            switch ($subscription->intervalUnit) {
                case CompanySubscription::INTERVAL_UNIT_MONTH:
                    $annual_amount = $amount->multipliedBy(12);
                    $monthly_amount = $amount;
                    break;
                case CompanySubscription::INTERVAL_UNIT_YEAR:
                    $annual_amount = $amount;
                    $monthly_amount = $amount->dividedBy(12, 2, RoundingMode::HALF_DOWN);
                    break;
            }
        }

        $discount_name = Registration::query()
            ->join('resellerRegistrationProfiles', 'resellerRegistrationProfiles.resellerRegistrationProfileID', '=', 'registrations.resellerRegistrationProfileID')
            ->join('companies', 'companies.registrationID', '=', 'registrations.registrationID')
            ->where('companies.companyID', $company->getKey())
            ->value('resellerRegistrationProfiles.name');

        $features = array_map(fn(IntakeFeature $feature): int => $feature->getKey(), $company->intake?->features->all() ?? []);

        $properties = [
            'dealname' => $company->name, // hubspot provided (required),

            'dealtype' => $current_subscription ? $interval : '', // hubspot provided
            'subscription_type' => $current_subscription ? $subscription->name : '', // custom
            'amount' => $current_subscription ? (string) $amount->toScale(2, RoundingMode::HALF_DOWN) : '', // hubspot provided
            'annual_amount' => $current_subscription ? (string) $annual_amount->toScale(2, RoundingMode::HALF_DOWN) : '', // custom
            'monthly_amount' => $current_subscription ? (string) $monthly_amount->toScale(2, RoundingMode::HALF_DOWN) : '', // custom
            'discount' => $discount_name ?? '', // custom

            'reseller_name' => $company->reseller->name, // custom

            // Active/1st Payment Date
            'became_a_customer_date' => $this->getDateProperty($company->firstActiveAt) ?? '', // custom

            // Close Date
            'closedate' => $this->getDateTimeProperty($company->activeAt) ?? '', // hubspot provided

            // Trial Start Date
            'registration_start_date' => $this->getDateProperty($company->trialAt) ?? '', // custom

            // Dormant Date
            'company_dormant' => $this->getDateProperty($company->dormantAt) ?? '', // custom

            'closed_won_reason_' => implode(';', $features), // custom
            'hubspot_owner_id' => $company->successManager->hubspotDealOwnerID,
            'time_zone' => $timezone->getKey() // custom
        ];
        if ($new_company_status !== null) {
            $status_deal_stage_map = Config::get('marketing.hubspot.status_deal_stage_map', []);
            $ids = $status_deal_stage_map[$new_company_status] ?? null;
            if ($ids === null) {
                throw new HubspotApiException('Unable to find deal stage properties for status: %d', $new_company_status);
            }
            [$properties['pipeline'], $properties['dealstage']] = explode(':', $ids, 2);
        }
        return $properties;
    }

    /**
     * Push deal to Hubspot
     *
     * Deals are pushed once a company reaches active status. We push it into a specific pipeline and stage for support.
     *
     * If the app company doesn't have a defined deal id from Hubspot, we create a new deal. After it's made, we
     * associate the company and any existing primary users.
     *
     * @param Company $company
     * @param int|null $new_company_status
     * @throws HubspotApiException
     */
    public function pushDeal(Company $company, ?int $new_company_status = null): void
    {
        $hs_company_id = $company->hubspotCompanyID;
        if ($hs_company_id === null) {
            throw new HubspotApiException('No Hubspot company id associated with deals\'s company');
        }

        // if status is dormant and we already cleared the deal id, we skip creating a new deal until they are active again
        if ($company->status === Company::STATUS_DORMANT && $company->hubspotDealID === null) {
            return;
        }

        $is_new_deal = $company->hubspotDealID === null;
        // if new deal, we need to ensure a pipeline and stage are assigned from company status
        if ($is_new_deal) {
            $new_company_status = $company->status;
        }

        $properties = $this->getDealProperties($company, $new_company_status);

        $deal_object = new DealObject($this);
        if ($is_new_deal) {
            $deal = $deal_object->create($properties);

            $company->hubspotDealID = $deal->getID();
            $company->save();

            $deal_object->associateCompany($deal->getID(), $hs_company_id);
            foreach ($this->getPrimaryUsersByCompanyID($company->getKey()) as $primary_users) {
                if ($primary_users->hubspotContactID === null) {
                    continue;
                }
                $deal_object->associateContact($deal->getID(), $primary_users->hubspotContactID);
            }
        } else {
            $deal_object->update($company->hubspotDealID, $properties);
            // if company went to dormant and had a deal, we clear it out so a new deal will be made if they go active again
            if ($company->status === Company::STATUS_DORMANT) {
                $company->hubspotDealID = null;
                $company->save();
            }
        }
    }

    /**
     * Sync any missing contacts in app with Hubspot deal
     *
     * @param Company $company
     * @throws HubspotApiException
     */
    public function syncDealContacts(Company $company)
    {
        $deal_object = new DealObject($this);

        $hs_deal_id = $company->hubspotDealID;
        if ($hs_deal_id === null) {
            throw new HubspotApiException('No deal id defined on company: %d', $company->getKey());
        }

        $contact_ids = $deal_object->getAssociatedContactIds($hs_deal_id);
        $primary_user_contact_ids = [];
        foreach ($this->getPrimaryUsersByCompanyID($company->getKey()) as $primary_user) {
            if ($primary_user->hubspotContactID === null) {
                continue;
            }
            $primary_user_contact_ids[] = $primary_user->hubspotContactID;
        }

        // @todo use batch api if needed
        $associate = array_diff($primary_user_contact_ids, $contact_ids);
        if (count($associate) > 0) {
            foreach ($associate as $contact_id) {
                $deal_object->associateContact($hs_deal_id, $contact_id);
            }
        }
    }
}
