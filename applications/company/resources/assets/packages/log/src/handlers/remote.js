'use strict';

const Backoff = require('backoff');
const $ = require('jquery');

const Base = require('./base');

/**
 * @memberof module:Log/Handlers
 */
class Remote extends Base {
    /**
     * Constructor
     *
     * @param {object} config
     */
    constructor(config = {}) {
        super();
        Object.assign(this.state, {
            records: {
                pending: [],
                processing: []
            },
            processing: false,
            timer: null,
            backoff: null,
            send_enabled: true,
            wait_time: 300,
            ajax_timeout: 60000,
            use_backoff: true,
            backoff_retries: 3,
            backoff_delay_min: 1500,
            backoff_delay_max: 30000,
            backoff_fail_wait_time: 20000
        }, config);
    };

    /**
     * Get pending record count
     *
     * @readonly
     *
     * @returns {number}
     */
    get pending_record_count() {
        return this.state.records.pending.length;
    };

    /**
     * Get processing record count
     *
     * @readonly
     *
     * @returns {number}
     */
    get processing_record_count() {
        return this.state.records.processing.length;
    };

    /**
     * Get total record count
     *
     * @readonly
     *
     * @returns {number}
     */
    get record_count() {
        return this.pending_record_count + this.processing_record_count;
    };

    /**
     * Add record to queue
     *
     * @param {object} record
     */
    addRecord(record) {
        this.state.records.pending.push(record);
        this.start();
    };

    /**
     * Set sending status
     *
     * Used to disable queue processing while offline for example
     *
     * @param {boolean} bool
     */
    sendEnabled(bool) {
        this.state.send_enabled = bool;
    };

    /**
     * Determines if record sending is enabled
     *
     * @returns {boolean}
     */
    isSendEnabled() {
        return this.state.send_enabled;
    };

    /**
     * Determines if queue is processing records or not
     *
     * @returns {boolean}
     */
    isProcessing() {
        return this.state.processing;
    };

    /**
     * Determines if their are pending records in the queue
     *
     * @returns {boolean}
     */
    hasPendingRecords() {
        return this.record_count > 0;
    };

    /**
     * Start processing records, setup timer to handle the queue after the configured wait time
     */
    start() {
        if (this.isProcessing() || this.pending_record_count === 0 || !this.isSendEnabled()) {
            return;
        }
        if (this.state.timer !== null) {
            clearTimeout(this.state.timer);
        }
        this.state.timer = setTimeout(() => this.startProcessing(), this.state.wait_time);
    };

    /**
     * Serialize any errors
     *
     * @param {*} key
     * @param {*} value
     * @returns {*}
     */
    replaceErrors(key, value) {
        if (value instanceof Error) {
            let error = {};
            ['name', 'code', 'message', 'fileName', 'lineNumber', 'columnNumber', 'stack'].forEach((key) => {
                let data = value[key];
                if (key === 'stack') {
                    data = typeof data === 'string' ? data.trim().split('\n') : null;
                }
                error[key] = data;
            });
            return error;
        }
        return value;
    };

    /**
     * Send records to API
     *
     * @param {Array} records - Records to send
     * @returns {Promise}
     */
    send(records) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: `${window.fx_url.API}errors`,
                method: 'POST',
                timeout: this.state.ajax_timeout,
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify({records}, this.replaceErrors)
            })
                .done(() => resolve())
                .fail(jqxhr => reject({status_code: jqxhr.status}));
        });
    };

    /**
     * Handle queue and call API
     *
     * @protected
     */
    startProcessing() {
        if (this.isProcessing()) {
            return;
        }
        this.state.processing = true;
        this.state.records.processing = this.state.records.pending;
        this.state.records.pending = [];
        this.processRecords();
    };

    /**
     * Reset processing to default state
     */
    reset() {
        this.state.timer = null;
        this.state.records.processing = [];
        this.state.backoff = null;
        this.state.processing = false;
    };

    /**
     * Send all records in processing bucket to API
     *
     * @protected
     */
    processRecords() {
        if (!this.isProcessing()) {
            return;
        }
        this.send(this.state.records.processing).then(() => {
            this.reset();
            this.start();
        }, ({status_code: code}) => {
            if (this.state.use_backoff && (code <= 0 || (code >= 500 && code < 600))) {
                if (this.state.backoff === null) {
                    this.state.backoff = Backoff.exponential({
                        randomisationFactor: 0,
                        initialDelay: this.state.backoff_delay_min,
                        maxDelay: this.state.backoff_delay_max
                    });
                    this.state.backoff.failAfter(this.state.backoff_retries);
                    this.state.backoff.on('ready', () => {
                        this.retry();
                    });
                    this.state.backoff.on('fail', () => {
                        this.reset();
                        setTimeout(() => {
                            this.start();
                        }, this.state.backoff_fail_wait_time);
                    });
                }
                this.state.backoff.backoff();
            }
        });
    };

    /**
     * Retry queue
     */
    retry() {
        this.processRecords();
    };

    /**
     * Handle to handle record
     *
     * Adds record to queue for sending to server
     *
     * @param {object} record
     */
    onHandle(record) {
        this.addRecord(record);
    };
}

module.exports = Remote;
