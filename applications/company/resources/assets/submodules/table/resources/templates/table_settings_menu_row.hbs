<div class="c-tsmbi-item{{#unless visible}} t-column-off{{/unless}}{{#if disabled}} t-disabled{{/if}}" data-js="row-item" data-id="{{id}}" data-key="{{key}}">
    <div class="c-tsmbii-move-icon">
        <svg><use xlink:href="#remix-icon--editor--draggable"></use></svg>
    </div>
    <div class="c-tsmbii-label">{{label}}</div>
    <div class="c-tsmbii-display-icon t-on" data-js="column-off">
        <svg><use xlink:href="#remix-icon--system--eye-line"></use></svg>
    </div>
    <div class="c-tsmbii-display-icon t-off" data-js="column-on">
        <svg><use xlink:href="#remix-icon--system--eye-off-line"></use></svg>
    </div>
</div>