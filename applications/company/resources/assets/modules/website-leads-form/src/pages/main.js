'use strict';

import LeadFormBuilder from "../../../company-profile/src/pages/main-pages/settings-pages/leads-pages/utilities/lead-form-builder";

import $ from 'jquery';
const Api = require('@ca-package/api');
import Page from '@ca-package/router/src/page';
const {findChild, jsSelector} = require("@ca-package/dom");
const FormValidator = require("@cas-validator-js");

import main_tpl from '@cam-website-leads-form-tpl/pages/main.hbs';
import {MAX_LENGTH, SMALL_LENGTH, MEDIUM_LENGTH} from "../../../company-profile/src/pages/main-pages/settings-pages/leads-pages/utilities/constants";
import {FIELD_NAMES} from "../../../company-profile/src/pages/main-pages/settings-pages/leads-pages/utilities/constants";
import LeadFormHelper from "../../../company-profile/src/pages/main-pages/settings-pages/leads-pages/utilities/lead-form-helper";

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            website_data: window.website_leads_form_data,
            company_colors: window.website_leads_form_data ? window.website_leads_form_data.company_colors : {},
            validator: null,
        });
        this.helper = null
    };


    /**
     * Load form
     *
     * @returns {Promise<void>}
     */
    async loadForm () {
        const { website_data } = this.state;
        const form_props = Object.assign({}, website_data.lead_form, {
            token: website_data.lead_form.token,
            fields: website_data.lead_form.fields,
            captcha_key: website_data.captcha_key,
        });

        const lead_form_builder = new LeadFormBuilder({
            form_props: form_props,
            marketing_types: website_data.marketing_types,
            project_types: website_data.project_types,
        });

        const lead = await lead_form_builder.buildForm()
        this.elem.container.html(lead);

        this.setupValidator(website_data.lead_form);
        this.setupListeners();
    }

    /**
     * Setup form validation dynamically based on form fields
     *
     * @param {Object} leadForm - The lead form data from the server
     */
    setupValidator(leadForm) {
        if (!Array.isArray(leadForm.fields)) {
            console.error('leadForm.fields is not an array:', leadForm.fields);
            return;
        }

        const validationRules = leadForm.fields.reduce((rules, field) => {
            if (field.is_enabled) {
                switch (field.reference) {
                    case 'first_name':
                    case 'last_name':
                        rules[field.reference] = {
                            required: true,
                            requiredMessage: 'This field is required',
                            maxlength: MEDIUM_LENGTH,
                        };
                        break;
                    case 'email':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
                            patternMessage: 'Please enter a valid email address',
                            requiredMessage: 'This field is required',
                            maxlength: MEDIUM_LENGTH,
                        };
                        break;
                    case 'phone':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            pattern: '^\\d{10,15}$',
                            patternMessage: 'Please enter a valid phone number (10-15 digits)',
                            requiredMessage: 'This field is required',
                        };
                        break;
                    case 'address':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            maxlength: MEDIUM_LENGTH,
                            requiredMessage: 'This field is required',
                        };

                        rules[FIELD_NAMES.CITY] = {
                            required: !!field.is_required,
                            pattern: '^[A-Za-z\\s]+$',
                            patternMessage: 'Please enter a valid city name (letters and spaces only)',
                            requiredMessage: 'This field is required',
                            maxlength: SMALL_LENGTH,
                        }

                        rules[FIELD_NAMES.STATE] = {
                            required: !!field.is_required,
                            requiredMessage: 'This field is required',
                        }

                        rules[FIELD_NAMES.POSTAL_CODE] = {
                            required: !!field.is_required,
                            pattern: '^\\d{5,9}$',
                            patternMessage: 'Please enter a valid postal code (5-9 digits)',
                            requiredMessage: 'This field is required',
                            title: 'Please enter a valid postal code (5-9 digits)',
                        }

                        break;
                    case 'marketing_source':
                    case 'project_type':
                    case 'customer_notes':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            maxlength: MAX_LENGTH,
                            requiredMessage: 'This field is required',

                        };
                        break;
                    default:
                        console.warn(`No validation rule defined for field reference: ${field.reference}`);
                        break;
                }
            }

            return rules;
        }, {});


        this.elem.form = findChild(this.elem.root, jsSelector('ca-lead-form'));
        this.elem.feedback_conainer = findChild(this.elem.root, jsSelector('feedback-container'));

        if (!this.elem.form.length) {
            console.error('Form element not found:', this.elem.root);
            return;
        }

        this.state.validator = FormValidator.create(this.elem.form, validationRules, {
            validate_event: true,
            error_event: true
        })
        .on('submit', () => {
            if (!this.state.validator.hasErrors()) {
                const event = new CustomEvent('trigger_recaptcha_execution');
                document.dispatchEvent(event)
            }
        })
        .on('error', () => {});

    }

    setupListeners() {
        this.helper.initializeFormComponents();
        this.helper.setupSubmitButtonCompanyColors(this.state.company_colors);
    }


    async save() {
        try {
            this.showLoader();

            const formDataArray = this.elem.form.serializeArray();
            const formData = formDataArray.reduce((acc, field) => {
                if (field.value.trim() !== "") {
                    acc[field.name] = field.value;
                }
                return acc;
            }, {});

            await $.ajax({
                url: this.state.website_data.lead_form.form_url,
                type: Api.Request.Method.POST,
                contentType: 'application/json',
                data: JSON.stringify(formData),
            });


            this.state.validator.reset();
            this.elem.feedback_conainer.html('<div class="alert form-alert-success">Form was submitted successfully</div>');
        } catch (e) {
            console.error(e);
            this.elem.feedback_conainer.html('<div class="alert form-alert-error">Something went wrong. Please contact us directly.</div>');
            return null;
        } finally {
            this.hideLoader();
        }
    }


    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.container = findChild(root, jsSelector('form-container'));
        this.elem.root = root
        this.helper = new LeadFormHelper(root, () => {});
    };

    async load() {
        await this.loadForm()
        document.addEventListener('recaptcha_token_set', (event) => this.save());

        $(document).ready(function() {
            let intervalId = setInterval(function() {
                let $recaptchaBadge = $('.grecaptcha-badge');
                if ($recaptchaBadge.length) {
                    $recaptchaBadge.css('visibility', 'hidden');
                    clearInterval(intervalId);
                }
            }, 50);
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl({});
    };
}