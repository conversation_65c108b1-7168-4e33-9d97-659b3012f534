<?php

declare(strict_types=1);

namespace App\Classes;

use App\Classes\Log\{AppProcessor, LineFormatter, MailHandler};
use Closure;
use Core\Classes\Arr;
use Core\StaticAccessors\{App, Config, Path};
use InvalidArgumentException;
use Monolog\Handler\{SlackWebhookHandler, StreamHandler};
use Monolog\Logger;

/**
 * Class Log
 *
 * Helper class to support easy logging via Monolog
 *
 * @package App\Classes
 */
abstract class Log
{
    /**
     * Detailed debug information
     */
    public const DEBUG = 100;

    /**
     * Interesting events
     *
     * Examples: User logs in, SQL logs.
     */
    public const INFO = 200;

    /**
     * Uncommon events
     */
    public const NOTICE = 250;

    /**
     * Exceptional occurrences that are not errors
     *
     * Examples: Use of deprecated APIs, poor use of an API,
     * undesirable things that are not necessarily wrong.
     */
    public const WARNING = 300;

    /**
     * Runtime errors
     */
    public const ERROR = 400;

    /**
     * Critical conditions
     *
     * Example: Application component unavailable, unexpected exception.
     */
    public const CRITICAL = 500;

    /**
     * Action must be taken immediately
     *
     * Example: Entire website down, database unavailable, etc.
     * This should trigger the SMS alerts and wake you up.
     */
    public const ALERT = 550;

    /**
     * Urgent alert.
     */
    public const EMERGENCY = 600;

    /**
     * Create Monolog instance with proper config and application email push handler
     *
     * @param string $logger_name
     * @param array $config
     * @return Logger
     */
    public static function create(string $logger_name, array $config = []): Logger
    {
        $log_config = Config::get('log', []);
        $slack_configs = $log_config['slack']['configs'] ?? [];
        unset($log_config['slack']['configs']);

        $config = Arr::mergeRecursiveDistinct($log_config, $config);
        $keys = [
            'email.to' => 'Email To',
            'email.from' => 'Email From',
            'email.subject' => 'Email Subject',
            'file' => 'Log File'
        ];
        foreach ($keys as $key => $label) {
            if (($value = Arr::get($config, $key, null)) === null) {
                throw new InvalidArgumentException(sprintf("%s (%s) missing from config", $label, $key));
            }
            switch ($key) {
                case 'email.to':
                case 'email.from':
                    if (filter_var($value, FILTER_VALIDATE_EMAIL) !== false) {
                        break;
                    }
                    throw new InvalidArgumentException(sprintf('%s (%s) must be a valid email', $label, $key));
                case 'file':
                    $value = Path::logs(ltrim($value, '/'));
                    Arr::set($config, $key, $value);
                    break;
            }
        }

        // if a slack config key exists, then we grab it, validate a default config exists, and merge in settings in needed
        if (isset($config['slack']['config'])) {
            $slack_config = $config['slack']['config'];
            unset($config['slack']['config']);
            if (isset($slack_configs[$slack_config])) {
                $config['slack'] = Arr::mergeRecursiveDistinct($config['slack'], $slack_configs[$slack_config]);
            }
        }

        // set default minimum logging levels
        if (!isset($config['minimum_levels'])) {
            $config['minimum_levels'] = [];
        }
        if (!isset($config['minimum_levels']['file'])) {
            $config['minimum_levels']['file'] = Logger::DEBUG;
        }
        if (!isset($config['minimum_levels']['email'])) {
            $config['minimum_levels']['email'] = Logger::ERROR;
        }
        if (!isset($config['minimum_levels']['slack'])) {
            $config['minimum_levels']['slack'] = Logger::ERROR;
        }

        // create the logger
        $logger = new Logger($logger_name);

        $stream_handler = new StreamHandler($config['file'], $config['minimum_levels']['file']);
        $disable_app_processor = isset($config['app_processor']) && $config['app_processor'] === false;
        if (!$disable_app_processor) {
            $app_processor = App::get(AppProcessor::class);
            if (isset($config['app_processor']) && $config['app_processor'] instanceof Closure) {
                $app_processor = $config['app_processor']($app_processor);
            }
            $stream_handler->pushProcessor($app_processor);
        }

        $line_formatter = new LineFormatter();
        $line_formatter->includeStacktraces();

        $stream_handler->setFormatter($line_formatter);

        // add handlers to write to log file and send email
        // log entries are written to file for any level, emails are only sent out for error
        $logger->pushHandler($stream_handler);
        if ($config['email']['enabled']) {
            $mail_handler = new MailHandler($config['minimum_levels']['email']);
            $mail_handler->setConfig($config['email']);
            $logger->pushHandler($mail_handler);
        }
        if ($config['slack']['enabled']) {
            $logger->pushHandler(new SlackWebhookHandler(
                $config['slack']['webhook_url'],
                $config['slack']['channel'],
                $config['slack']['username'],
                $config['slack']['use_attachment'],
                $config['slack']['icon_emoji'],
                $config['slack']['short_attachment'],
                $config['slack']['include_context'],
                $config['minimum_levels']['slack'],
                true,
                ['exception', 'context.exception']
            ));
        }
        return $logger;
    }

    /**
     * Get preconfigured company log which is shared
     *
     * @param string $name
     * @return Logger
     */
    public static function company(string $name = 'company'): Logger
    {
        return self::create($name, [
            'email' => [
                'subject' => 'Company'
            ],
            'slack' => [
                'config' => 'company'
            ],
            'file' => 'company.log',
            'minimum_levels' => [
                'slack' => Log::INFO
            ],
            // ignore input since it can contain sensitive data
            'app_processor' => function (Log\AppProcessor $processor) {
                return $processor->withInput(false);
            }
        ]);
    }

    /**
     * Get preconfigured support log which is shared
     *
     * @param string $name
     * @return Logger
     */
    public static function support(string $name = 'support'): Logger
    {
        return self::create($name, [
            'email' => [
                'subject' => 'Support'
            ],
            'slack' => [
                'config' => 'support'
            ],
            'file' => 'support.log',
            'minimum_levels' => [
                'slack' => Log::INFO
            ],
            // ignore input since it can contain sensitive data
            'app_processor' => function (Log\AppProcessor $processor) {
                return $processor->withInput(false);
            }
        ]);
    }

    /**
     * Simple and Basic log to main.file
     */
    public static function main(string $name = 'main'): Logger
    {
        $log_config = Config::get('log', []);
        return self::create($name, $log_config);
    }
}
