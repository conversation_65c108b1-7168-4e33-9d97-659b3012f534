<?php

namespace Core\Components\Queue\Interfaces;

use Carbon\Carbon;
use Core\Components\Queue\Exceptions\QueueException;
use Ramsey\Uuid\UuidInterface;

/**
 * Interface DriverInterface
 *
 * @package Core\Components\Queue\Interfaces
 */
interface DriverInterface
{
    /**
     * Get manager instance
     *
     * @return ManagerInterface
     */
    public function getManager(): ManagerInterface;

    /**
     * Configure driver
     *
     * @param array $config
     */
    public function configure(array $config): void;

    /**
     * Get default channel
     *
     * @return string
     */
    public function getDefaultChannel(): string;

    /**
     * Push job builder to redis
     *
     * @param JobBuilderInterface $job_builder
     */
    public function pushFromBuilder(JobBuilderInterface $job_builder): void;

    /**
     * Push job envelope onto queue
     *
     * @param JobEnvelopeInterface $job_envelope
     * @param bool $allow_delay
     */
    public function push(JobEnvelopeInterface $job_envelope, bool $allow_delay = true): void;

    /**
     * Get next job from queue to process
     *
     * @param array|null $channels
     * @return JobProcessorInterface|null
     */
    public function pop(?array $channels = null): ?JobProcessorInterface;

    /**
     * Get list of scheduled jobs for specified timestamp
     *
     * @param Carbon $timestamp
     * @return JobEnvelopeInterface[]
     */
    public function getScheduledJobs(Carbon $timestamp): array;

    /**
     * Retry all failed jobs
     *
     * @return int
     */
    public function retryAll(): int;

    /**
     * Retry specific job
     *
     * @param UuidInterface $id
     */
    public function retryOne(UuidInterface $id): void;

    /**
     * Remove all jobs for specified channel(s) or all if none specified
     *
     * @param array|null $channels
     * @throws QueueException
     */
    public function clearJobs(?array $channels = null): void;
}
