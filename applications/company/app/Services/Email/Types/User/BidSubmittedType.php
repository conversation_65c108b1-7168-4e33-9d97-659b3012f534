<?php

namespace App\Services\Email\Types\User;

use App\Classes\Acl;
use App\Classes\Template;
use App\Resources\Bid\ItemResource;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Common\Models\User;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Ramsey\Uuid\Uuid;

/**
 * Class BidSubmittedType
 *
 * @package App\Services\Email\Types\User
 */
class BidSubmittedType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        if (!isset($payload['bid_item_id'])) {
            throw new TypeException('Bid Item ID not defined in payload');
        }

        try {
            $bid_item_scope = Scope::make()
                ->field('id')
                ->with([
                    'submitted_by_user' => [
                        'fields' => ['first_name', 'last_name']
                    ],
                    'project' => [
                        'no_fields' => true,
                        'with' => [
                            'property' => [
                                'fields' => ['address', 'address_2'],
                                'with' => [
                                    'customer' => [
                                        'fields' => ['first_name', 'last_name'],
                                        'with' => [
                                            'company' => ['id']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]);
            $bid_item = ItemResource::make(Acl::make())
                ->entity($payload['bid_item_id'])
                ->scope($bid_item_scope)
                ->run();
        } catch (EntityNotFoundException $e) {
            throw new TypeException('Unable to find bid item: %s', $payload['bid_item_id']);
        }

        $company = Company::find($bid_item->get('project.property.customer.company.id'));
        if ($company === null) {
            throw new TypeException('Unable to find company');
        }

        $users = User::ofCompany($company)->anyRoles([User::ROLE_PRIMARY, User::ROLE_PROJECT_MANAGEMENT])->active()->get()->all();

        $this->setup($company, $users);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromString($bid_item['id']));

        $property_address = $bid_item->get('project.property.address');
        if (($address_2 = $bid_item->get('project.property.address_2')) !== null) {
            $property_address .= ", {$address_2}";
        }

        $message->subject("Bid Needs Approval - {$property_address}");

        $domain = $this->getDomain();
        $template_vars = [
            'submitted_by_user' => $bid_item['submitted_by_user'],
            'customer' => $bid_item->get('project.property.customer'),
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilder()->path("bids/create/{$bid_item['id']}")->build()
        ];

        $this->template->content = Template::fetch('emails.html.user.bid-submitted', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.bid-submitted', $template_vars));

        return $message;
    }
}
