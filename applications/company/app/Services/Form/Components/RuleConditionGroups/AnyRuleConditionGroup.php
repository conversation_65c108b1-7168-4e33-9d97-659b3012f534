<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionGroups;

use App\Services\Form\Classes\Structure\Group\Rule\ConditionGroup;

/**
 * Class AnyRuleConditionGroup
 *
 * @package App\Services\Form\Components\RuleConditionGroups
 */
class AnyRuleConditionGroup extends ConditionGroup
{
    /**
     * @var string Condition group type
     */
    protected string $type = 'any';
}
