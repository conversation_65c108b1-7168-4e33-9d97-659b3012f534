<?php

namespace App\Classes\FX\API;

use PDO;

class Authenticate extends Base
{
    private $userEmail;
    private $userPassword;
    private $results;

    public function setEmail($userEmail)
    {
        $this->userEmail = $userEmail;
    }

    public function setPassword($userPassword)
    {
        $this->userPassword = $userPassword;
    }

    public function authenticate()
    {
        if (!empty($this->token)) {
            $st = $this->db->prepare("SELECT userID 
                  FROM user AS u
	              WHERE token=?");
            //write parameter query to avoid sql injections
            $st->bindParam(1, $this->token);
            $st->execute();

            if ($st->rowCount() == 1) {
                while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                    $userID = $row["userID"];
                    $this->results = array('message' => 'success', 'userID' => $userID);
                }
            } else {
                $this->results = array('message' => 'Invalid Token');
            }
        } else {
            $this->results = array('message' => 'Empty Token');
        }
    }

    public function getCompanyID()
    {
        $st = $this->db->prepare("SELECT companies.companyID, user.userID 
                FROM user
                  JOIN companies ON user.companyID = companies.companyID
              WHERE user.token = :token 
                  AND companies.isActive = '1' 
                  AND user.userActive = '1'");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $companyID = $row["companyID"];
                $userID = $row["userID"];
            }
            $this->results = array('message' => 'success', 'companyID' => $companyID, 'userID' => $userID);
        } else {
            $this->results = array('message' => 'Not found');
        }
    }

    public function Login()
    {
        if (!empty($this->userEmail) && !empty($this->userPassword)) {
            $st = $this->db->prepare("SELECT userID, userActive, userFirstName, userLastName, userPassword, token, acceptUserAgreement 
                    FROM user
				        JOIN companies ON companies.companyID = user.companyID
				    WHERE userEmail = :userEmail 
				        AND companies.isActive = '1'");
            //write parameter query to avoid sql injections
            $st->bindParam(":userEmail", $this->userEmail);
            $st->execute();

            if ($st->rowCount() == 1) {
                $row = $st->fetch((PDO::FETCH_ASSOC));

                $userID = $row['userID'];
                $userActive = $row['userActive'];
                $actualPassword = $row['userPassword'];
                $token = $row['token'];
                $name = sprintf('%s %s', $row['userFirstName'], $row['userLastName']);

                $secondSt = $this->db->prepare("UPDATE user SET userLastLogin = UTC_TIMESTAMP WHERE userID = :userID");
                $secondSt->bindParam(':userID', $userID);
                $secondSt->execute();

                if ($userActive == 1) {
                    if (password_verify($this->userPassword, $actualPassword)) {
                        $this->results = array('message' => 'success',
                            'token' => $token,
                            'userID' => $userID,
                            'name' => $name);
                    } else {
                        $this->results = array('message' => 'Invalid Credentials',
                            'token' => '',
                            'userID' => '');
                    }
                } else {
                    $this->results = array('message' => 'User is inactive',
                        'token' => '',
                        'userID' => '');
                }
            } else {
                $this->results = array('message' => 'Invalid Credentials',
                    'token' => '',
                    'userID' => '');
            }
        } else {
            $this->results = array('message' => 'Empty Credentials',
                'token' => '',
                'userID' => '');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
