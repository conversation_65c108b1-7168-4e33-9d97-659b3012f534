<?php

declare(strict_types=1);

namespace App\Services\Payment\CoPilot\Api;

use App\Services\Payment\CoPilot\CoPilotCredentials;
use Core\Components\Http\Exceptions\RequestException;
use Core\StaticAccessors\Config;
use GuzzleHttp\Exception\GuzzleException;

/**
 * CoPilot API Service - Business logic for CoPilot merchant onboarding operations
 */
class CoPilotApiService
{
    private CoPilotApiHelper $apiHelper;
    private CoPilotCredentials $credentials;

    public function __construct()
    {
        $this->apiHelper = new CoPilotApiHelper();
        $this->credentials = CoPilotCredentials::fromEnv();
    }

    /**
     * Create merchant in CoPilot system
     */
    public function createMerchant(array $merchantData): array
    {
        try {
            $client = $this->apiHelper->getHttpClient($this->credentials);
            
            // Add template and sales code to merchant data
            $payload = array_merge($merchantData, [
                'templateId' => (int) Config::get('payments.copilot.application_template_id'),
                'merchant' => array_merge($merchantData['merchant'] ?? [], [
                    'salesCode' => Config::get('payments.copilot.sales_code')
                ])
            ]);

            $this->apiHelper->getLog()->info('Creating CoPilot merchant', [
                'template_id' => $payload['templateId'],
                'sales_code' => $payload['merchant']['salesCode'],
                'dba_name' => $payload['merchant']['dbaName'] ?? 'unknown'
            ]);

            $response = $client->request('POST', '/merchant', ['json' => $payload ]);
            $responseData = json_decode($response->getBody()->getContents(), true);


            if (isset($responseData['errors'])) {
                $this->apiHelper->getLog()->error('CoPilot merchant creation failed', [
                    'errors' => $responseData['errors']
                ]);
                
                return [
                    'success' => false,
                    'errors' => $responseData['errors']
                ];
            }

            $this->apiHelper->getLog()->info('CoPilot merchant created successfully', [
                'merchant_id' => $responseData['merchantId'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'merchantId' => $responseData['merchantId'] ?? null,
                'data' => $responseData
            ];

        } catch (Exception|GuzzleException $e) {
            $errorData = $this->apiHelper->handleApiException($e);
            
            return [
                'success' => false,
                'errors' => $errorData['copilot_errors'] ?? [
                    'code' => (string)$errorData['status_code'],
                    'message' => $errorData['message'],
                    'status' => 'REQUEST_FAILED'
                ]
            ];
        }
    }

    /**
     * Generate signature URL for merchant
     */
    public function generateSignature(string $merchantId): array
    {
        try {
            $client = $this->apiHelper->getHttpClient($this->credentials);

            $this->apiHelper->getLog()->info('Generating CoPilot signature', [
                'merchant_id' => $merchantId
            ]);

            $response = $client->put("/merchant/{$merchantId}/signature");
            $responseData = json_decode($response->getBody()->getContents(), true);
            
            if (isset($responseData['errors'])) {
                $this->apiHelper->getLog()->error('CoPilot signature generation failed', [
                    'merchant_id' => $merchantId,
                    'errors' => $responseData['errors']
                ]);
                
                return [
                    'success' => false,
                    'errors' => $responseData['errors']
                ];
            }

            $this->apiHelper->getLog()->info('CoPilot signature generated successfully', [
                'merchant_id' => $merchantId,
                'signature_url' => $responseData['signatureUrl'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'signatureUrl' => $responseData['signatureUrl'] ?? null,
                'data' => $responseData
            ];

        } catch (RequestException $e) {
            $errorData = $this->apiHelper->handleApiException($e);
            
            return [
                'success' => false,
                'errors' => $errorData['copilot_errors'] ?? [
                    'code' => (string)$errorData['status_code'],
                    'message' => $errorData['message'],
                    'status' => 'REQUEST_FAILED'
                ]
            ];
        }
    }

    /**
     * Get signature status for merchant
     */
    public function getSignatureStatus(string $merchantId): array
    {
        try {
            $client = $this->apiHelper->getHttpClient($this->credentials);

            $this->apiHelper->getLog()->info('Fetching CoPilot signature status', [
                'merchant_id' => $merchantId
            ]);

            $response = $client->get("/merchant/{$merchantId}/signature");
            $responseData = json_decode($response->getBody()->getContents(), true);
            
            if (isset($responseData['errors'])) {
                $this->apiHelper->getLog()->error('CoPilot signature status fetch failed', [
                    'merchant_id' => $merchantId,
                    'errors' => $responseData['errors']
                ]);
                
                return [
                    'success' => false,
                    'errors' => $responseData['errors']
                ];
            }

            $this->apiHelper->getLog()->info('CoPilot signature status fetched successfully', [
                'merchant_id' => $merchantId,
                'signature_status' => $responseData['signatureStatusCd'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'signatureStatusCd' => $responseData['signatureStatus']['signatureStatusCd'] ?? null,
                'signedDatetime' => $responseData['signatureStatus']['signedDatetime'] ?? null,
                'signatureUrl' => $responseData['signatureStatus']['signatureUrl'] ?? null,
                'data' => $responseData['signatureStatus']
            ];

        } catch (RequestException $e) {
            $errorData = $this->apiHelper->handleApiException($e);
            
            return [
                'success' => false,
                'errors' => $errorData['copilot_errors'] ?? [
                    'code' => (string)$errorData['status_code'],
                    'message' => $errorData['message'],
                    'status' => 'REQUEST_FAILED'
                ]
            ];
        }
    }

    /**
     * Get merchant status including application and gateway boarding status
     */
    public function getMerchantStatus(string $merchantId): array
    {
        try {
            $client = $this->apiHelper->getHttpClient($this->credentials);

            $this->apiHelper->getLog()->info('Fetching CoPilot merchant status', [
                'merchant_id' => $merchantId
            ]);

            $response = $client->get("/merchant/{$merchantId}/status");
            $responseData = json_decode($response->getBody()->getContents(), true);
            
            if (isset($responseData['errors'])) {
                $this->apiHelper->getLog()->error('CoPilot merchant status fetch failed', [
                    'merchant_id' => $merchantId,
                    'errors' => $responseData['errors']
                ]);
                
                return [
                    'success' => false,
                    'errors' => $responseData['errors']
                ];
            }

            $this->apiHelper->getLog()->info('CoPilot merchant status fetched successfully', [
                'merchant_id' => $merchantId,
                'application_boarding_status' => $responseData['merchantStatus']['boardingProcessStatusCd'] ?? 'unknown',
                'gateway_boarding_status' => $responseData['merchantStatus']['gatewayBoardingStatusCd'] ?? 'unknown',
                'approved_at' => $responseData['merchantStatus']['approvedDatetime'] ?? null,
                'declined_at' => $responseData['merchantStatus']['declinedDatetime'] ?? null,
            ]);

            return [
                'success' => true,
                'applicationBoardingStatus' => $responseData['merchantStatus']['boardingProcessStatusCd'] ?? null,
                'gatewayBoardingStatus' => $responseData['merchantStatus']['gatewayBoardingStatusCd'] ?? null,
                'approvedAt' => $responseData['merchantStatus']['approvedDatetime'] ?? null,
                'declinedAt' => $responseData['merchantStatus']['declinedDatetime'] ?? null,
                'data' => $responseData['merchantStatus']
            ];

        } catch (RequestException $e) {
            $errorData = $this->apiHelper->handleApiException($e);
            
            return [
                'success' => false,
                'errors' => $errorData['copilot_errors'] ?? [
                    'code' => (string)$errorData['status_code'],
                    'message' => $errorData['message'],
                    'status' => 'REQUEST_FAILED'
                ]
            ];
        }
    }
}