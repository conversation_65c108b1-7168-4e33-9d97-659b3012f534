@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/grid';
@use '~@cas-modal-sass/modal';
@use '~@cas-tooltip-sass/tooltip';

body {
  background-color: white!important;
}


.a-pic-logo, .a-p-image {
  display: none;
}

.m-website-leads-form {
  position: relative;
  @include base.full-width-height;
  padding: 0;

  .ca-form-wrapper {
    max-width: 550px;
    @include base.respond-to('<medium') {
      width: 100%;
      padding-bottom: base.unit-rem-calc(48px);
    }

    height: auto;
    margin: 0 auto;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    border-radius: base.unit-rem-calc(8px);
    background-color: #fff;
    padding: base.unit-rem-calc(24px);
  }
  .form-section {
    @include base.respond-to('>small') {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1em;
    }
  }

  .two-columns {
    @include base.respond-to('>small') {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
      gap: 1em;
    }

  }
  .three-columns {
    @include base.respond-to('>medium') {
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
      gap: 1em;
    }
  }

  .f-f-label {
    overflow: hidden;

    @include base.typo-paragraph;
    color: base.$color-grey-dark-2;
    font-weight: 500;

    .f-fl-optional {
      @include base.typo-paragraph;
      color: base.$color-grey-dark-2;
      font-style: italic;
      margin-bottom: base.unit-rem-calc(8px);
    }
  }

  .form-group {
    margin-bottom: base.unit-rem-calc(4px);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  .form-control {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: base.unit-rem-calc(4px);
  }
  .form-actions {
    margin-top: base.unit-rem-calc(16px);
    display: flex;
    flex-direction: column;

    .feedback-container {
      width: 100%;
      text-align: center;
      margin-bottom: base.unit-rem-calc(16px);

      .form-alert-success {
        @include base.callout-success;
      }
      .form-alert-error {
        @include base.callout-error;
      }
    }
    .wrapper-actions {
      display: flex;
      flex-direction: row-reverse;
    }

    .g-recaptcha {
      margin: 0 auto;
      margin-top: 1em;
    }
  }

  .form-footer {
    @include base.typo-paragraph-small;
    color: base.$color-grey-dark-1;
    text-align: center;
    margin: base.unit-rem-calc(16px) 0;
  }

  .btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .c-cr-loader {
    display: none;
    position: absolute;
    @include base.full-width-height;
    background: rgba(0, 0, 0, 0.5) url('~@cac-public/images/loading.svg') no-repeat center;
    background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
    z-index: 120;
  }

  .grecaptcha-badge {
    left: 0 !important;
    right: auto !important;
  }

  .submit-button {
    background-color: var(--button-background-color);
    border-color: var(--button-border-color);
    color: var(--button-text-color);

    &:hover {
      background-color: var(--button-hover-background-color);
      border-color: var(--button-hover-border-color);
      color: var(--button-hover-text-color);
    }
  }


}
