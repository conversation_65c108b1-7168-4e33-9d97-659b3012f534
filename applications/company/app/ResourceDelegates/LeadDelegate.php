<?php

namespace App\ResourceDelegates;

use App\NotificationJobs\User\LeadAssignmentNotificationJob;
use App\Resources\CompanyResource;
use App\Resources\CustomerResource;
use App\Resources\LeadResource;
use App\Resources\ProjectTypeResource;
use App\Resources\TaskResource;
use App\Resources\UserResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\Lead;
use Common\Models\MarketingType;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

class LeadDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('project_type')->modelRelation('projectType')->resource(ProjectTypeResource::class);
        $list->oneOrMany('working_by_user')->modelRelation('workingByUser')->resource(UserResource::class);
        $list->oneOrMany('converted_by_user')->modelRelation('convertedByUser')->resource(UserResource::class);
        $list->oneOrMany('dead_by_user')->modelRelation('deadByUser')->resource(UserResource::class);
        $list->oneOrMany('assigned_to')->modelRelation('assignedTo')->resource(UserResource::class);
        $list->oneOrMany('customer')->resource(CustomerResource::class);
        $list->oneOrMany('tasks')->resource(TaskResource::class);
        $list->oneOrMany('company')->resource(CompanyResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('leadID')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->noSave();

        $list->field('lead_uuid')
            ->typeUuid()
            ->column('leadUUID')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id');

        $list->field('status')
            ->label('Status')
            ->requireColumn()
            ->validationRules('required|type[int]|in_array[statuses]|check_status')
            ->onAction([LeadResource::ACTION_CREATE, LeadResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->disable();
            })
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('origin')
            ->label('Origin')
            ->validationRules('required|type[int]|in_array[origins]')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $status_names = LeadResource::getStatusNames();
        $list->field('status_name')
            ->onDemand()
            ->label('Status')
            ->value(function (Lead $lead) use ($status_names) {
                return $status_names[$lead->status];
            });

        $list->field('priority')
            ->requireColumn()
            ->validation('Priority', 'nullable|optional|type[int]|in_array[priorities]')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $priority_names = LeadResource::getPriorityNames();
        $list->field('priority_name')
            ->onDemand()
            ->label('Priority')
            ->value(function (Lead $lead) use ($priority_names) {
                return $priority_names[$lead->priority];
            });

        $list->field('project_type_id')
            ->typeUuid()
            ->column('projectTypeID')
            ->validation('Project Type Id', 'nullable|optional|uuid|check_project_type_id')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('project_type_name')
            ->onDemand()
            ->label('Project Type Name')
            ->query(function ($query) {
                return $query->leftJoin('projectTypes', 'projectTypes.projectTypeID', '=', 'leads.projectTypeID');
            })
            ->rawColumn('projectTypes.name', 'project_type_name')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('marketing_type_id')
            ->column('marketingTypeID')
            ->validation('Marketing Type Id', 'nullable|optional|type[int]|check_marketing_type_id')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('marketing_source')
            ->onDemand()
            ->label('Marketing Source')
            ->query(function ($query) {
                return $query->leftJoin('marketingType', 'marketingType.marketingTypeID', '=', 'leads.marketingTypeID');
            })
            ->rawColumn('marketingType.marketingTypeName', 'marketing_source')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('assigned_to_user_id')
            ->column('assignedToUserID')
            ->validation('Assigned To User Id', 'nullable|optional|type[int]|check_assigned_to_user_id')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('assigned_to_user_name')
            ->onDemand()
            ->label('Assigned To User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as assigned_to', 'assigned_to.userID', '=', 'leads.assignedToUserID');
            })
            ->rawColumn('IF(leads.assignedToUserID, CONCAT(assigned_to.userFirstName, \' \', assigned_to.userLastName), null)', 'assigned_to_user_name')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('first_name')
            ->column('firstName')
            ->label('First Name')
            ->validationRules('trim|required|max_length[50]');

        $list->field('last_name')
            ->column('lastName')
            ->label('Last Name')
            ->validationRules('trim|required|max_length|max_length[50]');

        $list->field('business_name')
            ->column('businessName')
            ->label('Business Name')
            ->validationRules('trim|nullable|optional|max_length[200]');

        $list->field('address')
            ->column('address')
            ->label('Address')
            ->validationRules('trim|nullable|optional|max_length[100]');

        $list->field('address_2')
            ->column('address2')
            ->label('Address 2')
            ->validationRules('trim|nullable|optional|max_length[100]');

        $list->field('city')
            ->column('city')
            ->label('City')
            ->validationRules('trim|nullable|optional|max_length[50]');

        $list->field('state')
            ->column('state')
            ->label('State')
            ->validationRules('trim|nullable|optional|max_length[15]');

        $list->field('zip')
            ->column('zip')
            ->label('Zip')
            ->validationRules('trim|nullable|optional|max_length[12]');

        $list->field('email')
            ->label('Email')
            ->validationRules('trim|nullable|optional|max_length[100]|email')
            ->enableAction(LeadResource::ACTION_FILTER);

        $list->field('phone_number')
            ->label('Phone Number')
            ->column('phoneNumber')
            ->validation('Number', 'trim|nullable|optional|us_phone|us_phone_format')
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('trim|required|us_phone|us_phone_format');
            });

        $list->field('notes')
            ->label('Notes')
            ->validationRules('trim|nullable|optional|max_length[50000]');

        $list->field('working_notes')
            ->column('workingNotes')
            ->label('Working Notes')
            ->validationRules('trim|nullable|optional|max_length[50000]');

        $list->field('dead_notes')
            ->column('deadNotes')
            ->label('Dead Notes')
            ->validationRules('trim|nullable|optional|max_length[50000]');

        $list->field('working_at')
            ->typeDateTime()
            ->column('workingAt')
            ->label('Working At')
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            })
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('working_by_user_id')
            ->column('workingByUserID', true)
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            });

        $list->field('working_by_user_name')
            ->onDemand()
            ->label('Working By User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as working_by', 'working_by.userID', '=', 'leads.workingByUserID');
            })
            ->rawColumn('IF(leads.workingByUserID, CONCAT(working_by.userFirstName, \' \', working_by.userLastName), null)', 'working_by_user_name')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('converted_at')
            ->typeDateTime()
            ->column('convertedAt')
            ->label('Converted At')
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            })
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('converted_by_user_id')
            ->column('convertedByUserID', true)
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            });

        $list->field('converted_by_user_name')
            ->onDemand()
            ->label('Converted By User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as converted_by', 'converted_by.userID', '=', 'leads.convertedByUserID');
            })
            ->rawColumn('IF(leads.convertedByUserID, CONCAT(converted_by.userFirstName, \' \', converted_by.userLastName), null)', 'converted_by_user_name')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('dead_at')
            ->typeDateTime()
            ->column('deadAt')
            ->label('Dead At')
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            })
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->mutable();
            });

        $list->field('dead_by_user_id')
            ->column('deadByUserID', true)
            ->immutable()
            ->onAction(LeadResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            });

        $list->field('dead_by_user_name')
            ->onDemand()
            ->label('Dead By User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as dead_by', 'dead_by.userID', '=', 'leads.deadByUserID');
            })
            ->rawColumn('IF(leads.deadByUserID, CONCAT(dead_by.userFirstName, \' \', dead_by.userLastName), null)', 'dead_by_user_name')
            ->onAction(LeadResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'status', 'priority', 'email', 'business_name', 'first_name', 'last_name', 'address', 'address_2', 'city',
            'state', 'zip', 'phone_number', 'working_at', 'converted_at', 'dead_at', 'origin'
        ], function (Field $field) {
            return $field->enableAction(LeadResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, LeadResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('company_id')->disable();
        }

        return $list;
    }

    public function validationRules(Rules $rules, LeadResource $resource)
    {
        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        $rules->register('check_assigned_to_user_id', function ($id) use ($resource) {
            if ($resource->relationResource('assigned_to')->entityExists($id)) {
                return true;
            }
            return 'check_assigned_to_user_id';
        }, [
            'check_assigned_to_user_id' => 'Unable to find assigned to user'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [LeadResource::STATUS_NEW],
                LeadResource::STATUS_NEW => [LeadResource::STATUS_WORKING, LeadResource::STATUS_CONVERTED, LeadResource::STATUS_DEAD],
                LeadResource::STATUS_WORKING => [LeadResource::STATUS_CONVERTED, LeadResource::STATUS_DEAD],
                LeadResource::STATUS_DEAD => [LeadResource::STATUS_WORKING, LeadResource::STATUS_CONVERTED],
                // once converted, status cannot be changed again
                LeadResource::STATUS_CONVERTED => []
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        $rules->register('check_marketing_type_id', function ($id) use ($resource) {
            $query = MarketingType::query();
            $user = $resource->acl()->user();
            if ($user !== null) {
                $query->where('companyID', $user->companyID);
            }
            if ($query->where('marketingTypeID', $id)->count() !== 0) {
                return true;
            }
            return 'check_marketing_type_id';
        }, [
            'check_marketing_type_id' => 'Unable to find marketing type'
        ]);

        $rules->register('check_project_type_id', function ($id) use ($resource) {
            if ($resource->relationResource('project_type')->entityExists($id)) {
                return true;
            }
            return 'check_project_type_id';
        }, [
            'check_project_type_id' => 'Unable to find project type'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', LeadResource::getStatuses());
        $config->store('priorities', LeadResource::getPriorities());
        $config->store('origins', LeadResource::getLeadOrigins());

        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        $user_id = $user !== null ? $user->userID : null;

        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }

        $model_data['leadUUID'] = Uuid::uuid4()->getBytes();
        $model_data['status'] = Lead::STATUS_NEW;

        if (isset($model_data['assignedToUserID']) && $model_data['assignedToUserID'] !== null) {
            if ($user_id === null || $model_data['assignedToUserID'] !== $user_id) {
                $request->store('send_assigned_notification', true);
            }
        }

        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        $user = $request->resource()->acl()->user();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case Lead::STATUS_NEW:
                    break;
                case Lead::STATUS_WORKING:
                    $model_data['workingAt'] = Carbon::now('UTC');
                    $model_data['workingByUserID'] = $resource->acl()->user()->getKey();
                    $model_data['deadAt'] = null;
                    $model_data['deadByUserID'] = null;
                    break;
                case Lead::STATUS_CONVERTED:
                    $model_data['convertedAt'] = Carbon::now('UTC');
                    $model_data['convertedByUserID'] = $resource->acl()->user()->getKey();
                    $model_data['deadAt'] = null;
                    $model_data['deadByUserID'] = null;
                    break;
                case Lead::STATUS_DEAD:
                    $model_data['deadAt'] = Carbon::now('UTC');
                    $model_data['deadByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        if (!isset($model_data['priority']) && $model_data['priority'] !== null) {
            $model_data['priority'] = $model->priority;
        }

        if (
            isset($model_data['assignedToUserID']) &&
            $model_data['assignedToUserID'] !== $model->assignedToUserID &&
            $model_data['assignedToUserID'] !== $user->userID
        ) {
            $request->store('send_assigned_notification', true);
        }

        return $model_data;
    }

    public function createSaveAfter(CreateRequest $request)
    {
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            LeadAssignmentNotificationJob::enqueue($model->getKey());
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            LeadAssignmentNotificationJob::enqueue($model->getKey());
        }
    }

    public function queryScopeGlobal($query, LeadResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null || $resource->getAccessLevel() === Field::ACCESS_LEVEL_PRIVATE) {
            return $query;
        }
        // @todo need to test this
        if (!$user->primary && !$user->projectManagement) {
            return $query->ofUser($user);
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    public function deleteSaveAfter(DeleteRequest $request)
    {
        $resource = $request->resource();
        $lead_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());

        $resource->relationResource('customer')->detachLead($lead_id);
        $resource->relationResource('tasks')->detachAssociation($lead_id);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param LeadResource $resource
     * @return bool
     */
    public function actionAllowed($action, LeadResource $resource)
    {
        if (in_array($action, [LeadResource::ACTION_GET_COLLECTION, LeadResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        //@todo check if user can delete a lead
        // if user is project management or sales, then we only allow them to do non-delete actions
        if (($user->projectManagement || $user->sales || $user->installation || $user->marketing || $user->metrics) && ($action & LeadResource::ACTION_GROUP_DELETE) === 0) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'lead_uuid', 'status', 'project_type_name', 'project_type_id', 'priority', 'marketing_source',
                    'assigned_to_user_id', 'assigned_to_user_name', 'converted_at', 'converted_by_user_name',
                    'working_at', 'working_by_user_name', 'dead_at', 'dead_by_user_name', 'created_at',
                    'created_by_user_name', 'email', 'business_name', 'first_name', 'last_name', 'address', 'address_2',
                    'city', 'state', 'zip', 'phone_number', 'notes', 'working_notes', 'dead_notes', 'created_by_user_id'
                ]);
                break;
            case 'collection-v2':
                $scope->fields([
                    'id', 'lead_uuid', 'status', 'origin',  'project_type_name', 'project_type_id', 'priority', 'marketing_source',
                    'assigned_to_user_id', 'assigned_to_user_name', 'converted_at', 'converted_by_user_name',
                    'working_at', 'working_by_user_name', 'dead_at', 'dead_by_user_name', 'created_at',
                    'created_by_user_name', 'email', 'business_name', 'first_name', 'last_name', 'address', 'address_2',
                    'city', 'state', 'zip', 'phone_number', 'notes', 'working_notes', 'dead_notes', 'created_by_user_id',
                    'updated_at', 'updated_by_user_name'
                ]);
                $scope->with(['customer']);
                break;
            case 'lead-v1':
                $scope->fields([
                    'id', 'lead_uuid', 'status', 'origin', 'project_type_name', 'project_type_id', 'priority', 'marketing_source',
                    'marketing_type_id', 'assigned_to_user_id', 'assigned_to_user_name', 'converted_at',
                    'converted_by_user_name', 'working_at', 'working_by_user_name', 'dead_at', 'dead_by_user_name',
                    'created_at', 'created_by_user_name', 'email', 'business_name', 'first_name', 'last_name',
                    'address', 'address_2', 'city', 'state', 'zip', 'phone_number', 'notes', 'working_notes',
                    'dead_notes'
                ]);
                $scope->with(['customer', 'tasks']);
                break;
            case 'list-v1':
                $scope->fields(['id', 'lead_uuid', 'first_name', 'last_name'], true);
                break;
            case 'export-v1':
                $scope->fields([
                    'status_name', 'priority_name', 'marketing_source', 'project_type_name', 'assigned_to_user_name', 'first_name',
                    'last_name', 'business_name', 'address', 'address_2', 'city', 'state', 'zip', 'email',
                    'phone_number', 'notes', 'working_notes', 'dead_notes', 'working_at', 'working_by_user_name',
                    'dead_at', 'dead_by_user_name', 'converted_at', 'converted_by_user_name', 'created_at',
                    'created_by_user_name'
                ], true);
                break;
        }
    }
}
