<?php

namespace App\Services\Email\Types\Customer;

use App\Classes\Template;
use App\Services\CompanySettingService;
use App\Services\Email\Addresses\UserAddress;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Traits\Type\ProjectContactsTrait;
use App\Services\Email\Traits\Type\UserTrait;
use App\Services\Email\Types\CustomerType;
use Common\Models\BidItem;
use Common\Models\CustomBid;
use Common\Models\EmailTemplate;
use Common\Models\Evaluation;
use Common\Models\EvaluationBid;
use Common\Models\User;
use Core\Components\DB\StaticAccessors\DB;
use Ramsey\Uuid\Uuid;

/**
 * Class LegacyBidViewType
 *
 * @package App\Services\Email\Types\Customer
 */
class LegacyBidViewType extends CustomerType
{
    use NotificationTrait;
    use ProjectContactsTrait;
    use UserTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        $evaluation = Evaluation::query()
            ->select(['evaluationID', 'evaluationUUID', 'evaluation.projectID', 'customEvaluation', DB::raw('IF(evaluation.bidItemID IS NOT NULL AND bidItems.type = ' . BidItem::TYPE_LEGACY . ', NULL, evaluation.bidItemID) as bidItemID'), 'evaluationCreatedByID', 'evaluationDescription'])
            ->where('evaluationUUID', $this->getNotificationItemID())
            ->leftJoin('bidItems', 'bidItems.bidItemID', '=', 'evaluation.bidItemID')
            ->first();
        if ($evaluation === null) {
            throw new TypeException('Unable to find evaluation');
        }

        // if not a custom evaluation, pull from evaluation bid
        if (empty($evaluation->customEvaluation) && empty($evaluation->bidItemID)) {
            $poly_evaluation = EvaluationBid::find($evaluation->evaluationID, ['bidID']);
        } else {
            $poly_evaluation = CustomBid::find($evaluation->evaluationID, ['bidID']);
        }

        $project = $evaluation->project;
        $property = $project->property;
        $customer = $property->customer;

        $this->setup($customer);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($evaluation->evaluationUUID));

        $setting_service = new CompanySettingService($this->company->companyID);
        $email_greeting = $setting_service->get('email_greeting', null);

        $evaluator = User::find(!empty($project->projectSalesperson) ? $project->projectSalesperson : $evaluation->evaluationCreatedByID);
        if ($evaluator === null) {
            throw new TypeException('Unable to find evaluator user');
        }
        $evaluator_phone = $evaluator->primaryPhone;

        $email_template_data = EmailTemplate::where('ownerID', $this->company->companyID)
            ->where('type', EmailTemplate::TYPE_CUSTOMER_BID)
            ->first();

        $evaluator_address = (new UserAddress())->fromModel($evaluator);
        if ($email_template_data->isSendFromSalesperson && !empty($project->projectSalesperson)) {
            $message->from($evaluator_address);
            $message->replyTo(clone $evaluator_address);
        } else {
            $message->from($this->getCompanyFromAddress());
            $message->replyTo($this->getCompanyReplyAddress());
        }

        $this->addProjectContacts($project, $message);

        $message->bcc(clone $evaluator_address);

        $property_address = $property->address;
        $property_address .= $property->address2 !== null ? ", {$property->address2}" : '';

        if (($evaluator_photo = $this->getUserImageUrl($evaluator)) !== null) {
            $evaluator_photo = '<img alt="Salesperson Photo" style="border:1px solid #151719;max-height:180px;" src="' . $evaluator_photo . '" />';
        }

        $view_bid_url = $this->newUrlBuilder()->path('view-bid.php')->query([
            'id' => $poly_evaluation->bidID
        ])->build();

        $company_color = $this->company->color;
        if ($company_color === '#000000') {
            $company_color = '#050505';
        }
        $view_bid_link = '<a href="' . $view_bid_url . '" style="color: '.$company_color.';text-decoration: underline;">View Bid</a>';

        $subject = Template::replace($email_template_data->subject, [
            'company_name' => $this->company->name,
            'address' => $property_address,
            'bid_name' => $evaluation->evaluationDescription
        ]);
        $message->subject($subject);

        $content = Template::replace($email_template_data->content, [
            'evaluatorFirstName' => $evaluator->userFirstName,
            'evaluatorLastName' => $evaluator->userLastName,
            'evaluatorBio' => $evaluator->userBio,
            'evaluatorEmail' => $evaluator->userEmail,
            'evaluatorPhone' => $evaluator_phone->phoneNumber,
            'evaluatorPicture' => $evaluator_photo,
            'viewBidLink' => $view_bid_link
        ]);

        $this->template->content = Template::fetch('emails.html.customer.legacy-bid-view', [
            'greeting' => $email_greeting ?: 'Hello',
            'first_name' => $customer->firstName,
            'content' => $content,
            'company_logo' => $this->getCompanyLogoUrl(),
            'company_color' => $company_color,
            'view_bid_link' => $view_bid_url
        ]);
        $content = $content . '<br>' .$view_bid_url;
        $message->html($this->template->render());
        $message->textFromHtml($content);

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
