<?php

declare(strict_types=1);

namespace Core\StaticAccessors;

use Core\Classes\StaticAccessor;

/**
 * Class Config
 *
 * @package Core\StaticAccessors
 *
 * @method static void merge(array $data)
 * @method static mixed get(string|null $key = null, mixed $default = null)
 * @method static void set(string $key, mixed $value)
 */
class Config extends StaticAccessor
{
    /**
     * Get instance of Config from registry
     *
     * @return \Core\Classes\Config
     */
    protected static function getInstance(): \Core\Classes\Config
    {
        return static::$kernel->get(\Core\Classes\Config::class);
    }
}
