<?php

declare(strict_types=1);

namespace App\Services\Payment\CoPilot;

use App\Classes\Acl;
use App\Resources\Payment\PropelrMerchantResource;
use App\ResourceDelegates\Payment\PropelrMerchantDelegate;
use App\Services\Payment\CoPilot\Api\CoPilotApiService;
use App\Services\Payment\ContextualPaymentLogger;
use App\Services\Payment\PaymentLogger;
use Common\Models\PropelrMerchant;
use Common\Models\User;
use Core\Components\Resource\Classes\Entity;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

/**
 * CoPilot Service - Orchestrates merchant onboarding and management operations
 */
class CoPilotService
{
    private CoPilotApiService $api_service;
    private PropelrMerchantResource $merchant_resource;
    private PropelrMerchantDelegate $merchant_delegate;
    private ?User $user;
    private ?ContextualPaymentLogger $logger = null;

    public function __construct(User $user = null)
    {
        $this->api_service = new CoPilotApiService();
        $this->merchant_resource = PropelrMerchantResource::make(Acl::make($user));
        $this->merchant_delegate = new PropelrMerchantDelegate();
        $this->user = $user;
    }

    /**
     * Get logger instance
     */
    protected function getLog(): ContextualPaymentLogger
    {
        if ($this->logger === null) {
            $this->logger = PaymentLogger::getInstance()->withContext('COPILOT_SERVICE');
        }
        return $this->logger;
    }

    /**
     * Create merchant with CoPilot integration
     *
     * @param array $merchantData The merchant data from request
     * @param string $companyUUID Company UUID
     * @param User $user The user creating the merchant
     * @return array Result with success status and merchant data
     * @throws \Exception
     */
    public function createMerchant(array $merchant_data, string $company_uuid): array
    {
        try {
            $existing_merchant = $this->merchant_resource->getByCompanyUUID($company_uuid);

            if ($existing_merchant) {
                return [
                    'success' => false,
                    'error' => 'A merchant already exists for this company.',
                    'code' => 'MERCHANT_ALREADY_EXISTS'
                ];
            }

            // Create merchant in CoPilot API
            $copilot_result = $this->api_service->createMerchant($merchant_data);

            if (!$copilot_result['success']) {
                $this->getLog()->error('CoPilot merchant creation failed', [
                    'company_uuid' => $company_uuid,
                    'errors' => $copilot_result['errors']
                ]);

                return [
                    'success' => false,
                    'error' => 'CoPilot merchant creation failed.',
                    'details' => $copilot_result['errors']
                ];
            }

            // Create merchant in local database
            $entity = Entity::make([
                'is_ready_to_process' => PropelrMerchant::IS_READY_TO_PROCESS_PENDING,
                'status' => PropelrMerchant::APPLICATION_STATUS_CREATED,
                'gateway_status' => PropelrMerchant::GATEWAY_STATUS_NOT_BOARDED,
                'company_uuid' => UUID::fromString($company_uuid)->getBytes(),
                'created_by_user_id' => $this->user ? $this->user->userID : null,
                'copilot_merchant_id' => (string) $copilot_result['merchantId'],
                'copilot_client_id' => CoPilotCredentials::fromEnv()->getClientId(),
                'template_id' => CoPilotCredentials::fromEnv()->getApplicationTemplateId(),
            ]);

            $merchant = $this->merchant_resource->create($entity)->run();

            $this->getLog()->info('CoPilot merchant created successfully', [
                'company_uuid' => $company_uuid,
                'copilot_merchant_id' => $copilot_result['merchantId'],
                'merchant_id' => $merchant->id
            ]);

            return [
                'success' => true,
                'merchantId' => $copilot_result['merchantId'],
            ];

        } catch (\Exception $e) {
            $this->getLog()->error('Error creating CoPilot merchant: ' . $e->getMessage(), [
                'company_uuid' => $company_uuid
            ]);
            throw $e;
        }
    }

    /**
     * Generate signature URL for a merchant
     *
     * @param PropelrMerchant $merchant
     * @return array Result with success status
     */
    public function generateSignature(PropelrMerchant $merchant): array
    {
        try {
            $signature_result = $this->api_service->generateSignature($merchant->copilotMerchantID);

            if ($signature_result['success']) {
                $this->merchant_delegate->transitionToPendingSignature($merchant, $signature_result['signatureUrl'], $this->merchant_resource);

                return [
                    'success' => true,
                    'signatureUrl' => $signature_result['signatureUrl']
                ];
            } else {
                $errors = $signature_result['errors'] ?? [];
                $error = is_array($errors) && !empty($errors) ? $errors[0] : [];
                $this->merchant_delegate->recordSignatureError($merchant, $error, $this->merchant_resource);

                return [
                    'success' => false,
                    'errors' => $signature_result['errors']
                ];
            }
        } catch (\Exception $e) {
            $this->getLog()->error('Failed to generate signature', [
                'merchant_id' => $merchant->propelrMerchantID,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update merchant status using delegate methods
     *
     * @param string $merchantId Merchant ID
     * @param array $statusData Status update data
     * @return PropelrMerchant Updated merchant
     * @throws \Exception
     */
    public function updateMerchantStatus(string $merchant_id, array $status_data): PropelrMerchant
    {
        $merchant = PropelrMerchant::where('propelrMerchantID', $merchant_id)
            ->whereNull('deletedAt')
            ->first();

        if (!$merchant) {
            throw new \Exception('Merchant not found');
        }

        // Use delegate for status transitions
        if (isset($status_data['applicationBoardingStatus'])) {
            $this->merchant_delegate->updateFromCoPilotStatus(
                $merchant,
                $status_data['applicationBoardingStatus'],
                $this->merchant_resource,
                $status_data['gatewayBoardingStatus'] ?? null
            );
        }

        return $merchant->fresh();
    }
}