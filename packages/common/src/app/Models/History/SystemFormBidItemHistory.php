<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\SystemFormBidItemCommon;
use Common\Models\Interfaces\SystemFormBidItemInterface;

class SystemFormBidItemHistory extends HistoryModel implements SystemFormBidItemInterface
{
    use SystemFormBidItemCommon;

    protected $table = 'systemFormBidItemsHistory';
    protected $primaryKey = 'systemFormBidItemsHistoryID';
    protected $entityPrimaryKey = 'systemFormBidItemID';
}
