/**
 * @module BidCreator/Entities/PaymentTerm
 */

'use strict';

const lang = require('lodash/lang');
const uuid4 = require('uuid/v4');
const isUuid = require('validator/lib/isUUID');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Entity = require('../../entity');
const PubSub = require('../../pubsub');

const TypeClasses = new Map;

/**
 * Base class for all payment term types, not used directly
 *
 * @memberof module:BidCreator/Entities/PaymentTerm
 */
class Base extends Entity {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super();

        Object.assign(this.state, {
            id: !lang.isNil(data.id) ? data.id : null,
            type: null,
            item_id: !lang.isNil(data.item_id) ? data.item_id : null,
            order: null,
            // only used for display purposes, not stored with entity
            title: null
        });

        // only fill certain fields since they need to be validated, we don't want users setting things like id's
        this.fill(['order'], data);
        // if we are loading an existing entity, we don't want the state to be changed which could trigger an
        // unnecessary save
        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {{ONE_TIME: number, INSTALLMENT: number}}
     */
    static get Type() {
        return {
            ONE_TIME: 1,
            INSTALLMENT: 2
        };
    };

    /**
     * Get class object by type
     *
     * @param {number} type
     * @returns {Object}
     */
    static getClassByType(type) {
        if (!TypeClasses.has(type)) {
            throw new Error(`Unable to find class for type: ${type}`);
        }
        return TypeClasses.get(type);
    };

    /**
     * Get new payment term instance by type
     *
     * @param {number} type
     */
    static getByType(type) {
        let type_class = this.getClassByType(type);
        return new type_class;
    };

    /**
     * Get proper type instance based on entity object
     *
     * @param {Object} data
     * @param {number} data.type
     * @param {boolean} [existing=false]
     */
    static getByData(data, existing = false) {
        let type = this.getClassByType(data.type);
        return new type(data, existing);
    };

    /**
     * Get type
     *
     * @readonly
     *
     * @returns {number}
     */
    get type() {
        return this.state.type;
    };

    /**
     * Get id
     *
     * If no id is set, one will be generated and returned
     *
     * @readonly
     *
     * @returns {string} - Uuid
     */
    get id() {
        if (lang.isNil(this.state.id)) {
            this.setState({
                id: uuid4()
            }, false);
        }
        return this.state.id;
    };

    /**
     * Get item id
     *
     * If no id is set, one will be generated and returned
     *
     * @readonly
     *
     * @returns {string} - Uuid
     */
    get item_id() {
        if (lang.isNil(this.state.item_id)) {
            this.setState({
                item_id: uuid4()
            }, false);
        }
        return this.state.item_id;
    };

    /**
     * Set order
     *
     * @param {number} value
     */
    set order(value) {
        // @todo validate order once it's used
        this.setState({
            order: value
        });
    };

    /**
     * Get order
     *
     * @readonly
     *
     * @returns {number}
     */
    get order() {
        return !lang.isNil(this.state.order) ? this.state.order : 1;
    };

    /**
     * Get title
     *
     * Only used for list item display, not stored with entity
     *
     * @readonly
     *
     * @returns {*}
     */
    get title() {
        return this.state.title;
    };

    /**
     * Get payload for API save request
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {Object}
     */
    getPayload(bid_item_id) {
        return {
            id: this.id,
            bid_item_id: bid_item_id,
            type: this.type,
            order: this.order
        };
    };

    /**
     * Validate payment term
     *
     * @param {Decimal} bid_total
     * @returns {Array}
     */
    validate(bid_total) {
        let errors = [];
        if (!isUuid(this.id, 4)) {
            errors.push('Invalid id');
        }
        return errors;
    };

    /**
     * Prepare payment term
     *
     * Fetch/prep any necessary data before saving
     *
     * @param {Decimal} bid_total
     * @returns {Promise<module:BidCreator/Entities/PaymentTerm.Base>}
     */
    async prepare(bid_total) {
        let errors = this.validate(bid_total);
        if (errors.length > 0) {
            throw errors;
        }
        return this;
    };

    /**
     * Save payment term
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {*}
     */
    save(bid_item_id) {
        this.setStateChanged(false);
        let request = new Api.BatchRequest.Single('bid-item-payment-term', 'poly-update-or-create', this.getPayload(bid_item_id));
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
        return request.promise;
    };

    /**
     * Delete payment term by sending API request, destroy if successful, and emit the respective events
     *
     * @returns {Promise}
     *
     * @emits module:BidCreator/Entities/PaymentTerm.Base~deleted
     */
    delete() {
        return new Promise((resolve, reject) => {
            let request = new Api.BatchRequest.Single('bid-item-payment-term', 'delete', {
                id: this.state.id
            });
            request.promise.then((data) => {
                this.events.emit('deleted');
                this.destroy();
                resolve(data);
            }, (error) => {
                console.log(error);
                reject(error);
            });
            PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, request);
        });
    };

    /**
     * Destroy payment term
     *
     * @emits module:BidCreator/Entities/PaymentTerm.Base~destroyed
     */
    destroy() {
        this.events.emit('destroyed');
    };
}

module.exports = Base;

TypeClasses.set(Base.Type.ONE_TIME, require('./one_time'));
TypeClasses.set(Base.Type.INSTALLMENT, require('./installment'));
