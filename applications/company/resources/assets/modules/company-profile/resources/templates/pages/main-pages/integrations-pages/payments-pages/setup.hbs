<div class="c-i--p-page">
    <div class="m-page-header t-section e-payments-header">
        <h4 class="c-ph-title" data-title>Payments Setup</h4>

        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="go-back-button">
                <div data-text>Cancel</div>
            </button>
            <button class="c-pha-tertiary t-text-icon t-hidden" data-js="fill-test-data-button">
                <div data-text>Fill Test Data</div>
                <svg data-icon><use xlink:href="#remix-icon--development--bug-line"></use></svg>
            </button>
            <button class="c-pha-primary" data-js="submit-setup-button">
                <div data-text>Submit</div>
            </button>
        </div>
    </div>

    <div class="section-loading" data-js="loader"></div>

    <div class="m-integrations m-integrations-payments">
        <div class="m-integrations-setup-section">
            <form class="f-payments-setup" data-js="setup-form">
                <!-- Business Info -->
                <h3 class="c-ps-title">Business Info</h3>
                <fieldset class="c-ps-group">
                    <div class="c-f-wrapper t-two-column">
                        <div class="f-field">
                            <label class="f-f-label" for="dba_name">DBA Name</label>
                            <input class="f-f-input" id="dba_name" name="dba_name" type="text" required data-js="dba-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="legal_business_name">Legal Business Name</label>
                            <input class="f-f-input" id="legal_business_name" name="legal_business_name" type="text" required data-js="legal-business-name" />
                        </div>
                    </div>

                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="tax_filing_name">Tax Filing Name</label>
                            <input class="f-f-input" id="tax_filing_name" name="tax_filing_name" type="text" required data-js="tax-filing-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="tax_filing_method">Tax Filing Method</label>
                            <select class="f-f-input" id="tax_filing_method" name="tax_filing_method" required data-js="tax-filing-method">
                                <option value="" disabled selected>--</option>
                                <option value="SSN">SSN</option>
                                <option value="EIN">EIN</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="tax_id">Tax ID</label>
                            <input class="f-f-input" id="tax_id" name="tax_id" type="text" required data-js="tax-id" />
                        </div>

                    </div>

                    <div class="c-f-wrapper t-four-column">
                        <div class="f-field">
                            <label class="f-f-label" for="ownership_type_code">Ownership Type <span class="f-fl-optional"></span></label>
                            <select class="f-f-input" id="ownership_type_code" name="ownership_type_code" data-js="ownership-type-code">
                                <option value="" disabled selected>--</option>
                                <option value="GOVT">Government</option>
                                <option value="INDIVSOLE">Sole Proprietor</option>
                                <option value="LLC">LLC</option>
                                <option value="NONPRFT">Non-Profit Org</option>
                                <option value="PRTNRSHP">Partnership</option>
                                <option value="PRIVCORP">Private Corporation</option>
                                <option value="PUBCORP">Public Corporation</option>
                                <option value="TAXEXMPT">Tax Exempt</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_start_date">Business Start Date <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="business_start_date" name="business_start_date" type="date" data-js="business-start-date" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="website_address">Website <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="website_address" name="website_address" type="url" data-js="website-address" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_phone">Business Phone Number <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="business_phone" name="business_phone" type="tel" data-js="business-phone" />
                        </div>
                    </div>
                </fieldset>

                <!-- Contact Info -->
                <h3 class="c-ps-title">Contact Info</h3>
                <fieldset class="c-ps-group">
                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="owner_first_name">First Name</label>
                            <input class="f-f-input" id="owner_first_name" name="owner_first_name" type="text" required data-js="owner-first-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_last_name">Last Name</label>
                            <input class="f-f-input" id="owner_last_name" name="owner_last_name" type="text" required data-js="owner-last-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_email">Owner Email</label>
                            <input class="f-f-input" id="owner_email" name="owner_email" type="email" required data-js="owner-email" />
                        </div>
                    </div>

                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="contact_name">Contact Name <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="contact_name" name="contact_name" type="text" data-js="contact-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="contact_email">Contact Email <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="contact_email" name="contact_email" type="email" data-js="contact-email" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="contact_phone">Contact Phone Number <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="contact_phone" name="contact_phone" type="tel" data-js="contact-phone" />
                        </div>
                    </div>
                </fieldset>

                <!-- Business Address -->
                <h3 class="c-ps-title">Business Address</h3>
                <fieldset class="c-ps-group">
                    <div class="c-f-wrapper">
                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="business_address_line1">Business Address</label>
                            <input class="f-f-input" id="business_address_line1" name="business_address_line1" type="text" required data-js="business-address-line1" />
                        </div>

                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="business_address_line2">Address 2 (Apt, Suite, Unit)</label>
                            <input class="f-f-input" id="business_address_line2" name="business_address_line2" type="text" required data-js="business-address-line2" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_city">City</label>
                            <input class="f-f-input" id="business_city" name="business_city" type="text" required data-js="business-city" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_state">State</label>
                            <select class="f-f-input" id="business_state" name="business_state" required data-js="business-state">
                                <option value="" disabled selected>--</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_zip">Zip</label>
                            <input class="f-f-input" id="business_zip" name="business_zip" type="text" required data-js="business-zip" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="business_country">Country</label>
                            <select class="f-f-input" id="business_country" name="business_country" required data-js="business-country">
                                <option value="" disabled selected>--</option>
                                <option value="US" selected>United States</option>
                            </select>
                        </div>
                    </div>

                    <!-- Add Separate Mailing Address Button -->
                    <div class="c-ps-add-section">
                        <a class="c-ps-add-button" data-js="add-mailing-address">
                            <div data-text>Add Separate Mailing Address</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </a>
                    </div>

                    <div class="c-ps-mailing-wrapper t-hidden" data-js="mailing-address-group">
                        <div class="c-ps-mailing-header">
                            <h5 class="c-ps-subtitle">Separate Mailing Address</h5>
                            <a class="c-ps-remove-button" data-js="remove-mailing-address">
                                <div data-text>Remove</div>
                                <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
                            </a>
                        </div>

                        <div class="c-f-wrapper t-top-gap">
                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="mailing_address_line1">Mailing Address</label>
                            <input class="f-f-input" id="mailing_address_line1" name="mailing_address_line1" type="text" required data-js="mailing-address-line1" />
                        </div>

                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="mailing_address_line2">Address 2 (Apt, Suite, Unit)</label>
                            <input class="f-f-input" id="mailing_address_line2" name="mailing_address_line2" type="text" data-js="mailing-address-line2" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="mailing_city">City</label>
                            <input class="f-f-input" id="mailing_city" name="mailing_city" type="text" required data-js="mailing-city" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="mailing_state">State</label>
                            <select class="f-f-input" id="mailing_state" name="mailing_state" required data-js="mailing-state">
                                <option value="" disabled selected>--</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="mailing_zip">Zip</label>
                            <input class="f-f-input" id="mailing_zip" name="mailing_zip" type="text" required data-js="mailing-zip" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="mailing_country">Country</label>
                            <select class="f-f-input" id="mailing_country" name="mailing_country" required data-js="mailing-country">
                                <option value="" disabled selected>--</option>
                                <option value="US" selected>United States</option>
                            </select>
                        </div>
                        </div> <!-- Close c-f-wrapper -->
                    </div> <!-- Close c-ps-mailing-wrapper -->
                </fieldset>

                <!-- Owner Info -->
                <h3 class="c-ps-title">Owner Info</h3>
                <fieldset class="c-ps-group">
                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="owner_name">Legal Name</label>
                            <input class="f-f-input" id="owner_name" name="owner_name" type="text" required data-js="owner-name" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_title">Owner Title</label>
                            <select class="f-f-input" id="owner_title" name="owner_title" required data-js="owner-title" disabled>
                                <option value="" disabled selected>Select ownership type first</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_ownership_pct">Ownership Percentage <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="owner_ownership_pct" name="owner_ownership_pct" type="text" data-fx-form-input="number" data-js="owner-ownership-pct" />
                        </div>
                    </div>

                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="owner_phone">Phone Number</label>
                            <input class="f-f-input" id="owner_phone" name="owner_phone" type="tel" required data-js="owner-phone" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_mobile_phone">Mobile Phone Number <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="owner_mobile_phone" name="owner_mobile_phone" type="tel" data-js="owner-mobile-phone" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_email_address">Email Address</label>
                            <input class="f-f-input" id="owner_email_address" name="owner_email_address" type="email" required data-js="owner-email-address" />
                        </div>
                    </div>

                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="owner_dob">Date of Birth</label>
                            <input class="f-f-input" id="owner_dob" name="owner_dob" type="date" data-js="owner-dob" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_ssn">SSN <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input" id="owner_ssn" type="password" name="owner_ssn" data-js="owner-ssn" maxlength="11" />
                        </div>

                    </div>

                    <div class="c-f-wrapper t-two-column">
                        <div class="f-field">
                            <label class="f-f-label" for="drivers_license_number">Driver License Number</label>
                            <input class="f-f-input" id="drivers_license_number" name="drivers_license_number" type="text" required data-js="drivers-license-number" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="drivers_license_state">Driver License State</label>
                            <select class="f-f-input" id="drivers_license_state" name="drivers_license_state" required data-js="drivers-license-state">
                                <option value="" disabled selected>--</option>
                            </select>
                        </div>
                    </div>

                    <div class="c-f-wrapper">
                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="owner_address_line1">Owner's Address</label>
                            <input class="f-f-input" id="owner_address_line1" name="owner_address_line1" type="text" required data-js="owner-address-line1" />
                        </div>

                        <div class="f-field f-span-2">
                            <label class="f-f-label" for="owner_address_line2">Address 2</label>
                            <input class="f-f-input" id="owner_address_line2" name="owner_address_line2" type="text" required data-js="owner-address-line2" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_city">City</label>
                            <input class="f-f-input" id="owner_city" name="owner_city" type="text" required data-js="owner-city" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_state">State</label>
                            <select class="f-f-input" id="owner_state" name="owner_state" required data-js="owner-state">
                                <option value="" disabled selected>--</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_zip">Zip</label>
                            <input class="f-f-input" id="owner_zip" name="owner_zip" type="text" required data-js="owner-zip" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="owner_country">Country</label>
                            <select class="f-f-input" id="owner_country" name="owner_country" required data-js="owner-country">
                                <option value="" disabled selected>--</option>
                                <option value="US" selected>United States</option>
                            </select>
                        </div>
                    </div>

                    <!-- Additional Owners Section -->
                    <div class="c-ps-additional-owners-wrapper" data-js="additional-owners-group">
                        <!-- Additional Owners Container -->
                        <div class="c-ps-additional-owners-container" data-js="additional-owners-container">
                            <!-- Additional owner entries will be added here dynamically -->
                        </div>

                        <!-- Add Additional Owner Button (inside section) -->
                        <div class="c-ps-add-additional-owner-button" data-js="add-additional-owner-entry">
                            <div data-text>Add Additional Owner</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </div>
                    </div>
                </fieldset>

                <!-- Banking Info -->
                <h3 class="c-ps-title">Banking Info</h3>
                <fieldset class="c-ps-group">
                    <h5 class="c-ps-subtitle">Deposit Information</h5>
                    <div class="c-f-wrapper t-four-column">
                        <div class="f-field">
                            <label class="f-f-label" for="deposit_account_number">Account Number</label>
                            <input class="f-f-input" id="deposit_account_number" name="deposit_account_number" type="password" required data-js="deposit-account-number" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="deposit_routing_number">Routing Number</label>
                            <input class="f-f-input" id="deposit_routing_number" name="deposit_routing_number" type="password" required data-js="deposit-routing-number" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="deposit_account_type">Account Type</label>
                            <select class="f-f-input" id="deposit_account_type" name="deposit_account_type" required data-js="deposit-account-type">
                                <option value="" disabled selected>--</option>
                                <option value="BIZ">Checking</option>
                                <option value="SAVINGS">Savings</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="deposit_bank_name">Bank Name</label>
                            <input class="f-f-input" id="deposit_bank_name" name="deposit_bank_name" type="text" required data-js="deposit-bank-name" />
                        </div>
                    </div>

                    <br>
                    <h5 class="c-ps-subtitle">Withdrawal Information</h5>
                    <div class="c-f-wrapper t-four-column">
                        <div class="f-field">
                            <label class="f-f-label" for="withdrawal_account_number">Account Number</label>
                            <input class="f-f-input" id="withdrawal_account_number" name="withdrawal_account_number" type="password" required data-js="withdrawal-account-number" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="withdrawal_routing_number">Routing Number</label>
                            <input class="f-f-input" id="withdrawal_routing_number" name="withdrawal_routing_number" type="password" required data-js="withdrawal-routing-number" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="withdrawal_account_type">Account Type</label>
                            <select class="f-f-input" id="withdrawal_account_type" name="withdrawal_account_type" required data-js="withdrawal-account-type">
                                <option value="" disabled selected>--</option>
                                <option value="BIZ">Checking</option>
                                <option value="SAVINGS">Savings</option>
                            </select>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="withdrawal_bank_name">Bank Name</label>
                            <input class="f-f-input" id="withdrawal_bank_name" name="withdrawal_bank_name" type="text" required data-js="withdrawal-bank-name" />
                        </div>
                    </div>
                </fieldset>

                <!-- Processing Info -->
                <h3 class="c-ps-title">Processing Info</h3>
                <fieldset class="c-ps-group">
                    <div class="c-f-wrapper">
                        <div class="f-field">
                            <label class="f-f-label" for="average_monthly_volume">Average Monthly Volume <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input t-currency" id="average_monthly_volume" name="average_monthly_volume" type="text" data-fx-form-input="number" data-js="average-monthly-volume" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="high_ticket_amount">High Ticket Amount <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input t-currency" id="high_ticket_amount" name="high_ticket_amount" type="text" data-fx-form-input="number" data-js="high-ticket-amount" />
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="average_ticket_amount">Average Ticket Amount <span class="f-fl-optional">(Optional)</span></label>
                            <input class="f-f-input t-currency" id="average_ticket_amount" name="average_ticket_amount" type="text" data-fx-form-input="number" data-js="average-ticket-amount" />
                        </div>
                    </div>
                </fieldset>

            </form>
        </div>
    </div>
</div>