'use strict';

const Styled = require('../styled');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Rectangle/Styled
 */
class Square extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Square.Type.SQUARE);
        Object.assign(this.state, {
            label: 'Square',
            icon: 'module--drawing--tools--square',
            node_type: Node.Entity.Type.SQUARE,
            config_panel_type: ConfigPanel.Type.RECTANGLE_COLORS_ONLY
        });
    };
}

module.exports = Square;
