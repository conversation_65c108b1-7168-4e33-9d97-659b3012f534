<?php



namespace App\Services\Wisetack\DTOs;


use App\Services\Wisetack\Exceptions\InvalidBidStatusException;
use App\Services\Wisetack\Utils\Sanitizer;
use Common\Models\BidItem;
use Common\Models\Company;
use Common\Models\Customer;
use Common\Models\Interfaces\BidItemInterface;
use Common\Models\Project;
use Common\Models\WisetackTransaction;
use Core\Components\Http\StaticAccessors\URI;
use Illuminate\Support\Carbon;
use Ramsey\Uuid\Uuid;

class TransactionDTO
{

    private BidItem $bid;

    private string $merchantId;

    private Company $company;

    private Customer $customer;


    private string $appSource;

    private ?float $amount = null;

    private Project $project;

    private string $serviceCompletedOn;

    const MAX_ALLOWED_FOR_LOAN = 25000.00;

    const MIN_ALLOWED_FOR_LOAN = 500.00;

    /**
     * TransactionDTO constructor.
     *
     * @param array $data
     * @throws InvalidBidStatusException
     */
    public function __construct(array $data)
    {
        $this->customer = $data['customer'];
        $this->company = $data['company'];
        $this->project = $data['project'];
        $this->bid = $data['bid'];
        $this->merchantId = $data['merchantId'];
        $this->appSource = $data['appSource'];

        if ($data['amount']) {
            $this->amount = floatval($data['amount']);
        }

        if (!$this->isBidStatusValid()) {
            throw new InvalidBidStatusException();
        }

        $this->setServiceCompletedOn();
    }

    /**
     * Sets the serviceCompletedOn property based on the bid status.
     *
     * @return void
     * @throws InvalidBidStatusException
     */
    private function setServiceCompletedOn(): void
    {
        if ($this->bid->status === BidItemInterface::STATUS_ACCEPTED) {
            $this->serviceCompletedOn = Carbon::parse($this->bid->acceptedAt)->format('Y-m-d');
        } elseif ($this->bid->status === BidItemInterface::STATUS_FINALIZED) {
            $this->serviceCompletedOn = Carbon::parse($this->bid->finalizedAt)->format('Y-m-d');
        } else {
            throw new InvalidBidStatusException();
        }
    }

    /**
     * Checks if the bid STATUS is valid for creating a transaction.
    * @return bool
     */
    private function isBidStatusValid(): bool
    {
        return $this->bid->status == BidItemInterface::STATUS_FINALIZED || $this->bid->status == BidItem::STATUS_ACCEPTED;
    }

    public function getMerchantId(): string
    {
        return $this->merchantId;
    }

    public function getBidId()
    {
        return $this->bid->bidItemID;
    }

    public function getWebhookUrl()
    {
        return URI::route('wisetack.webhooks.transactions')->build();
    }

    private function getCustomerPhone(): string
    {
        return $this->customer->primaryPhone->phoneNumber
            ? "+1" . preg_replace('/\D+/', '', $this->customer->primaryPhone->phoneNumber)
            : '';
    }

    public function getBidAmount(): Float
    {
        $bid_amount = $this->amount ?? $this->bid->total;

        switch (true) {
            case $bid_amount < self::MIN_ALLOWED_FOR_LOAN:
                return self::MIN_ALLOWED_FOR_LOAN;
            case $bid_amount > self::MAX_ALLOWED_FOR_LOAN:
                return self::MAX_ALLOWED_FOR_LOAN;
            default:
                return $bid_amount;
        }
    }

    public function getTransactionPurpose(): string
    {
        $salesperson = $this->project->salesperson()->first();
        if (!$salesperson) {
            $bid_creator_user = $this->bid->createdByUser()->first();
            return $bid_creator_user ? $bid_creator_user->fullName() : '';
        }
        return $salesperson->fullName();
    }

    public function getAppSource(): string
    {
        return $this->appSource;
    }

    public function generateCreateTransactionData(): array
    {
        $first_name = Sanitizer::sanitize($this->customer->firstName);
        $last_name = Sanitizer::sanitize($this->customer->lastName);
        $transaction_purpose = Sanitizer::sanitize($this->getTransactionPurpose());

        return [
            "callbackURL" => $this->getWebhookUrl(),
            "transactionAmount" => $this->getBidAmount(),
            "mobileNumber"=> $this->getCustomerPhone(),
            "serviceCompletedOn" => $this->serviceCompletedOn,
            "email" => $this->customer->email,
            "appSource" => $this->appSource,
            "firstName" => $first_name,
            "lastName" => $last_name,
            "transactionPurpose" => $transaction_purpose,
        ];
    }

}