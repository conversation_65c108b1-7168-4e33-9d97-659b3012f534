<?php

declare(strict_types=1);

namespace App\ResourceJobs\Company;

use App\Attributes\JobAttribute;
use App\Classes\Acl;
use App\Classes\SupportList;
use App\Resources\CompanyResource;
use Common\Models\Company;
use Common\Models\Reseller;
use Common\Models\SuccessManager;
use Common\Models\Timezone;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Throwable;

/**
 * Class SupportListPushJob
 *
 * @package App\ResourceJobs\User
 */
#[JobAttribute(type: 44)]
class SupportListPushJob extends Job
{
    /**
     * SupportListPushJob constructor
     *
     * @param int $company_id
     */
    public function __construct(protected int $company_id)
    {}

    /**
     * Handle job
     *
     * Send company data to Zendesk
     *
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        $company_resource = CompanyResource::make(Acl::make());

        try {
            $company_scope = Scope::make()
                ->fields([
                    'name', 'website', 'success_manager_id', 'status', 'created_at', 'trial_at', 'active_at',
                        'suspended_at', 'dormant_at', 'reseller_id', 'timezone_id', 'zendesk_organization_id'
                ])
                ->with([
                    'current_subscription' => [
                        'fields' => ['name']
                    ]
                ]);
            $company = $company_resource
                ->entity($this->company_id)
                ->scope($company_scope)
                ->run();
        } catch (EntityNotFoundException $e) {
            throw new JobFailedException('Unable to find company: %s', $this->company_id);
        }

        try {
            $support_list = new SupportList();
            if (!$support_list->isEnabled()) {
                return;
            }
            $success_manager = SuccessManager::find($company->success_manager_id);
            $reseller = Reseller::find($company->reseller_id);
            $timezone = Timezone::find($company->timezone_id);

            $status_map = [
                CompanyResource::STATUS_SIGNUP => 'created_at',
                CompanyResource::STATUS_TRIAL => 'trial_at',
                CompanyResource::STATUS_ACTIVE => 'active_at',
                CompanyResource::STATUS_SUSPENDED => 'suspended_at',
                CompanyResource::STATUS_DORMANT => 'dormant_at',
            ];
            $company_status_name = [
                Company::STATUS_SIGNUP => 'setup',
                Company::STATUS_TRIAL => 'trial',
                Company::STATUS_ACTIVE => 'active',
                Company::STATUS_SUSPENDED => 'suspended',
                Company::STATUS_DORMANT => 'dormant'
            ];

            $subscription_name = '';
            $subscription_name_lookup = ['Starter', 'Growth', 'Professional', 'Enterprise'];
            if ($company->current_subscription !== null) {
                $subscription_name = in_array($company->current_subscription->name, $subscription_name_lookup) ? $company->current_subscription->name : 'Legacy';
            }

            $website = '';
            if ($company->website !== null) {
                $website = strpos($company->website, '/') === false ? $company->website : '';
            }

            $data = [
                'name' => "{$company->name} ({$this->company_id})",
                'group_id' => $success_manager->zendeskGroupName,
                'domain_names' => $website,
                'organization_fields' => [
                    'company_id' => $this->company_id,
                    'status' => $company_status_name[$company->status],
                    'last_status_change_date' => $company[$status_map[$company->status]],
                    'subscription_level' => $subscription_name,
                    'reseller' => strtolower(str_replace(' ', '_', $reseller->name)),
                    'timezone' => $timezone->label,
                ]
            ];
            if ($company->zendesk_organization_id !== null) {
                $support_list->updateOrganization($company->zendesk_organization_id, $data);
                return;
            }
            $zendesk_result = $support_list->createOrganization($data);
            $company_resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->partialUpdate(Entity::make([
                'id' => $this->company_id,
                'zendesk_organization_id' => $zendesk_result->organization->id
            ]))->run();
        } catch (Throwable $e) {
            throw (new JobFailedException('Unable to push to support list'))->setLastException($e);
        }
    }
}
