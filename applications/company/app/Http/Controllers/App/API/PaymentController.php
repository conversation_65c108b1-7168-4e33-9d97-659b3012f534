<?php

namespace App\Http\Controllers\App\API;

use App\Classes\Acl;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\ContextualPaymentLogger;
use App\Exceptions\Api\UnprocessableEntityException;
use App\Resources\Payment\PropelrMerchantResource;
use App\Services\Payment\CoPilot\CoPilotService;
use App\Services\Payment\Exceptions\DuplicatePaymentException;
use App\Services\Payment\Exceptions\GatewayTimeoutException;
use App\Services\Payment\Exceptions\PaymentDeclinedException;
use App\Services\Payment\Propelr\Handlers\PaymentCheckoutHandler;
use App\Services\Payment\Propelr\Handlers\PaymentProcessingHandler;
use App\Services\Payment\PropelrPaymentService;
use App\Traits\Resource\Controller\ActionTrait;
use Common\Models\PropelrMerchant;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Validation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validator;
use Monolog\Logger;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

class PaymentController
{
    use ActionTrait {
        retrieve as actionRetrieve;
    }

    protected ?ContextualPaymentLogger $logger = null;

    /**
     * Get logger
     *
     * @return ContextualPaymentLogger
     */
    protected function getLog(): ContextualPaymentLogger
    {
        if ($this->logger === null) {
            $this->logger = PaymentLogger::getInstance()->withContext('CONTROLLER');
        }
        return $this->logger;
    }

    /**
     * Render the checkout page using payment link token
     *
     * @param RequestInterface $request
     * @param string $shortToken
     * @return mixed
     * @throws HttpResponseException
     */
    public function showCheckoutPage(RequestInterface $request, string $shortToken)
    {
        try {
            $checkout_handler = new PaymentCheckoutHandler();
            $payment_data = $checkout_handler->prepareCheckoutData($request, $shortToken);

            return Response::view('pages.payment', ['script_data' => $payment_data])
                ->share('include_layout', false)
                ->share('layout-header', false);

        } catch (HttpResponseException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getLog()->error('Error loading checkout page: ' . $e->getMessage());
            throw new HttpResponseException(500, 'An error occurred while loading the checkout page.');
        }
    }

    /**
     * Process payment using payment link token
     *
     * @param RequestInterface $request
     * @param string $shortToken
     * @return JSONResponse
     */
    public function chargeViaPaymentLink(RequestInterface $request, string $shortToken): JSONResponse
    {
        try {
            $input = $request->input()->all();
            $processing_handler = new PaymentProcessingHandler();
            
            $payment_request = $processing_handler->preparePaymentRequest($input, $shortToken);
            $response_data = $processing_handler->processPaymentAndGetResponse($payment_request);

            $this->getLog()->info(
                'Payment processed successfully via payment link',
                $response_data['logging_context']
            );

            // Remove logging context from response
            unset($response_data['logging_context']);
            return Response::json($response_data, 201);

        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (DuplicatePaymentException $e) {
            $this->getLog()->warning('Duplicate payment attempt blocked: ' . $e->getMessage(), [
                'wait_minutes' => $e->getWaitMinutes(),
                'recent_transaction' => $e->getRecentTransaction()
            ]);
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (GatewayTimeoutException $e) {
            $this->getLog()->warning('Payment gateway timeout: ' . $e->getMessage());
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (PaymentDeclinedException $e) {
            $this->getLog()->error('Payment declined/retryable via payment link: ' . $e->getMessage(), $e->getGatewayDetails());
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (\Exception $e) {
            $this->getLog()->error('Payment processing failed via payment link: ' . $e->getMessage());
            return Response::json(['error' => 'Payment processing failed.'], 500);
        }
    }


    /**
     * Get payment settings for the authenticated user's company
     *
     * @return JSONResponse
     * @throws HttpResponseException
     */
    public function getSettings(): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new HttpResponseException(401, 'User not authenticated.');
            }

            $company = $user->company;
            $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)
                ->whereNull('deletedAt')
                ->first();

            if (!$merchant) {
                return Response::json(['error' => 'No merchant found.'], 404);
            }

            $payment_service = new PropelrPaymentService();
            $payment_data = $payment_service->getApiPaymentData($company, $merchant);

            return Response::json($payment_data);
        } catch (\Exception $e) {
            $this->getLog()->error('Error fetching payment settings: ' . $e->getMessage());
            return Response::json(['error' => 'An error occurred while fetching payment settings.'], 500);
        }
    }

    /**
     * Create CoPilot merchant for onboarding
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function createCoPilotMerchant(RequestInterface $request): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new HttpResponseException(401, 'User not authenticated.');
            }

            $input = $request->input()->all();
            $validator = $this->validateCoPilotMerchantData($input);

            // todo - filipe - Improve this error handling to provide more specific feedback to users about the nature
            // of what's wrong with the data they provided.
            // Currently the errors are being collected in the ValidationException thrown below.

            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $company = $user->company;
            $copilotService = new CoPilotService($user);
            $company_uuid = UUID::fromBytes($company->companyUUID);
            $result = $copilotService->createMerchant($input, $company_uuid, $user);

            if (!$result['success']) {
                $statusCode = ($result['code'] ?? null) === 'MERCHANT_ALREADY_EXISTS' ? 409 : 400;
                
                return Response::json([
                    'error' => $result['error'],
                    'code' => $result['code'] ?? null,
                    'details' => $result['details'] ?? null
                ], $statusCode);
            }

            return Response::json([
                'message' => 'CoPilot merchant created successfully.',
                'merchantID' => $result['merchantId'],
            ], 201);

        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (\Exception $e) {
            $this->getLog()->error('Error creating CoPilot merchant: ' . $e->getMessage());
            return Response::json(['error' => 'An error occurred while creating CoPilot merchant.'], 500);
        }
    }


    /**
     * Validate CoPilot merchant data
     *
     * @param array $input
     * @return Validator
     */
    private function validateCoPilotMerchantData(array $input): Validator
    {
        $config = FieldConfig::fromArray([
            'merchant' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.dbaName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.legalBusinessName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.taxFilingName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.taxFilingMethod' => [
                'rules' => 'trim|required|in_array[tax_filing_methods]'
            ],
            'merchant.businessStartDate' => [
                'rules' => 'optional|to_carbon'
            ],
            'merchant.demographic' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.demographic.websiteAddress' => [
                'rules' => 'trim|optional|max_length[255]'
            ],
            'merchant.demographic.businessPhone' => [
                'rules' => 'optional|us_phone_copilot'
            ],
            'merchant.demographic.businessAddress' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.demographic.businessAddress.address1' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.demographic.businessAddress.city' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'merchant.demographic.businessAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.demographic.businessAddress.zip' => [
                'rules' => 'trim|required|max_length[10]'
            ],
            'merchant.demographic.businessAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.demographic.mailingAddress' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.demographic.mailingAddress.address1' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.demographic.mailingAddress.city' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'merchant.demographic.mailingAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.demographic.mailingAddress.zip' => [
                'rules' => 'trim|required|max_length[10]'
            ],
            'merchant.demographic.mailingAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.ownership' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.ownership.owner' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.ownership.owner.ownerName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.ownership.owner.ownerEmail' => [
                'rules' => 'trim|required|email|max_length[100]'
            ],
            'merchant.ownership.owner.ownerPhone' => [
                'rules' => 'trim|required|us_phone_copilot|max_length[20]'
            ],
            'merchant.ownership.owner.ownerTitle' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'merchant.ownership.owner.ownerAddress' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.ownership.owner.ownerAddress.address1' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.ownership.owner.ownerAddress.city' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'merchant.ownership.owner.ownerAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.ownership.owner.ownerAddress.zip' => [
                'rules' => 'trim|required|max_length[10]'
            ],
            'merchant.ownership.owner.ownerAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.ownership.driversLicenseNumber' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'merchant.ownership.driversLicenseStateCd' => [
                'rules' => 'trim|required|max_length[2]'
            ],
            'merchant.ownership.ownershipTypeCd' => [
                'rules' => 'trim|optional|in_array[ownership_types]'
            ],
            'merchant.ownership.ownerOwnershipPct' => [
                'rules' => 'optional|numeric|greater_than_equal[0]|less_than_equal[100]'
            ],
            // todo - filipe this must be required.
            'merchant.ownership.owner.ownerDob' => [
                'rules' => 'optional|to_carbon'
            ],
            'merchant.ownership.owner.ownerSSN' => [
                'rules' => 'optional|us_ssn|us_ssn_format'
            ],
            'merchant.ownership.owner.ownerMobilePhone' => [
                'rules' => 'nullable|us_phone_copilot|us_phone_format'
            ],
            'merchant.merchantContactInfo' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.merchantContactInfo.contactName' => [
                'rules' => 'trim|optional|max_length[100]'
            ],
            'merchant.merchantContactInfo.contactEmail' => [
                'rules' => 'trim|optional|email|max_length[100]'
            ],
            'merchant.merchantContactInfo.contactPhone' => [
                'rules' => 'optional|us_phone_copilot|us_phone_format'
            ],
            'merchant.processing' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.processing.volumeDetails' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.processing.volumeDetails.averageMonthlyVolume' => [
                'rules' => 'optional|numeric|greater_than_equal[0]'
            ],
            'merchant.processing.volumeDetails.highTicketAmount' => [
                'rules' => 'optional|numeric|greater_than_equal[0]'
            ],
            'merchant.processing.volumeDetails.averageTicketAmount' => [
                'rules' => 'optional|numeric|greater_than_equal[0]'
            ],
            'merchant.processing.platformDetails.taxId' => [
                'rules' => 'trim|required|type[string]|exact_length[9]|numeric',
            ],
            'merchant.bankDetail' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.bankDetail.depositBank' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.bankDetail.depositBank.bankAcctNum' => [
                'rules' => 'trim|required|max_length[30]'
            ],
            'merchant.bankDetail.depositBank.bankRoutingNum' => [
                'rules' => 'required|aba_routing_transit_number'
            ],
            'merchant.bankDetail.depositBank.bankAcctTypeCd' => [
                'rules' => 'trim|required|in_array[bank_account_types]'
            ],
            'merchant.bankDetail.depositBank.bankName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'merchant.bankDetail.withdrawalBank' => [
                'rules' => 'required|type[array]'
            ],
            'merchant.bankDetail.withdrawalBank.bankAcctNum' => [
                'rules' => 'trim|required|max_length[30]'
            ],
            'merchant.bankDetail.withdrawalBank.bankRoutingNum' => [
                'rules' => 'required|aba_routing_transit_number'
            ],
            'merchant.bankDetail.withdrawalBank.bankAcctTypeCd' => [
                'rules' => 'trim|required|in_array[bank_account_types]'
            ],
            'merchant.bankDetail.withdrawalBank.bankName' => [
                'rules' => 'trim|required|max_length[100]'
            ],
            'ownerSiteUser' => [
                'rules' => 'required|type[array]'
            ],
            'ownerSiteUser.firstName' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'ownerSiteUser.lastName' => [
                'rules' => 'trim|required|max_length[50]'
            ],
            'ownerSiteUser.email' => [
                'rules' => 'trim|required|email|max_length[100]'
            ]
        ]);

        // Store validation arrays for in_array rules
        $config->store('tax_filing_methods', PropelrMerchant::getTaxFilingMethods());
        $config->store('ownership_types', PropelrMerchant::getOwnershipTypes());
        $config->store('bank_account_types', PropelrMerchant::getBankAccountTypes());

        $validation = Validation::make()->config($config);
        return $validation->run($input);
    }

    private function logAndRespondWithErrors($e): JSONResponse
    {
        $this->getLog()->error($e->getMessage(), $e instanceof ValidationException ? $e->getErrors() : []);
        $errors = $e instanceof UnprocessableEntityException ? $e->getResponse()->getContent() : $e->getErrors();

        return Response::json([
            'error' => $e->getMessage(),
            'code' => 'VALIDATION_FAILED',
            'errors' => $errors
        ], 400);
    }
}
