<?php

namespace App\ResourceDelegates\Bid\Item;

use App\Resources\Bid\Item\OneTimePaymentTermResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class OneTimePaymentTermDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('bidItemOneTimePaymentTermID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([OneTimePaymentTermResource::ACTION_CREATE, OneTimePaymentTermResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('due_time_frame')
            ->column('dueTimeFrame')
            ->validation('Due Time Frame', 'required|type[int]|in_array[due_time_frames]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, OneTimePaymentTermResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('due_time_frames', OneTimePaymentTermResource::getDueTimeFrames());
        return $config;
    }

    public function queryScopeGlobal($query, OneTimePaymentTermResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
            case 'detail-v1':
                $scope->fields(['id', 'due_time_frame'], true);
                break;
        }
    }
}
