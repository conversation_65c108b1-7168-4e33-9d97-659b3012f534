<?php

declare(strict_types=1);

namespace App\Services\Lead;

use App\Classes\Acl;
use App\Resources\LeadFormFieldResource;
use App\Resources\LeadFormResource;
use App\Resources\LeadResource;
use Common\Models\LeadForm;
use Common\Models\ProjectType;
use Common\Models\MarketingType;
use Common\Models\User;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Exception;
use Ramsey\Uuid\Uuid;
use GuzzleHttp\Client;

class LeadFormService
{

    public static function make()
    {
        return new static();
    }

    public function __construct()
    {
    }

    /**
     * Get marketing and project types for the company.
     *
     * @return array|JSONResponse
     */
    public function getCompanyMarketingInfo($companyID)
    {
        $project_types = ProjectType::where('companyID', $companyID)
            ->where('status', ProjectType::STATUS_ACTIVE)
            ->orderBy('name', 'asc')
            ->get();

        $marketing_types = MarketingType::where('companyID', $companyID)
            ->whereNull('isDeleted')
            ->orderBy('parentMarketingTypeID', 'asc')
            ->get();

        $marketing = [];
        foreach ($marketing_types as $type) {
            if ($type->parentMarketingTypeID === null) {
                $marketing[$type->marketingTypeID] = [
                    'label' => $type->marketingTypeName,
                    'options' => []
                ];
            } else {
                if (isset($marketing[$type->parentMarketingTypeID])) {
                    $marketing[$type->parentMarketingTypeID]['options'][$type->marketingTypeID] = $type->marketingTypeName;
                }
            }
        }

        $project = [];
        foreach ($project_types as $type) {
            $id = strtoupper(bin2hex($type->projectTypeID));
            $project[$id] = $type->name;
        }

        $marketing = array_values($marketing);
        $project = array_values([['label' => 'Project Types', 'options' => $project]]);

        return [
            'marketing_types' => $marketing,
            'project_types' => $project
        ];
    }


    /**
     * Activate a lead form for the company.
     * If a lead form already doesn't exist, create a new one.
     * If a lead form already exists, activate it.
     *
     * @throws Exception
     */
    public function activate()
    {
        $user = Auth::user();
        $acl = Auth::acl();
        if (!$user) {
            throw new Exception('User not found.');
        }

        $existing_lead_form = LeadForm::where('companyID', $user->companyID)
            ->first();

        // Activate existing lead form if it exists
        if ($existing_lead_form && !$existing_lead_form->isActive) {
            $form = Entity::make([
                'id' => Uuid::fromBytes($existing_lead_form->leadFormID)->toString(),
                'is_active' => true,
                'title' => $existing_lead_form->title,
                'save_button_label' => $existing_lead_form->saveButtonLabel,
                'default_assigned_to_user_id' => $existing_lead_form->defaultAssignedToUserID,
            ]);

            return LeadFormResource::make($acl)->partialUpdate($form)->run();
        }

        $uuid = Uuid::uuid4();
        $form = Entity::make([
            'id' => $uuid->toString(),
            'token' => Uuid::uuid4()->toString(),
            'company_id' => $user->companyID,
            'is_active' => true,
            'title' => LeadFormResource::LABEL_DEFAULT_FORM_TITLE,
            'save_button_label' => LeadFormResource::LABEL_DEFAULT_SAVE_BUTTON,
        ]);

        $batch = BatchRequest::make()->sequential();
        $lead_form = LeadFormResource::make($acl)->create($form);
        $batch->add($lead_form);

        $field_data = $this->prepareLeadFormFields($uuid->toString());

        foreach ($field_data as $data) {
            $field = Entity::make($data);
            $batch->add(LeadFormFieldResource::make($acl)->create($field));
        }

        $batch->run();
        return $lead_form->response();
    }

    /**
     * Create a new lead from the input data.
     *
     * @param LeadForm $lead_form
     * @param $input
     * @return mixed
     */
    public function createLead($lead_form, $input)
    {
        $payload = [
            'company_id' => $lead_form->companyID,
            'first_name' => $input['first_name'] ?? null,
            'last_name' => $input['last_name'] ?? null,
            'email' => $input['email'] ?? null,
            'phone_number' => $input['phone'] ?? null,
            'address' => $input['address'] ?? null,
            'city' => $input['city'] ?? null,
            'state' => $input['state'] ?? null,
            'zip' => $input['postal_code'] ?? null,
            'origin' => LeadResource::ORIGIN_WEBSITE_LEADS_FORM,
            'status' => LeadResource::STATUS_NEW,
        ];

        $array = $this->parseMarketingFields($lead_form, $input);
        $payload = array_merge($payload, $array);

        $payload['notes'] = $this->parseCustomerNotes($lead_form, $input);
        $payload['assigned_to_user_id'] = $this->getValidDefaultAssignedUserId($lead_form);

        $lead = Entity::make($payload);
        $acl = Acl::make()->setCompanyID($lead_form->companyID);
        $lead_form = LeadResource::make($acl)->create($lead)->run();
        return $lead_form;
    }

    /**
     * Handles the processing of specific fields (marketing_source and project_type).
     *
     * @param LeadForm $lead_form
     * @param array $input
     * @return array
     */
    private function parseMarketingFields(LeadForm $lead_form, array $input): array
    {
        $data = [];
        $field_keys = [
            LeadFormFieldResource::REFERENCE_MARKETING_SOURCE => 'marketingTypeID',
            LeadFormFieldResource::REFERENCE_PROJECT_TYPE => 'projectTypeID',
        ];

        $fields = $lead_form->fields()
            ->whereIn('reference', array_keys($field_keys))
            ->where('isEnabled', true)
            ->get();

        foreach ($fields as $field) {
            $reference = $field->reference;
            $value = trim($input[$reference] ?? '');

            if (!empty($value)) {
                if ($field->fieldType === LeadFormFieldResource::TYPE_DROPDOWN) {
                    if ($reference === LeadFormFieldResource::REFERENCE_MARKETING_SOURCE) {
                        $data['marketing_type_id'] = (int) $value;
                    } elseif ($reference === LeadFormFieldResource::REFERENCE_PROJECT_TYPE) {
                        $data['project_type_id'] = $value;
                    }
                }
            }
        }

        return $data;
    }

    /**
     * Parses customer notes from the lead form input.
     *
     * @param LeadForm $leadForm The lead form instance.
     * @param array $input The input data from the form submission.
     * @return string|null The parsed customer notes.
     */
    private function parseCustomerNotes(LeadForm $leadForm, array $input): ?string
    {
        $notes = [];

        $references_to_include = [
            LeadFormFieldResource::REFERENCE_MARKETING_SOURCE,
            LeadFormFieldResource::REFERENCE_PROJECT_TYPE,
            LeadFormFieldResource::REFERENCE_CUSTOMER_NOTES,
        ];

        $fields = $leadForm->fields()
            ->whereIn('reference', $references_to_include)
            ->where('isEnabled', true)
            ->get();

        foreach ($fields as $field) {
            $reference = $field->reference;
            $value = trim($input[$reference] ?? '');

            if ($reference === LeadFormFieldResource::REFERENCE_CUSTOMER_NOTES) {
                if (!empty($value)) {
                    $notes[] = "<strong>{$field->label}</strong>: {$value}";
                }
            } else {
                if ($field->fieldType === LeadFormFieldResource::TYPE_FREEFORM && !empty($value)) {
                    $notes[] = "<strong>{$field->label}</strong>: {$value}";
                }
            }
        }

        $notes = implode("<br>", $notes);
        return $notes === '' ? null : $notes;
    }

    /**
     * Retrieves the valid default assigned user ID for a given lead form.
     *
     * @param LeadForm $leadForm The lead form instance.
     *
     * @return int|null The user ID if a valid user is found; otherwise, null.
     */
    private function getValidDefaultAssignedUserId(LeadForm $leadForm): ?int
    {
        $default_assigned_to = $leadForm->defaultAssignedToUserID;
        $company_id = $leadForm->companyID;

        if (empty($default_assigned_to)) {
            return null;
        }

        $user = User::where('userID', $default_assigned_to)
                ->ofCompany($company_id)
                ->active()
                ->ofRole(User::ROLE_SALES)
                ->notInvited()
                ->first();

        return $user ? $user->userID : null;
    }

    /**
     * Prepare lead form fields for bulk insertion.
     *
     * @param string $lead_form_id The UUID of the lead form.
     * @return array The prepared data for insertion.
     */
    private function prepareLeadFormFields($lead_form_id): array
    {
        $fields = [
            LeadFormFieldResource::REFERENCE_FIRST_NAME => LeadFormFieldResource::LABEL_DEFAULT_FIRST_NAME,
            LeadFormFieldResource::REFERENCE_LAST_NAME => LeadFormFieldResource::LABEL_DEFAULT_LAST_NAME,
            LeadFormFieldResource::REFERENCE_EMAIL => LeadFormFieldResource::LABEL_DEFAULT_EMAIL,
            LeadFormFieldResource::REFERENCE_PHONE => LeadFormFieldResource::LABEL_DEFAULT_PHONE,
            LeadFormFieldResource::REFERENCE_ADDRESS => LeadFormFieldResource::LABEL_DEFAULT_ADDRESS,
            LeadFormFieldResource::REFERENCE_MARKETING_SOURCE => LeadFormFieldResource::LABEL_DEFAULT_MARKETING_SOURCE,
            LeadFormFieldResource::REFERENCE_PROJECT_TYPE => LeadFormFieldResource::LABEL_DEFAULT_PROJECT_TYPE,
            LeadFormFieldResource::REFERENCE_CUSTOMER_NOTES => LeadFormFieldResource::LABEL_DEFAULT_CUSTOMER_NOTES,
        ];

        // Prepare and insert fields in bulk
        return array_map(function ($reference, $label) use ($lead_form_id) {
            $type = $reference === LeadFormFieldResource::REFERENCE_CUSTOMER_NOTES
                ? LeadFormFieldResource::TYPE_TEXTAREA
                : LeadFormFieldResource::TYPE_FREEFORM;

            return [
                'created_at' => date('Y-m-d H:i:s'),
                'lead_form_id' => $lead_form_id,
                'field_type' => $type,
                'reference' => $reference,
                'label' => $label,
                'is_enabled' => true,
                'is_required' => false,
            ];
        }, array_keys($fields), $fields);
    }

}
