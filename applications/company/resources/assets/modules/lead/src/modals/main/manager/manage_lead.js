'use strict';

const Inputmask = require("inputmask");

const Api = require('@ca-package/api');

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/dynamic_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
const Modal = require('@ca-submodule/modal').Base;
const Tooltip = require('@ca-submodule/tooltip');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const FormValidator = require('@ca-submodule/validator');

const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const states = require('@cac-js/data/states');
const us_territories = require('@cac-js/data/us_territories');
const provinces = require('@cac-js/data/provinces');

const modal_tpl = require('@cam-lead-tpl/modals/main/manager/manage_lead.hbs');
const marketing_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/marketing.hbs');
const project_type_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/project_type.hbs');

const Actions = {
    ADD: 1,
    DUPLICATE: 2,
    EDIT: 3
};
const Priorities = {
    HOT: {
        label: 'Hot',
        value: Api.Constants.Leads.Priority.HOT
    },
    WARM: {
        label: 'Warm',
        value: Api.Constants.Leads.Priority.WARM
    },
    COLD: {
        label: 'Cold',
        value: Api.Constants.Leads.Priority.COLD
    },
    DEAD: {
        hidden: true,
        label: 'Dead',
        value: Api.Constants.Leads.Priority.DEAD
    }
};

/**
 * @memberof module:Lead/Modals
 */
class ManageLead extends Modal {
    constructor(module) {
        super(modal_tpl({
            priorities: Priorities,
            states,
            us_territories,
            provinces
        }), {
            size: Modal.Size.TINY,
            classes: ['t-manage-lead']
        });
        Object.assign(this.state, {
            module,
            action: null,
            lead: null,
            external_close: false,
            active_users: null,
            assign_to_users: new Map,
            editor: null,
            marketing_types: null,
            marketing_types_dropdown: null,
            project_types: null,
            project_types_dropdown: null
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        Tooltip.initAll(this.elem.content);
        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of [
            'project_type_id', 'priority', 'marketing_type_id', 'assigned_to_user_id', 'email', 'phone_number',
            'business_name', 'first_name', 'last_name', 'address', 'address_2', 'city', 'state', 'zip', 'notes'
        ]) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.validator = FormValidator.create(this.elem.form, {
            project_type_id: {},
            priority: {},
            marketing_type_id: {},
            assigned_to_user_id: {},
            email: {
                maxlength: 100,
                type: 'email'
            },
            phone_number: {
                pattern: '^\\(\\d{3}\\)\\s\\d{3}-\\d{4}',
                patternMessage: 'Number must be formatted as (XXX) XXX-XXXX.'
            },
            business_name: {
                maxlength: 200
            },
            first_name: {
                maxlength: 50,
                required: true
            },
            last_name: {
                maxlength: 50,
                required: true
            },
            address: {
                maxlength: 100
            },
            address_2: {
                maxlength: 100
            },
            city: {
                maxlength: 50
            },
            state: {
                maxlength: 15
            },
            zip: {
                maxlength: 12
            },
            notes: {
                maxlength: 50000
            }
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        Inputmask({
            "mask": "(*************"
        }).mask(this.elem.input.phone_number);

        initSelectPlaceholder(this.state.validator.getInputElem('state'));
        initSelectPlaceholder(this.state.validator.getInputElem('priority'));
        initSelectPlaceholder(this.state.validator.getInputElem('marketing_type_id'));
        initSelectPlaceholder(this.state.validator.getInputElem('project_type_id'));

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });
    };

    /**
     * Get available actions
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Action() {
        return Actions;
    };

    /**
     * Get and cache users list
     *
     * @returns {Promise<array>}
     */
    async getUsers() {
        if (this.state.active_users === null) {
            let {entities: users} = await Api.Resources.Users()
                .fields(['id', 'first_name', 'last_name'])
                .filter('is_active', true)
                .filter('is_user_invited', false)
                .all();
            this.state.active_users = users.map(user => user.data);
        }
        return this.state.active_users;
    };

    /**
     * Create dropdown of projects
     *
     * @returns {Promise<boolean>}
     */
    async createAssignToDropdown() {
        let input = this.state.validator.getInputElem('assigned_to_user_id');
        input.prop('disabled', true);

        let assign_to_input = FormInput.init(input, {
            data_provider: () => {
                let users = [];
                for (let user of this.state.active_users) {
                    users.push({
                        id: user.id,
                        text: `${user.first_name} ${user.last_name}`
                    });
                }
                return users;
            },
            placeholder: '-- Select One --',
            closeOnSelect: true,
        });
        await assign_to_input.promise;
        input.prop('disabled', false);
        return assign_to_input;
    };

    async loadWysiwyg() {
        if (this.state.editor === null) {
            let textarea_config = {
                preset: 'simple',
                height: 200,
                remove_empty_paragraphs: true,
                no_image: true
            };
            this.state.notes = FormInput.init(this.state.validator.getInputElem('notes'), textarea_config);
            this.state.editor = await this.state.notes.promise;
        }
        return this.state.editor;
    };

    async fetchMarketingSources() {
        if (this.state.marketing_types === null) {
            let result = await $.ajax({
                url: window.fx_url.API + 'company/marketing-sources',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded"
            });
            this.state.marketing_types = result.marketing_types;
            this.populateMarketingTypes(result.marketing_types);
        }
        return this.state.marketing_types;
    };

    populateMarketingTypes() {
        if (this.state.marketing_types_dropdown === null) {
            let dropdown = marketing_tpl({
                marketing_types: this.state.marketing_types
            });
            this.state.marketing_types_dropdown = dropdown;
            this.elem.input.marketing_type_id.append(dropdown);
        }
    };

    /**
     * Get and cache project types list
     *
     * @returns {Promise<array>}
     */
    async fetchProjectTypes() {
        if (this.state.project_types === null) {
            let {entities: types} = await Api.Resources.ProjectTypes()
                .fields(['id', 'name'])
                .filter('status', Api.Constants.ProjectTypes.Status.ACTIVE)
                .sort('name', 'asc')
                .all();
            this.state.project_types = types.map(type => type.data);
            this.populateProjectTypes(this.state.project_types);
        }
        return this.state.project_types;
    };

    populateProjectTypes() {
        if (this.state.project_types_dropdown === null) {
            let dropdown = project_type_tpl({
                project_types: this.state.project_types
            });
            this.state.project_types_dropdown = dropdown;
            this.elem.input.project_type_id.append(dropdown);
        }
        this.elem.input.project_type_option = this.elem.form.fxFind('project_type_option');
    };

    checkOptions(value) {
        let value_exists = false;
        this.elem.input.project_type_option.each(function() {
            if (this.value === value) {
                value_exists = true;
                return false;
            }
        });
        return value_exists;
    };

    async populate(data) {
        if (data.priority !== null) {
            this.elem.input.priority.val(data.priority).trigger('change');
        }

        if (data.project_type_id !== null && this.checkOptions(data.project_type_id)) {
            this.elem.input.project_type_id.val(data.project_type_id).trigger('change');
        }

        if (data.assigned_to_user_id !== null) {
            this.elem.input.assigned_to_user_id.val(data.assigned_to_user_id).trigger('change');
        }

        if (data.marketing_type_id !== null) {
            this.elem.input.marketing_type_id.val(data.marketing_type_id).trigger('change');
        }

        if (data.notes !== null) {
            this.elem.input.notes.val(data.notes).trigger('change');
        }

        if (data.email !== null) {
            this.elem.input.email.val(data.email);
        }

        if (data.phone_number !== null) {
            this.elem.input.phone_number.val(data.phone_number);
        }

        if (data.business_name !== null) {
            this.elem.input.business_name.val(data.business_name);
        }

        if (data.first_name !== null) {
            this.elem.input.first_name.val(data.first_name);
        }

        if (data.last_name !== null) {
            this.elem.input.last_name.val(data.last_name);
        }

        if (data.address !== null) {
            this.elem.input.address.val(data.address);
        }

        if (data.address_2 !== null) {
            this.elem.input.address_2.val(data.address_2);
        }

        if (data.city !== null) {
            this.elem.input.city.val(data.city);
        }

        if (data.state !== null) {
            this.elem.input.state.val(data.state);
        }

        if (data.zip !== null) {
            this.elem.input.zip.val(data.zip);
        }
    };

    /**
     * Fetch id for lead
     *
     * @param {string} lead_uuid
     * @returns {Promise<*>}
     */
    async fetchID(lead_uuid) {
        let {entities: leads} = await await Api.Resources.Leads().fields(['id', 'lead_uuid'])
            .filter('lead_uuid', lead_uuid)
            .all();
        let data = leads.map(lead => lead.data);

        return data[0];
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {object} $0.config
     * @param {Promise} $0.promise
     */
    async open({config, promise}) {
        this.state.promise = promise;
        this.state.action = config.action;
        await this.loadWysiwyg();
        await this.getUsers();
        await this.createAssignToDropdown();
        await this.fetchMarketingSources();
        await this.fetchProjectTypes();
        this.populateProjectTypes();
        switch (config.action) {
            case Actions.ADD:
                this.setTitle('Create Lead');
                break;
            case Actions.EDIT:
            case Actions.DUPLICATE:
                if (config.lead_id !== undefined) {
                    this.startWorking();
                    let data = await this.fetchID(config.lead_id);
                    Api.Resources.Leads().retrieve(data.id).then((entity) => {
                        let data = entity.get();
                        this.state.lead = data;
                        this.populate(data);
                        this.resetWorking();
                    }, (error) => {
                        this.showErrorMessage('Unable to fetch lead info');
                        log.error('Unable to fetch lead info', {error});
                    });
                }
                this.setTitle(config.action === Actions.DUPLICATE ? 'Duplicate Lead' : 'Edit Lead');
                break;
        }
        super.open();
    };

    /**
     * Pull data from form inputs
     *
     * @returns {object}
     */
    buildEntity() {
        let entity = {};

        let priority = this.state.validator.getInputElem('priority').val(),
            project_type_id = this.state.validator.getInputElem('project_type_id').val(),
            assigned_to_user_id = this.state.validator.getInputElem('assigned_to_user_id').val(),
            marketing_type_id = this.state.validator.getInputElem('marketing_type_id').val(),
            email = this.state.validator.getInputElem('email').val(),
            phone_number = this.state.validator.getInputElem('phone_number').val(),
            business_name = this.state.validator.getInputElem('business_name').val(),
            first_name = this.state.validator.getInputElem('first_name').val(),
            last_name = this.state.validator.getInputElem('last_name').val(),
            address = this.state.validator.getInputElem('address').val(),
            address_2 = this.state.validator.getInputElem('address_2').val(),
            city = this.state.validator.getInputElem('city').val(),
            state = this.state.validator.getInputElem('state').val(),
            zip = this.state.validator.getInputElem('zip').val(),
            notes = this.state.validator.getInputElem('notes').val();

        entity['priority'] = priority !== '' ? parseInt(priority) : null;
        entity['project_type_id'] = project_type_id !== '' ? project_type_id : null;
        entity['assigned_to_user_id'] = assigned_to_user_id !== '' ? parseInt(assigned_to_user_id) : null;
        entity['marketing_type_id'] = marketing_type_id !== '' ? parseInt(marketing_type_id) : null;
        entity['email'] = email !== '' ? email : null;
        entity['phone_number'] = phone_number !== '' ? phone_number : null;
        entity['business_name'] = business_name !== '' ? business_name : null;
        entity['first_name'] = first_name !== '' ? first_name : null;
        entity['last_name'] = last_name !== '' ? last_name : null;
        entity['address'] = address !== '' ? address : null;
        entity['address_2'] = address_2 !== '' ? address_2 : null;
        entity['city'] = city !== '' ? city : null;
        entity['state'] = state !== '' ? state : null;
        entity['zip'] = zip !== '' ? zip : null;
        entity['notes'] = notes !== '' ? notes : null;
        entity['origin'] = Api.Constants.Leads.Origin.STANDARD;
        return entity;
    };

    /**
     * Save changes
     */
    save() {
        this.startWorking();

        let data = this.buildEntity();
        let resource = Api.Resources.Leads(),
            request = this.state.action !== Actions.ADD ? resource.partialUpdate(this.state.lead.id, data) : resource.store(data);
        request.then(({data}) => {
            this.resetWorking();
            let message = createSuccessMessage(`Lead ${this.state.action === Actions.EDIT ? 'edited' : (this.state.action === Actions.DUPLICATE ? 'duplicated' : 'added')} successfully`);
            this.state.module.router.main_route.layout.toasts.addMessage(message);
            this.state.promise.resolve(data);
            this.state.promise = null;
            this.close();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    // for (let item in item_errors) {
                    //     if (this.state.field[item] === undefined) {
                    //         continue;
                    //     }
                    //     this.state.field[item].addError('fx-' + item, {message: item_errors[item]});
                    // }
                    this.resetWorking();
                    break;
                default:
                    let message = createErrorMessage('Unable to save lead, please contact support');
                    this.state.module.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.elem.content.scrollTop(0);
        this.resetWorking();
        this.state.action = null;
        this.state.lead = null;
        this.state.validator.reset();

        this.elem.input.marketing_type_id.val('').trigger('change');
        this.elem.input.project_type_id.val('').trigger('change');

        this.elem.form[0].reset();
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = ManageLead;
