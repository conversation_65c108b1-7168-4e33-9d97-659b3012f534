'use strict';

const Component = require('@ca-package/router/src/component');

const menu_tpl = require('@cam-user-profile-tpl/pages/main-components/menu.hbs');
const menu_item_tpl = require('@cam-user-profile-tpl/pages/main-components/menu_item.hbs');
const menu_option_tpl = require('@cam-user-profile-tpl/pages/main-components/menu_item_option.hbs');
const {findChild, jsSelector} = require("@ca-package/dom");

class Menu extends Component {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page} parent
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
                active_menu_item: null,
                menu: {
                    profile: {
                        icon: 'user & faces--account-circle-line',
                        label: 'Profile',
                        route: 'profile.details'
                    },
                    security: {
                        icon: 'system--lock-line',
                        label: 'Security',
                        route: 'security'
                    },
                    integrations: {
                        has_access: () => {
                            return profile_data.integrations;
                        },
                        icon: 'editor--link',
                        label: 'Integrations',
                        route: 'integrations'
                    }
                },
                edit_mode: false
            }
        );
    };

    /**
     * Verify user has proper role to see menu item
     *
     * @param {object} subcomponent - menu subcomponent item
     * @returns {boolean}
     */
    hasAccess(subcomponent) {
        let access = true;
        if (typeof subcomponent.has_access === 'function' && subcomponent.has_access() === false) {
            access = false;
        }
        return access;
    };

    /**
     * Set active item for menu
     *
     * @param {string} active_item
     */
    setActiveItem(active_item) {
        if (this.state.active_menu_item === active_item) {
            return;
        }
        if (this.state.active_menu_item !== null) {
            this.state.menu[this.state.active_menu_item].elem.removeClass('active');
        }
        this.state.menu[active_item].elem.addClass('active');
        this.state.active_menu_item = active_item;
    };

    /**
     * Set active item from route id
     *
     * @param {string} route_id
     */
    setActiveItemFromRoute(route_id) {
        for (let id of Object.keys(this.state.menu)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }
            this.setActiveItem(id);
            break;
        }
    };

    /**
     * Sets the edit mode for the menu
     *
     * Disables all menu options while in edit mode
     *
     * @param {boolean} status
     */
    setEditMode(status) {
        this.state.edit_mode = status;
        if (status) {
            this.elem.menu_select.prop('disabled', true);
            this.elem.menu_select_wrapper.addClass('t-disabled');
            this.state.menu.security.elem.addClass('t-disabled');
            this.state.menu.integrations.elem.addClass('t-disabled');
            return;
        }
        this.elem.menu_select.prop('disabled', false);
        this.elem.menu_select_wrapper.removeClass('t-disabled');
        this.state.menu.security.elem.removeClass('t-disabled');
        this.state.menu.integrations.elem.removeClass('t-disabled');
    };

    /**
     * Handle route change
     *
     * @param {object} data
     */
    onRouteChange(data) {
        this.setActiveItemFromRoute(data.current.name);
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        await super.load(request);
        this.setActiveItemFromRoute(this.router.current_route.name);
        let route = this.router.current_route.name;
        if (route === 'profile.details' || route === 'profile.edit') {
            route = 'profile';
        }
        this.elem.menu_select.val(route);
        this.router.subscribe('route-changed', this, 'onRouteChange');
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request) {
        this.router.unsubscribe('route-changed', this);
        await super.unload(request);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.menu = findChild(root, jsSelector('menu'));
        this.elem.menu_select_wrapper = findChild(root, jsSelector('menu-select-wrapper'));
        this.elem.menu_select = findChild(root, jsSelector('menu-select'));

        //Build Menu
        for (let item of Object.keys(this.state.menu)) {
            let this_item = this.state.menu[item];
            if (!this.hasAccess(this_item)) {
                continue;
            }
            let menu = $(menu_item_tpl({
                key: item,
                icon: this_item.icon,
                name: this_item.label
            }));
            let menu_option = $(menu_option_tpl({
                key: item,
                name: this_item.label
            }));
            this.state.menu[item].elem = menu;
            this.elem.menu.append(menu);
            this.elem.menu_select.append(menu_option);
        }

        const that = this;
        this.elem.root.fxClickWatcher('item', function (e) {
            e.preventDefault();
            if (that.state.edit_mode) {
                return;
            }
            let new_route = $(this).data('id');
            if (new_route === that.state.active_menu_item && that.state.menu[that.state.active_menu_item].route === that.router.current_route.name) {
                return;
            }
            let route = that.state.menu[new_route].route;
            that.router.navigate(route);
            if (route === 'profile.details') {
                route = 'profile';
            }
            that.elem.menu_select.val(route);
        });

        this.elem.menu_select.on('change', function (e) {
            e.preventDefault();
            let new_route = $(this).val();
            if (new_route === that.state.active_menu_item && that.state.menu[that.state.active_menu_item].route === that.router.current_route.name) {
                return;
            }
            that.router.navigate(that.state.menu[new_route].route);
        });
    };

    /**
     * Render component
     */
    render() {
        return menu_tpl();
    };
}

module.exports = Menu;
