<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Carbon\Carbon;
use Core\Attributes\SerializeIgnoreAttribute;
use Core\Traits\SerializeTrait;
use Core\Components\Queue\Interfaces\{
    JobAttributeInterface,
    JobBuilderInterface,
    JobEnvelopeInterface,
    JobInterface,
    ManagerInterface
};
use ReflectionClass;
use Throwable;

/**
 * Class Job
 *
 * @package Core\Components\Queue\Classes
 */
abstract class Job implements JobInterface
{
    use SerializeTrait;

    /**
     * @var null|ManagerInterface
     */
    protected static ?ManagerInterface $manager = null;

    /**
     * @var JobEnvelopeInterface|null Envelope instance
     */
    #[SerializeIgnoreAttribute]
    protected ?JobEnvelopeInterface $envelope = null;

    /**
     * Set manager for all job instances
     *
     * @param ManagerInterface $manager
     */
    public static function setManager(ManagerInterface $manager): void
    {
        static::$manager = $manager;
    }

    /**
     * Get manager instance
     *
     * @return ManagerInterface
     */
    public static function getManager(): ManagerInterface
    {
        return static::$manager;
    }

    /**
     * Make new job builder instance for job
     *
     * @param mixed ...$args
     * @return JobBuilderInterface
     */
    public static function make(...$args): JobBuilderInterface
    {
        return new JobBuilder(static::$manager->getJobInstance(static::class, $args));
    }

    /**
     * Dispatch a new job of this class
     *
     * @param array $args
     * @return JobBuilderInterface
     */
    public static function enqueue(...$args): JobBuilderInterface
    {
        $builder = static::make(...$args);
        static::$manager->dispatch($builder);
        return $builder;
    }

    /**
     * Run job immediately
     *
     * @param array $args
     */
    public static function run(...$args): void
    {
        $job = static::$manager->getJobInstance(static::class, $args);
        static::$manager->runJob($job);
    }

    /**
     * Set envelope instance
     *
     * Envelopes contain all the information on when and where to run a job.
     *
     * @param JobEnvelopeInterface $envelope
     */
    public function setEnvelope(JobEnvelopeInterface $envelope): void
    {
        $this->envelope = $envelope;
    }

    /**
     * Get envelope instance
     *
     * @return JobEnvelopeInterface
     */
    public function getEnvelope(): JobEnvelopeInterface
    {
        return $this->envelope;
    }

    /**
     * Get job attribute info
     *
     * @return JobAttributeInterface|null
     */
    public function getAttribute(): ?JobAttributeInterface
    {
        $attribute = (new ReflectionClass($this))
            ->getAttributes(JobAttributeInterface::class, \ReflectionAttribute::IS_INSTANCEOF)[0] ?? null;
        return $attribute?->newInstance();
    }

    /**
     * Get retry time
     *
     * @return Carbon|null
     */
    public function retryAt(): ?Carbon
    {
        return null;
    }

    /**
     * Handle failed call
     *
     * @param Throwable $e
     * @param int $tries
     */
    public function failed(Throwable $e, int $tries): void
    {
        // do nothing
    }

    /**
     * Run code when job is retried after failure
     */
    public function onRequeue(): void
    {
        // do nothing
    }
}
