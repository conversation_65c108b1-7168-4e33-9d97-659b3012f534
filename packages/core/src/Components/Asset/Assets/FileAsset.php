<?php

declare(strict_types=1);

namespace Core\Components\Asset\Assets;

use Core\Components\Asset\Classes\{Asset, Manifest};
use Closure;
use Core\Components\Asset\Exceptions\AssetException;

/**
 * Class FileAsset
 *
 * @package Core\Components\Asset\Assets
 */
abstract class FileAsset extends Asset
{
    /**
     * @var Manifest|null Manifest instance which to pull the asset from
     */
    protected ?Manifest $manifest = null;

    /**
     * @var Closure|string|null Path to asset file
     */
    protected Closure|string|null $path = null;

    /**
     * Render group of similar assets together
     *
     * @param FileAsset[] $assets
     * @return string
     */
    public static function renderGroup(array $assets): string
    {
        return implode('', array_map(fn(self $asset): string => $asset->render(), $assets));
    }

    /**
     * Set path to asset
     *
     * @param Closure|string|null $path
     * @return $this
     */
    public function path(Closure|string|null $path): self
    {
        $this->path = $path;
        return $this;
    }

    /**
     * Get path to asset
     *
     * @param bool $raw
     * @return Closure|string|null
     */
    public function getPath(bool $raw = false): Closure|string|null
    {
        $path = $this->path;
        if (!$raw && $path instanceof Closure) {
            $path = $path();
        }
        return $path;
    }

    /**
     * Assign manifest as source for asset
     *
     * @param Manifest $manifest
     * @return $this
     */
    public function manifest(Manifest $manifest): self
    {
        $this->manifest = $manifest;
        return $this;
    }

    /**
     * Get assigned manifest
     *
     * @return Manifest|null
     */
    public function getManifest(): ?Manifest
    {
        return $this->manifest;
    }

    /**
     * Determine if input looks like a URL
     *
     * @param string $data
     * @return bool
     */
    protected function isUrl(string $data): bool
    {
        foreach (['http://', 'https://', '//'] as $str) {
            if (str_starts_with($data, $str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get URI from asset path
     *
     * If manifest is present, path is pulled from it. Otherwise, it is parsed and then converted from absolute to
     * a public relative path.
     *
     * @return string
     * @throws AssetException
     */
    public function getUri(): string
    {
        if (($path = $this->getPath()) === null) {
            throw new AssetException('No path defined to get URI from');
        }
        if ($this->isUrl($path)) {
            return $path;
        }
        if ($this->manifest !== null) {
            return $this->manifest->uri($path)->build();
        }
        $path_info = $this->manager->parsePath($path, $this->manager->getAssetTypePath(get_called_class()));
        return $this->manager->uri($path_info['info']['name'], $path_info['path'])->build();
    }

    /**
     * Get absolute path to file
     *
     * If manifest is present, path is pulled from it. Otherwise, it is parsed and returned.
     *
     * @return string
     * @throws AssetException
     */
    public function getAbsolutePath(): string
    {
        if (($path = $this->getPath()) === null) {
            throw new AssetException('No path defined to get URI from');
        }
        if ($this->manifest !== null) {
            return $this->manifest->path($path);
        }
        $path_info = $this->manager->parsePath($path, $this->manager->getAssetTypePath(get_called_class()));
        return $this->manager->path($path_info['info']['name'], $path_info['path']);
    }

    /**
     * Get contents of asset
     *
     * @return string
     * @throws AssetException
     */
    public function getContents(): string
    {
        $path = $this->getAbsolutePath();
        if (!file_exists($path)) {
            throw new AssetException('File does not exist: %s', $path);
        }
        if (($contents = file_get_contents($path)) === false) {
            throw new AssetException('Unable to read file: %s', $path);
        }
        return $contents;
    }
}
