<?php

namespace App\Resources;

use App\ResourceDelegates\EmailTemplateDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\EmailTemplate;
use Core\Components\Resource\Classes\Resource;

class EmailTemplateResource extends Resource
{
    const OWNER_TYPE_MANUFACTURER = 1;
    const OWNER_TYPE_COMPANY = 2;

    const SOURCE_SYSTEM = 1;
    const SOURCE_CUSTOM = 2;

    const TYPE_NEW_CUSTOMER = 1;
    const TYPE_SALES_APPOINTMENT = 2;
    const TYPE_SALES_APPOINTMENT_REMINDER = 3;
    const TYPE_CUSTOMER_BID = 4;
    const TYPE_BID_ACCEPTED = 5;
    const TYPE_BID_REJECTED = 6;
    const TYPE_INSTALLATION_APPOINTMENT = 7;
    const TYPE_INSTALLATION_APPOINTMENT_REMINDER = 8;
    const TYPE_WARRANTIES = 9;
    const TYPE_INVOICE = 10;
    const TYPE_BID_FOLLOW_UP = 11;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'emailTemplates';
    protected $model = EmailTemplate::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getOwnerTypes()
    {
        return [
            static::OWNER_TYPE_MANUFACTURER, static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getOwnerTypeMap()
    {
        return [
            EmailTemplate::OWNER_MANUFACTURER => static::OWNER_TYPE_MANUFACTURER,
            EmailTemplate::OWNER_COMPANY => static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getSources()
    {
        return [
            static::SOURCE_SYSTEM, static::SOURCE_CUSTOM
        ];
    }

    public static function getTypes()
    {
        return [
            static::TYPE_NEW_CUSTOMER, static::TYPE_SALES_APPOINTMENT, static::TYPE_SALES_APPOINTMENT_REMINDER,
            static::TYPE_CUSTOMER_BID, static::TYPE_BID_ACCEPTED, static::TYPE_BID_REJECTED,
            static::TYPE_INSTALLATION_APPOINTMENT, static::TYPE_INSTALLATION_APPOINTMENT_REMINDER,
            static::TYPE_WARRANTIES, static::TYPE_INVOICE, static::TYPE_BID_FOLLOW_UP
        ];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(EmailTemplateDelegate::class);
    }
}
