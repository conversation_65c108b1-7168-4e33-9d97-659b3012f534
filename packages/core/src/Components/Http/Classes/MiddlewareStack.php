<?php

namespace Core\Components\Http\Classes;

use Core\Classes\Arr;
use Core\Components\Http\Exceptions\MiddlewareException;
use Core\Components\Http\Interfaces\MiddlewareInterface;
use Serializable;

/**
 * Class MiddlewareStack
 *
 * @package Core\Components\Http\Classes
 */
class MiddlewareStack implements Serializable
{
    /**
     * Push types
     */
    const PUSH_TOP = 1;
    const PUSH_BOTTOM = 2;

    protected static $global_middleware = null;

    /**
     * @var MiddlewareInterface
     */
    protected $middleware;

    /**
     * @var array[]
     */
    protected $items = [];

    /**
     * @param MiddlewareInterface $middleware
     */
    public static function setGlobalMiddleware(MiddlewareInterface $middleware)
    {
        self::$global_middleware = $middleware;
    }

    /**
     * MiddlewareStack constructor
     *
     * @param MiddlewareInterface $middleware
     */
    public function __construct(MiddlewareInterface $middleware)
    {
        $this->middleware = $middleware;
    }

    /**
     * @param string $name
     * @param array $args
     * @return $this
     *
     * @throws MiddlewareException
     *
     * @todo add in ability to insert at a position if needed
     */
    public function add($name, array $args = [])
    {
        if (!$this->middleware->has($name)) {
            throw new MiddlewareException('Unable to find middleware: %s', $name);
        }
        $this->items[$name] = $args;
        return $this;
    }

    /**
     * @param string $name
     * @return $this
     *
     * @throws MiddlewareException
     */
    public function group($name)
    {
        if (($stack = $this->middleware->getGroup($name)) === false) {
            throw new MiddlewareException('Unable to find middleware group: %s', $name);
        }
        $this->merge($stack);
        return $this;
    }

    /**
     * @param MiddlewareStack $stack
     * @param int $action
     */
    public function merge(MiddlewareStack $stack, $action = self::PUSH_BOTTOM)
    {
        $array_1 = $this->items;
        $array_2 = $stack->getItems();
        if ($action === self::PUSH_TOP) {
            list($array_2, $array_1) = [$array_1, $array_2];
        }
        $this->items = Arr::mergeRecursiveDistinct($array_1, $array_2);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function remove($name)
    {
        if (!isset($this->items[$name])) {
            return false;
        }
        unset($this->items[$name]);
        return true;
    }

    /**
     * @return array[]
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @return array[]
     */
    public function toArray()
    {
        return $this->items;
    }

    public function serialize()
    {
        return serialize($this->items);
    }

    public function unserialize($data)
    {
        $this->middleware = self::$global_middleware;
        $this->items = unserialize($data);
    }
}
