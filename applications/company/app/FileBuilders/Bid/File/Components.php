<?php

declare(strict_types=1);

namespace App\FileBuilders\Bid\File;

use App\Resources\Bid\ItemResource;
use App\Resources\{ContentPartialResource, ContentTemplateResource};
use App\Services\ContentTemplateService;
use Common\Models\BidItem;
use Core\Exceptions\AppException;

/**
 * Class Components
 *
 * Defines and configures all the available components which make up a bid PDF.
 *
 * @package App\FileBuilders\Bid\File
 */
class Components
{
    const TYPE_TEMPLATE = 1;
    const TYPE_FILES = 2;

    const COVER = 1;
    const INTRO = 2;
    const SECTIONS = 3;
    const LINE_ITEMS = 4;
    const TERMS_CONDITIONS = 5;
    const IMAGES = 6;
    const MEDIA_LINKS = 7;
    const DRAWINGS = 8;
    const CUSTOM_DRAWINGS = 9;
    const MEDIA = 10;

    /**
     * @var array List of available components with default config
     */
    protected $config = [
        self::COVER => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'cover'
        ],
        self::INTRO => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'intro'
        ],
        self::SECTIONS => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'sections',
            'styles' => [
                'resourceStyle' => ['pdfs/bid/form_render.css']
            ]
        ],
        self::LINE_ITEMS => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'line_items'
        ],
        self::TERMS_CONDITIONS => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'terms_conditions'
        ],
        self::IMAGES => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'images'
        ],
        self::MEDIA_LINKS => [
            'type' => self::TYPE_TEMPLATE,
            'name' => 'media'
        ],
        self::DRAWINGS => [
            'type' => self::TYPE_FILES
        ],
        self::CUSTOM_DRAWINGS => [
            'type' => self::TYPE_FILES
        ],
        self::MEDIA => [
            'type' => self::TYPE_FILES
        ]
    ];

    /**
     * Get list of all components
     *
     * @return array
     */
    public static function getAll()
    {
        return [
            self::COVER, self::INTRO, self::SECTIONS, self::LINE_ITEMS, self::TERMS_CONDITIONS, self::IMAGES,
            self::MEDIA_LINKS, self::DRAWINGS, self::CUSTOM_DRAWINGS, self::MEDIA
        ];
    }

    /**
     * Determine if component exists
     *
     * @param int $component
     * @return bool
     */
    public function has(int $component): bool
    {
        return isset($this->config[$component]);
    }

    /**
     * Get component config
     *
     * @param int $component
     * @return array|null
     */
    public function get(int $component): ?array
    {
        return $this->config[$component] ?? null;
    }

    /**
     * Determines if component is enabled
     *
     * @param int $component
     * @return bool
     * @throws AppException
     */
    public function isEnabled(int $component): bool
    {
        if (($config = $this->get($component)) === null) {
            throw new AppException('Unable to find component: %d', $component);
        }
        return $config['enabled'] ?? true;
    }

    /**
     * Add file to file type component
     *
     * @param int $component
     * @param string $path
     * @param int|null $pages
     * @return Components
     * @throws AppException
     */
    public function addFile(int $component, string $path, ?int $pages): self
    {
        if (!isset($this->config[$component])) {
            throw new AppException('Unable to find component: %d', $component);
        }
        $config =& $this->config[$component];
        if ($config['type'] !== self::TYPE_FILES) {
            throw new AppException('Cannot add files to non file component');
        }
        if (!isset($config['files'])) {
            $config['files'] = [];
        }
        $config['files'][] = [$path, $pages];

        return $this;
    }

    /**
     * Configure components based on settings in bid item model
     *
     * Enables or disables template components based on model data, stores template_id for those which are in use for the
     * build method to use
     *
     * @param ItemResource $item_resource
     * @param BidItem $model
     * @return Components
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function configure(ItemResource $item_resource, BidItem $model): self
    {
        $fields = $item_resource->getFields();
        foreach ($this->config as &$item) {
            switch ($item['type']) {
                case self::TYPE_TEMPLATE:
                    $field = $fields->get("{$item['name']}_content_template_id");
                    if (($template_id = $field->outputValueFromModel($model)) === null) {
                        $item['enabled'] = false;
                        break;
                    }
                    $item['template_id'] = $template_id;
                    break;
            }
            unset($item);
        }

        return $this;
    }

    /**
     * Build out component configuration based on info in context
     *
     * Compiles templates using context data and store with component. Pull in files for file type components or disable
     * them when none are present.
     *
     * @param Context $context
     * @throws AppException
     */
    public function build(Context $context): void
    {
        $acl = $context->getAcl();

        $template_context = $context->toArray();

        $content_template = ContentTemplateResource::make($acl);
        $content_partial = ContentPartialResource::make($acl);
        foreach ($this->config as $component => &$item) {
            if (isset($item['enabled']) && !$item['enabled']) {
                continue;
            }
            switch ($item['type']) {
                case self::TYPE_TEMPLATE:
                    if (
                        ($component === self::IMAGES && !isset($template_context['images'])) ||
                        ($component === self::MEDIA_LINKS && count($template_context['media']) === 0)
                    ) {
                        $item['enabled'] = false;
                        break;
                    }
                    $data = ContentTemplateService::render($content_template, $content_partial, $item['template_id'], $template_context);
                    $item['template_styles'] = implode("\n", $data['styles']);
                    $item['template_content'] = $data['content'];
                    break;
                case self::TYPE_FILES:
                    if (isset($item['files'])) {
                        break;
                    }
                    // if no files have been added to component, we disable it
                    $item['enabled'] = false;
                    break;
            }
            unset($item);
        }
    }
}
