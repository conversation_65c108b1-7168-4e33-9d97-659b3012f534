<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Models\Pivots\CompanyTrainingSectionModule;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyTrainingSectionModuleAction extends Model
{
    use SoftDeletes;

    protected $table = 'companiesTrainingSectionModulesActions';
    protected $primaryKey = 'companyTrainingSectionModuleActionID';
    protected $fillable = [
        'companyTrainingSectionModuleID', 'trainingActionID', 'isCompleted', 'completedAt',
        'createdByUserID', 'updatedByUserID', 'deletedByUserID'
    ];
    protected $casts = [
        'companyTrainingSectionModuleID' => 'int',
        'trainingActionID' => 'int',
        'isCompleted' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dates = ['completedAt'];

    public function action()
    {
        return $this->belongsTo(TrainingAction::class, 'trainingActionID', 'trainingActionID');
    }

    public function companyTrainingSectionModule()
    {
        return $this->belongsTo(CompanyTrainingSectionModule::class, 'companyTrainingSectionModuleID', 'companyTrainingSectionModuleID');
    }
}
