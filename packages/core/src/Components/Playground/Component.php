<?php

declare(strict_types=1);

namespace Core\Components\Playground;

use Core\Components\Console\Interfaces\CommandInterface;
use Core\Components\Playground\Commands\PlaygroundCommand;
use Core\Interfaces\KernelInterface;

/**
 * Class Component
 *
 * @package Core\Components\Playground
 */
class Component extends \Core\Classes\Component
{
    /**
     * Register component
     */
    public function register(): void
    {
        if ($this->kernel->getMode() === KernelInterface::MODE_CONSOLE) {
            $command = $this->kernel->get(CommandInterface::class);
            $command->add('playground', PlaygroundCommand::class, 'run');
        }
    }
}
