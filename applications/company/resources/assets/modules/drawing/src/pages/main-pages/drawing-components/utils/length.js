'use strict';

const Number = require('@cac-js/utils/number');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Utils
 */
class Length {
    /**
     * Constructor
     *
     * @param {number} per_unit - pixels per unit of measurement
     * @param {number} divisions - number of divisions of unit
     */
    constructor(per_unit, divisions) {
        this.state = {
            per_unit,
            per_unit_area: Math.pow(per_unit, 2),
            divisions
        };
        let {division_amounts, ranges} = this.createRanges(per_unit, divisions);
        this.state.division_amounts = division_amounts;
        this.state.round_func = this.createFunction(ranges);
    };

    /**
     * Get ranges based on per unit and division numbers
     *
     * Finds the min and max value for each division.
     *
     * @param {number} per_unit
     * @param {number} divisions
     * @returns {{division_amounts: Map<number, number>, ranges: {min: number, division: number, max: number}[]}}
     */
    createRanges(per_unit, divisions) {
        let per_division = Number.of(per_unit).dividedBy(divisions),
            range_delta = per_division.dividedBy(2),
            ranges = [],
            division_amounts = new Map,
            amount = Number.of(0);
        for (let i = 0; i <= divisions; i++) {
            let range = {
                min: Math.max(0, amount.minus(range_delta).toNumber()),
                division: i,
                max: Math.min(per_unit, amount.plus(range_delta).toNumber())
            };
            division_amounts.set(i, amount.toNumber());
            amount = amount.plus(per_division);
            ranges.push(range);
        }
        return {division_amounts, ranges};
    };

    /**
     * Split ranges into groups based on max size count
     *
     * Designed to make if statement lookups faster by doing a binary type search.
     *
     * @param {{min: number, division: number, max: number}[]} ranges
     * @param {number} [max_size=2] - number of ranges per group
     * @returns {{ranges: *, type: string}|{ranges: *[], max: number, type: string}}
     */
    splitRanges(ranges, max_size = 2) {
        let length = ranges.length;
        if (length <= max_size) {
            return {
                type: 'ranges',
                ranges
            };
        }
        let half = Math.ceil(ranges.length / 2),
            range_1 = ranges.slice(0, half),
            range_2 = ranges.slice(half);
        return {
            type: 'group',
            max: range_1.reduce((max, range) => {
                return range.max > max ? range.max : max;
            }, 0),
            ranges: [
                this.splitRanges(range_1),
                this.splitRanges(range_2)
            ]
        };
    };

    /**
     * Build function code for specific range group
     *
     * @param {object} config
     * @returns {string}
     */
    buildRange(config) {
        if (config.type === 'ranges') {
            let str = '',
                end = config.ranges.length - 1;
            for (let i = 0; i < config.ranges.length; i++) {
                let range = config.ranges[i];
                if (end > 0) {
                    str += i === 0 ? 'if' : (i !== end ? 'else if' : 'else');
                    if (i === 0 || i !== end) {
                        str += `(amount <= ${range.max})`;
                    }
                    str += '{';
                }
                str += `return ${range.division};`;
                if (end > 0) {
                    str += '}';
                }
            }
            return str;
        }
        return `if(amount<${config.max}){${this.buildRange(config.ranges[0])}}else{${this.buildRange(config.ranges[1])}}`;
    };

    /**
     * Create function from ranges
     *
     * A function is generated which can utilize dynamic if statements to do more efficient lookups rather than looping
     * through a list of ranges.
     *
     * @param {{min: number, division: number, max: number}[]} ranges
     * @returns {Function}
     */
    createFunction(ranges) {
        let func = `'use strict';${this.buildRange(this.splitRanges(ranges))}`;
        return new Function('amount', func);
    };

    /**
     * Get pixel amount of specific division
     *
     * @param {number} division
     * @returns {number}
     */
    getDivisionAmount(division) {
        return this.state.division_amounts.get(division);
    };

    /**
     * Get unit values for length
     *
     * @param {number} length
     * @returns {{feet: number, inches: number}}
     */
    get(length) {
        let feet = 0;
        if (length >= this.state.per_unit) {
            feet = Math.floor(length / this.state.per_unit);
        }
        let inches = this.state.round_func(length - (feet * this.state.per_unit));
        if (inches === 12) {
            feet += 1;
            inches = 0;
        }
        return {feet, inches};
    };

    /**
     * Format length to feet and inches based on range rounding system
     *
     * @param {number} length
     * @returns {string}
     */
    format(length) {
        let {feet, inches} = this.get(length),
            str = '';
        if (feet > 0 || (feet === 0 && inches === 0)) {
            str += `${feet}'`;
        }
        if (inches > 0) {
            str += (feet > 0 ? ' ' : '') + `${inches}"`;
        }
        return str;
    };

    /**
     * Format area of width and height to feet squared based on range rounding system
     *
     * @param {number} width
     * @param {number} height
     * @returns {string}
     */
    formatArea(width, height) {
        width = this.roundToNearestDivision(width);
        height = this.roundToNearestDivision(height);
        let sft = Number.of(width).times(height).dividedBy(this.state.per_unit_area);
        return `${sft.toDecimalPlaces(2).toString()}ft²`;
    };

    /**
     * Round length to nearest division
     *
     * @param {number} length
     * @returns {number}
     */
    roundToNearestDivision(length) {
        let units = length / this.state.per_unit,
            base = Math.floor(units) * this.state.per_unit,
            division = this.state.round_func(length - base);
        return base + this.getDivisionAmount(division);
    };

    /**
     * Get pixels from unit value
     *
     * @param {number|object} units
     * @param {boolean} [round=true]
     * @returns {number}
     */
    fromUnits(units, round = true) {
        let length = null;
        if (typeof units === 'number') {
            length = units * this.state.per_unit;
        } else if (typeof units === 'object') {
            let feet = units.feet !== null && units.feet !== undefined ? parseInt(units.feet) : 0,
                inches = units.inches !== null && units.inches !== undefined ? parseInt(units.inches) : 0;
            // handle different scenarios here
            length = (feet + (inches / 12)) * this.state.per_unit;
        }
        if (length === null) {
            throw new Error('Invalid units argument');
        }
        return round ? Math.round(length) : length;
    };
}

module.exports = Length;
