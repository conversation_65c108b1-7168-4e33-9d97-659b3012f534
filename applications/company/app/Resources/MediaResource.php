<?php

namespace App\Resources;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\MediaDelegate;
use App\Traits\Resource\CompanyMediaTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\Media;
use Core\Components\Resource\Classes\Resource;

class MediaResource extends Resource implements ResourceCompanyMediaInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'media';
    protected $model = Media::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(MediaDelegate::class);
    }
}
