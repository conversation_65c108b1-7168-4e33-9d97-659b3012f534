'use strict';

const Dimension = require('../dimension');
const Tool = require('../../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment/Dimension
 */
class FrenchDrain extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: FrenchDrain.Entity.Type.FRENCH_DRAIN,
            tool_type: Tool.Type.FRENCH_DRAIN
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({inches: 12}),
            color: 'rgba(181, 181, 181, 0.95)'
        });
        return state;
    };
}

module.exports = FrenchDrain;
