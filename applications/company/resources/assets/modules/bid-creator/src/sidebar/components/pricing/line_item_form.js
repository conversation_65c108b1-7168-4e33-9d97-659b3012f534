/**
 * @module BidCreator/Sidebar/Components/Pricing/Forms
 */

'use strict';

/**
 * @type {module:PanelStack/Panel}
 */
const Panel = require('@ca-submodule/panel-stack').Panel;

const DeleteModal = require('../../../modals/line-item/delete');
const LineItem = require('../../../entities/line_item');
const GeneralForm = require('./forms/general');
const ProductForm = require('./forms/product');
const PriceAdjustmentForm = require('./forms/price_adjustment');

const line_item_form_tpl = require('@cam-bid-creator-tpl/sidebar/components/pricing/line_item_form.hbs');

const States = {
    ADD: 1,
    EDIT: 2
};

/**
 * @memberof module:BidCreator/Sidebar/Components/Pricing
 */
class LineItemForm extends Panel {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Sidebar/Components.Pricing} component - Pricing component
     */
    constructor(component) {
        super();
        Object.assign(this.state, {
            component: component,
            types: new Map([
                [null, {
                    label: '-- Choose Type --',
                    title: 'Line Item'
                }],
                [LineItem.Type.GENERAL, {
                    label: 'General',
                    title: 'General',
                    delete_title: 'Delete General Line Item',
                    form: new GeneralForm,
                    rendered: false
                }],
                [LineItem.Type.PRODUCT, {
                    label: 'Product',
                    title: 'Product',
                    delete_title: 'Delete Product Line Item',
                    form: new ProductForm,
                    rendered: false
                }],
                [LineItem.Type.DISCOUNT, {
                    label: 'Discount',
                    title: 'Discount',
                    delete_title: 'Delete Discount',
                    form: new PriceAdjustmentForm('discount'),
                    rendered: false
                }],
                [LineItem.Type.FEE, {
                    label: 'Fee',
                    title: 'Fee',
                    delete_title: 'Delete Fee',
                    form: new PriceAdjustmentForm('fee'),
                    rendered: false
                }]
            ]),
            active_type: null,
            config: {},
            locked: false,
            line_item: null,
            external_close: true
        });
    };

    /**
     * Get delete modal instance
     *
     * If modal doesn't exist, it will be created and cached for future calls
     *
     * @readonly
     *
     * @returns {Delete}
     */
    static get delete_modal() {
        if (this._delete_modal === undefined) {
            this._delete_modal = new DeleteModal;
        }
        return this._delete_modal;
    };

    /**
     * States
     *
     * @readonly
     *
     * @returns {{ADD: number, EDIT: number}}
     */
    static get State() {
        return States;
    };

    /**
     * Get component
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components.Pricing}
     */
    get component() {
        return this.state.component;
    };

    /**
     * Get active type
     *
     * @readonly
     *
     * @returns {number}
     */
    get active_type() {
        return this.state.active_type;
    };

    /**
     * Get config
     *
     * @readonly
     *
     * @returns {Object}
     */
    get config() {
        return this.state.config;
    };

    /**
     * Get line item
     *
     * @readonly
     *
     * @returns {*|null}
     */
    get line_item() {
        return this.state.line_item;
    };

    /**
     * Set external close status
     *
     * Used we are adding a new line item and we don't want to lookup check to run which see's if the line item is currently
     * in use in a line item form panel
     *
     * @param {boolean} bool
     */
    setExternalClose(bool) {
        this.state.external_close = bool;
    };

    /**
     * Determines if panel allows being closed externally
     *
     * @returns {boolean}
     */
    allowExternalClose() {
        return this.state.external_close;
    };

    /**
     * Load panel
     *
     * @param {Object} config
     * @param {number} config.type - Type of form to show
     * @param {number[]} config.available_types - Array of types which are allowed to be used
     * @param {boolean} config.lock - Determines if user is allowed to change item type
     * @param {(Object|undefined)} config.line_item - Line item instance
     */
    load(config) {
        this.state.config = typeof config === 'object' ? config : {};
        this.state.external_close = true;
        this.elem.actions.hide();
        if (config.line_item === undefined) {
            this.state.state = States.ADD;
            if (this.state.config.type === undefined) {
                this.state.config.type = null;
            }
        } else {
            this.state.line_item = config.line_item;
            this.state.state = States.EDIT;
            this.state.config.type = this.state.line_item.type;
            this.state.config.lock = true;
            if (this.state.line_item.source === LineItem.Source.BID_SIDEBAR_PRICING) {
                this.elem.actions.show();
                this.elem.sidebar_form.addClass('t-includes-actions');
            }
        }
        let available_types = Array.from(this.state.types.keys());
        if (config.available_types !== undefined) {
            available_types = config.available_types;
            // add empty type to the beginning so the user has to chose one
            available_types.unshift(null);
        }
        this.setAvailableTypes(available_types);
        this.setType(this.state.config.type);
        if (typeof this.state.config.lock === 'boolean' && this.state.config.lock) {
            this.lock();
        }
    };

    /**
     * Unload panel
     */
    unload() {
        if (this.state.locked) {
            this.unlock();
        }
        this.setType(null);
        this.state.line_item = null;
        this.elem.sidebar_form.removeClass('t-includes-actions');
        this.state.component.changeSectionTitle('Pricing');
    };

    /**
     * Lock form so type cannot be changed
     */
    lock() {
        this.state.locked = true;
        if (this.isBooted()) {
            this.elem.type.hide();
        }
    };

    /**
     * Unlock form so type can be changed
     */
    unlock() {
        this.state.locked = false;
        if (this.isBooted()) {
            this.elem.type.show();
        }
    };

    /**
     * Set available types
     *
     * @param {number[]} types - Array of allowed types
     */
    setAvailableTypes(types) {
        this.elem.input.type.empty();
        types.forEach((type) => {
            let $type = this.state.types.get(type);
            this.elem.input.type.append($type.option.clone(true));
        });
    };

    /**
     * Set type
     *
     * @param {number} type
     * @param {boolean} [update_elem=true]
     */
    setType(type, update_elem = true) {
        if (this.state.locked) {
            return;
        }
        if (update_elem) {
            this.state.types.get(type).option.prop('selected', true);
        }
        this.handleType(type);
    };

    /**
     * Setup form for type
     *
     * @param {number} type
     */
    handleType(type) {
        if (this.state.active_type !== null && type !== this.state.active_type) {
            this.unloadType(this.state.active_type);
        }
        this.state.active_type = type;
        let type_info = this.state.types.get(type);
        if (type_info.form !== undefined) {
            if (!type_info.rendered) {
                type_info.elem = $(type_info.form.render(this));
                this.elem.container.append(type_info.elem);
                type_info.rendered = true;
                type_info.form.boot();
            }

            type_info.form.load(this.state.config);
            type_info.elem.show();
        }
        this.state.component.changeSectionTitle((this.state.state === States.ADD ? 'Add ' : 'Edit ') + type_info.title);
        if (this.state.line_item !== null && this.state.line_item.source === LineItem.Source.BID_SIDEBAR_PRICING) {
            this.elem.delete.text(type_info.delete_title);
        }
    };

    /**
     * Unload type
     *
     * @param {number} type
     */
    unloadType(type) {
        let $type = this.state.types.get(type);
        $type.elem.hide();
        $type.form.unload();
    };

    /**
     * Start delete process by opening a confirmation modal
     *
     * If the form wasn't loaded with a line item or if the line item wasn't created in the sidebar then we don't do
     * anything
     */
    startDelete() {
        if (this.state.line_item === null || this.state.line_item.source !== LineItem.Source.BID_SIDEBAR_PRICING) {
            return;
        }
        LineItemForm.delete_modal.open(this, this.state.line_item);
    };

    /**
     * Boot line item form panel
     *
     * @param {jQuery} root - Panel jQuery element
     */
    boot(root) {
        super.boot(root);

        this.elem.cancel = this.elem.root.fxFind('cancel-action');
        this.elem.save = this.elem.root.fxFind('save-action');

        this.elem.sidebar_form = this.elem.root.fxFind('sidebar-form');
        this.elem.container = this.elem.root.fxFind('form-wrapper');

        this.elem.type = this.elem.root.fxFind('type-wrapper');
        this.elem.input = {
            type: this.elem.type.fxFind('type')
        };
        this.elem.actions = this.elem.root.fxFind('actions');
        this.elem.delete = this.elem.root.fxFind('delete-action');

        if (this.locked) {
            this.elem.type.hide();
        }

        // set type options
        for (let [type, info] of this.state.types.entries()) {
            let option = $('<option />');
            option.text(info.label).attr('value', type === null ? '' : type);
            option.data('bc_type', type);
            info.option = option;
        }
        this.elem.input.type.fxEvent('change', () => {
            this.setType(this.elem.input.type.find(':selected').data('bc_type'), false);
        });

        this.elem.cancel.fxClick((e) => {
            e.preventDefault();
            // @todo check for changes, prompt if found
            this.controller.pop();
            return false;
        });
        this.elem.save.fxClick((e) => {
            e.preventDefault();
            if (this.state.active_type !== null) {
                this.state.types.get(this.state.active_type).form.submit();
            }
            return false;
        });
        this.elem.delete.fxClick((e) => {
            e.preventDefault();
            this.startDelete();
            return false;
        });
    };

    /**
     * Render line item form panel
     *
     * @returns {string}
     */
    render() {
        return line_item_form_tpl();
    };
}

module.exports = LineItemForm;
