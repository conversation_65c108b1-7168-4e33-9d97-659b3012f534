<?php

namespace App\Resources;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\CompanyDelegate;
use App\Traits\Resource\CompanyMediaTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\Company;
use Core\Components\Resource\Classes\Resource;
use Core\Exceptions\AppException;

class CompanyResource extends Resource implements ResourceCompanyMediaInterface
{
    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    const STATUS_SIGNUP = 4;
    const STATUS_TRIAL = 5;
    const STATUS_ACTIVE = 1;
    const STATUS_SUSPENDED = 2;
    const STATUS_DORMANT = 3;

    const SIGNUP_STATUS_ADDRESS = 1;
    const SIGNUP_STATUS_INTAKE = 2;
    const SIGNUP_STATUS_SUBSCRIPTION = 3;
    const SIGNUP_STATUS_COMPLETE = 4;

    const SETUP_WIZARD_STEP_INSTRUCTION = 0;
    const SETUP_WIZARD_STEP_GENERAL = 1;
    const SETUP_WIZARD_STEP_USERS = 2;
    const SETUP_WIZARD_STEP_BID_CUSTOMIZATION = 3;
    const SETUP_WIZARD_STEP_EMAILS = 4;
    const SETUP_WIZARD_STEP_TERMS_CONDITIONS = 5;
    const SETUP_WIZARD_STEP_PRODUCTS = 6;
    const SETUP_WIZARD_STEP_MEDIA = 7;
    const SETUP_WIZARD_STEP_WARRANTY_PACKET = 8;
    const SETUP_WIZARD_STEP_QUICKBOOKS_ONLINE = 9;
    const SETUP_WIZARD_STEP_GOOGLE_CALENDAR = 10;
    const SETUP_WIZARD_STEP_ADDITIONAL_SERVICES = 11;
    const SETUP_WIZARD_STEP_REVIEW = 12;
    const SETUP_WIZARD_STEP_COMPLETE = 13;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_DELETE);

    protected $table = 'companies';
    protected $model = Company::class;

    protected $allow_no_user = true;

    public static function getStatuses()
    {
        return [
            static::STATUS_ACTIVE, static::STATUS_SUSPENDED, static::STATUS_DORMANT, static::STATUS_SIGNUP,
            static::STATUS_TRIAL
        ];
    }

    public static function getSignupStatuses()
    {
        return [
            static::SIGNUP_STATUS_ADDRESS, static::SIGNUP_STATUS_INTAKE, static::SIGNUP_STATUS_SUBSCRIPTION,
            static::SIGNUP_STATUS_COMPLETE
        ];
    }

    protected static function boot()
    {
        static::delegate(CompanyDelegate::class);
    }

    /**
     * Get available users for the current subscription of passed company
     *
     * Note: will return null if the available users is unlimited
     *
     * @param int $company_id
     * @return int|null
     * @throws AppException
     */
    public function getAvailableUserCount(int $company_id): ?int
    {
        $company = $this->newQuery()
            ->ofCompany($company_id)
            ->leftJoin('companySubscriptions', function ($join) {
                $join->on('companySubscriptions.companyID', '=', 'companies.companyID')
                    ->where('companySubscriptions.isCurrent', 1);
            })
            ->first(['companies.status', 'companies.subscriptionID', 'companySubscriptions.companySubscriptionID', 'companySubscriptions.users', 'companySubscriptions.additionalUsers']);
        if ($company->status === static::STATUS_TRIAL) {
            /** @var SubscriptionResource $subscription_resource */
            $subscription_resource = $this->relationResource('subscription');
            return $subscription_resource->getTrialUserCount($company->subscriptionID);
        }
        if ($company->companySubscriptionID === null && $company->status !== static::STATUS_ACTIVE) {
            throw new AppException('Unable to find company/subscription data for id: %s', $company_id);
        }
        if ($company->users === null) {
            return null;
        }
        return (int) $company->users + (int) $company->additionalUsers;
    }

    /**
     * Determines if passed company has user seats available
     *
     * @param int $company_id
     * @return bool
     * @throws AppException
     */
    public function hasUserSeatsAvailable(int $company_id): bool
    {
        $available_users = $this->getAvailableUserCount($company_id);
        // if available_users is null, then they have unlimited seats
        if ($available_users === null) {
            return true;
        }
        $user_resource = UserResource::make($this->acl());
        return $user_resource->activeUsersByCompanyID($company_id) < $available_users;
    }
}
