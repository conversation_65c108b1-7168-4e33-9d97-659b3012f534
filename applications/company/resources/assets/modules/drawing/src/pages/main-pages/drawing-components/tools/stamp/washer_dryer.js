'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class WasherDryer extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, WasherDryer.Type.WASHER_DRYER);
        Object.assign(this.state, {
            label: 'Washer Dryer',
            icon: 'module--drawing--tools--washer-dryer',
            node_type: Node.Entity.Type.WASHER_DRYER
        });
    };
}

module.exports = WasherDryer;
