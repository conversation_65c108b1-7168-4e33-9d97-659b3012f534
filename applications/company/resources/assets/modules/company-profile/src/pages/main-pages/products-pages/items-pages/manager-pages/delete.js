'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

class Delete extends Modal {
    /**
     * Get and cache delete product modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../../../modals/product/delete');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open delete product modal with promise
     *
     * @param {string} product_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal(product_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                product_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.openModal(request.params.product_id).then((result) => {
            if (result === null) { // no action was taken
                this.router.navigate('products.items.manager');
                return;
            }
            this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Product deleted successfully'));
            // redirect instead of navigate to remove the delete from the nav history so user can't hit back
            // and see the same modal which will no longer work
            this.router.redirect('products.items.manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}

module.exports = Delete;
