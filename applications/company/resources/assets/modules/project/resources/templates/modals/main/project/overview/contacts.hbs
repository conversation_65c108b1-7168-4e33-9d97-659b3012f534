<h3 class="text-center">Edit Additional Project Contacts</h3>
<p class="text-center no-margin">
    <small>Additional project contacts will be CC'ed on all emails that are sent for this project.</small>
</p>
<form>
    <div class="row">
        <div class="large-12 columns text-center">
            <table data-js="contact-table" id="emailTable" class="emailTable" cellpadding="0" cellspacing="0">
                <thead style="background:none;">
                <tr>
                    <th width="35%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0.4rem 0rem 0.4rem 0.7rem;font-weight: normal;font-size: 0.875rem;">Name</th>
                    <th width="20%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0.4rem 0rem 0.4rem 0.7rem;font-weight: normal;font-size: 0.875rem;">Phone</th>
                    <th width="35%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0.4rem 0rem 0.4rem 0.7rem;font-weight: normal;font-size: 0.875rem;">Email</th>
                    <th width="10%" style="text-align:center;color:#000000;line-height: .5rem;border-bottom:0;padding:0rem 0.4rem;">
                        <a data-js="add-contact"><img src="{{add}}" /></a>
                    </th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
    <div class="medium-12 columns text-center">
        <button class="button" data-js="save">Save</button>
        <button class="button secondary" data-js="close">Cancel</button>
    </div>
    <div class="large-12 columns">
        <div data-js="email-validation-result" class="callout warning validating-email-message project-contact">
            <p>Verifying email deliverability...<span><a data-js="abort-email-validation" class="abort-validation">skip</a></span></p>
        </div>
    </div>
</form>
