<?php

namespace App\Services;

use App\Classes\Log;
use Core\StaticAccessors\Config;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Monolog\Logger;

class GoogleRecaptchaService
{
    private static ?Client $httpClient = null;

    /**
     * @var Logger|null
     */
    protected static ?Logger $logger = null;

    protected static float $RECAPTCHA_SCORE_THRESHOLD = 0.5;

    private static string $RECAPTCHA_VERIFY_URL = 'https://www.google.com/recaptcha/api/';
    private static string $RECAPTCHA_SECRET_KEY = 'website_leads_form.recaptcha_secret';

    /**
     * Returns a configured Guzzle HTTP client for Google reCAPTCHA requests.
     * Implements singleton pattern to ensure only one instance of the client is used.
     *
     * @return Client The configured Guzzle HTTP client.
     */
    public static function getHttpClient(): Client
    {
        if (self::$httpClient === null) {
            self::$httpClient = new Client([
                'base_uri' => self::$RECAPTCHA_VERIFY_URL,
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json',
                ],
            ]);
        }

        return self::$httpClient;
    }

    /**
     * Verify the reCAPTCHA token by making a request to the Google reCAPTCHA API.
     *
     * @param string $token The reCAPTCHA token from the client.
     * @return bool True if verification is successful, False otherwise.
     * @throws Exception If there is an issue with the request.
     */
    public static function verifyToken(string $token): bool
    {
        try {
            $secret = Config::get(self::$RECAPTCHA_SECRET_KEY);
            $response = self::getHttpClient()->post('siteverify', [
                'form_params' => [
                    'secret' => $secret,
                    'response' => $token,
                ],
            ]);

            $result = json_decode($response->getBody(), true);

            if ($result['success'] && $result['score'] >= self::$RECAPTCHA_SCORE_THRESHOLD && $result['action'] === 'submit') {
                return true;
            }

            $errors = isset($result['error-codes']) ? implode(', ', $result['error-codes']) : 'Failed reCAPTCHA verification.';
            self::getLog()->error('reCAPTCHA verification failed', ['errors' => $errors]);
            return false;
        } catch (ClientException | ServerException $e) {
            self::handleApiException($e);
            return false;
        } catch (GuzzleException $e) {
            self::getLog()->error('Error communicating with reCAPTCHA API: ' . $e->getMessage());
            throw new Exception('Error during reCAPTCHA verification.', $e->getCode(), $e);
        }
    }

    /**
     * Handle API exceptions.
     *
     * @param Exception $e The caught exception.
     * @param array $payload
     */
    public static function handleApiException(Exception $e): void
    {
        if ($e instanceof ClientException || $e instanceof ServerException) {
            $response_body = json_decode($e->getResponse()->getBody()->getContents(), true);
            $error_details['api_error_codes'] = $response_body['error-codes'] ?? ['Unknown error'];
        }

        self::getLog()->error('Google reCAPTCHA API request error', $error_details);
        throw new Exception('There was an issue processing your request. Please try again later.', $e->getCode());
    }

    /**
     * Get Log instance configured for Google reCAPTCHA service.
     *
     * @return Logger
     */
    public static function getLog(): Logger
    {
        if (static::$logger === null) {
            static::$logger = Log::create('google_recaptcha_api', [
                'file' => 'google_recaptcha_api.log',
                'app_processor' => function (Log\AppProcessor $processor) {
                    return $processor->withInput(true);
                }
            ]);
        }
        return static::$logger;
    }
}
