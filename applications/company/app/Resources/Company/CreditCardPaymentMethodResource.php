<?php

namespace App\Resources\Company;

use App\ResourceDelegates\Company\CreditCardPaymentMethodDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyCreditCardPaymentMethod;
use Core\Components\Resource\Classes\Resource;

class CreditCardPaymentMethodResource extends Resource
{
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_CREATE | self::ACTION_UPDATE | self::ACTION_PARTIAL_UPDATE | self::ACTION_DELETE;

    protected $table = 'companyCreditCardPaymentMethods';
    protected $model = CompanyCreditCardPaymentMethod::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(CreditCardPaymentMethodDelegate::class);
    }
}
