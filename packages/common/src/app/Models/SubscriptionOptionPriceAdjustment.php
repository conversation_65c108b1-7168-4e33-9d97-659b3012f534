<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class SubscriptionOptionPriceAdjustment extends Model
{
    const TYPE_ADD_ON = 1;
    const TYPE_DISCOUNT = 2;
    const TYPE_FEE = 3;

    const AMOUNT_TYPE_TOTAL = 1;
    const AMOUNT_TYPE_PERCENTAGE = 2;

    protected $table = 'subscriptionOptionPriceAdjustments';
    protected $primaryKey = 'subscriptionOptionPriceAdjustmentID';
    protected $fillable = [
        'subscriptionOptionID', 'type', 'name', 'amountType', 'amount', 'delayCount', 'occurrenceCount',
        'isInitialSubscriptionOnly', 'isRefundable', 'addonID'
    ];
    protected $casts = [
        'subscriptionOptionID' => 'int',
        'type' => 'int',
        'amountType' => 'int',
        'delayCount' => 'int',
        'occurrenceCount' => 'int',
        'isInitialSubscriptionOnly' => 'bool',
        'isRefundable' => 'bool',
        'addonID' => 'int'
    ];

    public function subscriptionOption()
    {
        return $this->belongsTo(SubscriptionOption::class, 'subscriptionOptionID', 'subscriptionOptionID');
    }

    public function scopeIsSubsequentSubscriptionOnly($query)
    {
        return $query->where("{$this->table}.isInitialSubscriptionOnly", 0);
    }
}
