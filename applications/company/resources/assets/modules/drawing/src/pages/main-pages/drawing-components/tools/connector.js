'use strict';

const Base = require('./base');
const Event = require('../event');
const ConnectorService = require('../node-services/connector');

/**
 * Connector tool class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools
 */
class Connector extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     * @param {number} type
     */
    constructor(controller, type) {
        super(controller, type);
        Object.assign(this.state, {
            config_panel_type: null,
            accept_events: [Event.Type.DOWN, Event.Type.MOVE, Event.Type.UP, Event.Type.TAP]
        });
    };

    /**
     * Set tool mode
     *
     * @param {number} mode
     */
    setMode(mode) {
        switch (mode) {
            case Base.Mode.UPDATE:
                this.controller.interaction.down_hit_test = false;
                break;
            case Base.Mode.IDLE:
                this.controller.interaction.down_hit_test = true;
                break;
        }
        super.setMode(mode);
    };

    /**
     * Handle tap event
     */
    onInteractTap() {
        if (this.mode === Base.Mode.UPDATE) {
            this.clearTool();
        }
    };

    /**
     * Handle down event
     */
    onInteractDown() {
        if (this.mode === Base.Mode.UPDATE) {
            this.state.action = Base.Action.MOVE;
            this.setMovePoints({
                position: this.node.position
            });
            this.state.service = new ConnectorService(this.node);
        }
    };

    /**
     * Handle move event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractMove(event) {
        if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                if (this.move(event) !== false) {
                    this.state.service.moveConnector(this.getMovePoint('position'));
                }
            }
        }
    };

    /**
     * Handle up event
     */
    onInteractUp() {
        if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                this.state.service.commit();
                this.clearMoveData();
                this.state.service = null;
            }
            this.state.action = null;
        }
    };
}

module.exports = Connector;
