<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Entities;

use App\Services\HubspotApi\Classes\BaseEntity;
use HubSpot\Client\Crm\Companies\Model\{SimplePublicObject, SimplePublicObjectWithAssociations};

/**
 * Class CompanyEntity
 *
 * @package App\Services\HubspotApi\Entities
 */
class CompanyEntity extends BaseEntity
{
    /**
     * @var int|null Company status
     */
    protected ?int $status = null;

    /**
     * Create entity from simple public object returned from SDK
     *
     * @param SimplePublicObject|SimplePublicObjectWithAssociations $object
     * @return static
     */
    public static function fromSimplePublicObject(SimplePublicObject|SimplePublicObjectWithAssociations $object): static
    {
        $entity = new static();
        $entity->setID($object->getId());
        $entity->setStatus($object->getProperties()['hs_lead_status'] ?? null);
        return $entity;
    }

    /**
     * Set status for company
     *
     * @param string|null $status
     * @return $this
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status !== null ? (int) $status : $status;
        return $this;
    }

    /**
     * Get status
     *
     * @return int|null
     */
    public function getStatus(): ?int
    {
        return $this->status;
    }
}
