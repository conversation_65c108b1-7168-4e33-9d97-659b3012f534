'use strict';

const Base = require('./base');
const Paper = require('../paper');
const Tool = require('../tools/base');
const Line = require('../utils/line');
const Point = require('../utils/point');
const Miter = require('../utils/miter');
const ConnectorService = require('../node-services/connector');
const PointCache = require('../node-services/point_cache');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes
 */
class Connector extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            layer: Paper.Layer.LINE_CONNECTOR,
            type: Base.Entity.Type.CONNECTOR,
            tool_type: Tool.Type.CONNECTOR,
            allowed_types: new Map,
            run_detach_actions: true,
            miter: new Miter,
            persist: false,
            miter_auto_commit: true
        });
    };

    /**
     * Get point cache from paper
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @returns {PointCache}
     */
    static getPointCache(paper) {
        let cache = paper.storage.get('connector_point_cache');
        if (cache === undefined) {
            cache = new PointCache('position');
            paper.storage.set('connector_point_cache', cache);
        }
        return cache;
    };

    /**
     * Get connector instance by point
     *
     * Looks to see if connector exists at point. If not, one is created and returned. Otherwise, we check to see if
     * node is allowed on connector. If so, we return it otherwise a new connector is made.
     *
     * Note: this can only be used on initialization since it will not be properly updated if a connector moves or is
     *       deleted.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} node
     * @param {boolean} [init=false] - determines if this is being called during node initialization
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector}
     */
    static getByPoint(paper, point, node, init = false) {
        let cache = this.getPointCache(paper),
            list = cache.getByPoint(point),
            node_connector = null;
        if (list.length > 0) {
            for (let connector of list) {
                if (!connector.isNodeAllowed(node)) {
                    continue;
                }
                node_connector = connector;
                break;
            }
        }
        if (node_connector === null) {
            node_connector = paper.createNode(Base.Entity.Type.CONNECTOR, {
                position: point
            }, false);
            // if we are initializing a drawing, we want to disable auto committing
            if (init) {
                node_connector.miter_auto_commit = false;
            }
            cache.store(node_connector);
        }
        return node_connector;
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            position: new this.paper.ps.Point(0, 0)
        });
        return state;
    };

    /**
     * Load data into state
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        state.position = Point.of(state.position);
        return state;
    };

    /**
     * Get point cache for connectors
     *
     * @readonly
     *
     * @returns {PointCache}
     */
    get point_cache() {
        return Connector.getPointCache(this.paper);
    };

    /**
     * Get miter instance
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Utils.Miter}
     */
    get miter() {
        return this.properties.miter;
    };

    /**
     * Get position
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get position() {
        return this.state.position;
    };

    /**
     * Set if we commit line updates automatically to miter system
     *
     * @param {boolean} bool
     */
    set miter_auto_commit(bool) {
        this.properties.miter_auto_commit = bool;
    };

    /**
     * Set if we run any detach actions when line is removed
     *
     * @param {boolean} bool
     */
    set run_detach_actions(bool) {
        this.properties.run_detach_actions = bool;
    };

    /**
     * Handle a zoom change by rendering again
     */
    onZoomChange() {
        this.render(true);
    };

    /**
     * Select node
     */
    select() {
        this.paperSubscribe('zoom-changed', 'onZoomChange');
        super.select();
    };

    /**
     * Deselect node
     */
    deselect() {
        if (!this.selected || this.deleted) {
            return;
        }
        this.paperUnsubscribe('zoom-changed');
        super.deselect();
    };

    /**
     * Determine if line can be attached to connector
     *
     * Simulates adding line to miter to determine if the miter build will be valid.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {string} endpoint
     * @returns {boolean}
     */
    canAttachLine(line, endpoint) {
        let opposite_connector = line.getConnector(line.oppositeEndpoint('from'));
        if (opposite_connector === this) {
            return false;
        }
        let miter = this.miter,
            miter_line = {
                id: line.id,
                from: line.from,
                to: line.to,
                width: line.width,
                inverted: endpoint === 'to'
            };
        // update line to proper position before trying to add line to mitering system
        miter_line[endpoint] = this.position;
        let build = miter.addLine(miter_line).build();
        miter.rollback();
        return build.valid;
    };

    /**
     * Join lines of connector
     *
     * If connector has only 2 lines and they are parallel, we just join them together since the connector is no
     * longer needed.
     *
     * @returns {{line: module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment, connector: module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector}|boolean}
     */
    joinLines() {
        let lines = this.getNodeAttachmentsByTag('miter', true);
        if (lines.length !== 2) {
            return false;
        }
        let [{node: one, data: {endpoint: one_endpoint}}, {node: two, data: {endpoint: two_endpoint}}] = lines;
        if (!Line.linesParallel(one, two, 400)) {
            return false;
        }
        // get connector at opposite end of the one connected to this one, this will be the new connector for the final
        // line
        let new_connector = two.getConnector(two.oppositeEndpoint(two_endpoint));
        // detach the old line from the new connector
        two.detachConnector(new_connector, {
            run_actions: false
        });
        // assign new connector to final line and update position
        one.setConnector(one_endpoint, new_connector, {
            attach: {
                update: true,
                notify: false
            },
            detach: {
                run_actions: false
            }
        });

        // transfer any necessary attachments to final line
        let attachments = two.getNodeAttachmentsByTag(['stamp', 'opening']);
        if (attachments.length > 0) {
            for (let attachment of attachments) {
                attachment.auto_detach = false;
                attachment.setLine(one, false, false);
            }
        }

        // detach segment from this connector so events won't be sent to it
        two.detachConnector(this, {
            run_actions: false
        });
        two.delete();

        // emit events for line so any attachments update
        one.emitNodeEvent('update');
        one.emit('update');
        one.render(true);
        one.commit();

        // re-enable auto detach for all attachments
        attachments.forEach(attachment => attachment.auto_detach = true);

        // finally, delete this connector as it is no longer needed
        this.delete();

        return {
            line: one,
            connector: new_connector
        };
    };

    /**
     * Track allowed types of nodes which can attach
     *
     * @param {number[]} types
     */
    trackTypes(types) {
        for (let type of types) {
            let count = this.properties.allowed_types.get(type);
            if (count === undefined) {
                count = 0;
            }
            count++;
            this.properties.allowed_types.set(type, count);
        }
    };

    /**
     * Untrack types of nodes allowed to attach
     *
     * @param {number[]} types
     */
    untrackTypes(types) {
        for (let type of types) {
            let count = this.properties.allowed_types.get(type);
            if (count === undefined) {
                continue;
            }
            count--;
            if (count > 0) {
                this.properties.allowed_types.set(type, count);
                continue;
            }
            this.properties.allowed_types.delete(type);
        }
    };

    /**
     * Determine if node is allowed to attach
     *
     * If no allowed types are provided, we allow the first attachment to succeed.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} node
     * @returns {boolean}
     */
    isNodeAllowed(node) {
        return (this.properties.allowed_types.size === 0 && this.getNodeAttachmentCount() === 0) || this.properties.allowed_types.has(node.type);
    };

    /**
     * Attach node
     *
     * If attachment is tagged as miter, we add it to mitering system. We also track the allowed types of the node.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {(null|object)} config
     * @param {object} [config.data={}]
     * @param {array} [config.tags=[]]
     * @param {boolean} [notify=true]
     * @returns {NodeAttachment}
     */
    attachNode(node, config = null, notify = true) {
        if (!this.isNodeAllowed(node)) {
            throw new Error(`Node type ${node.type} is not allowed`);
        }
        let attachment = super.attachNode(node, config, notify);
        if (attachment.tags.indexOf('miter') !== -1) {
            this.properties.miter.addLine({
                id: node.id,
                from: node.from,
                to: node.to,
                width: node.width,
                inverted: attachment.data.endpoint === 'to'
            });
            if (this.properties.miter_auto_commit) {
                this.properties.miter.commit();
            }
        }
        this.trackTypes(node.snap_types);
        return attachment;
    };

    /**
     * Detach segment from connector
     *
     * If attachment is tagged as miter, we remove it from the mitering system. Node types are also untracked. If
     * attachment count goes to zero, we delete the connector. If connector only has 2 lines, we test if they are
     * parallel and join the line if they are. Otherwise, we just render all associated lines.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} node
     * @param {object} [config={}]
     * @param {boolean} [config.notify=true]
     * @param {boolean} [config.run_actions=true]
     * @param {boolean} [config.render=true]
     * @returns {{actions: boolean, deleted: (boolean|undefined), join_line: (boolean|module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment|undefined)}}
     */
    detachSegment(node, {run_actions = true, notify = true, render = true} = {}) {
        let attachment = this.detachNode(node, notify);
        if (attachment.tags.indexOf('miter') !== -1) {
            this.properties.miter.deleteLine(node.id);
            this.properties.miter.commit();
        }
        this.untrackTypes(node.snap_types);
        let result = {
                actions: run_actions
            };
        if (run_actions) {
            let attachment_count = this.getNodeAttachmentCount();
            result.deleted = false;
            result.join = false;
            if (attachment_count === 0) {
                this.delete();
                result.deleted = true;
            } else if (attachment_count === 2) {
                result.join = this.joinLines();
            }
            if (render) {
                // if a join occurred, then we need to render the new connector and all associated segments
                if (result.join !== false) {
                    result.join.connector.renderAttachments(true);
                } else if (!result.deleted) { // if not deleted, then we just render all directly attached segments
                    this.renderAttachments();
                }
            }
        }
        return result;
    };

    /**
     * Move node by vector
     *
     * Disabled since this can only be handled via connector node service.
     *
     * @param {object} data
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Vector} delta
     * @param {boolean} [notify=true]
     */
    move(data, delta, notify = true) {
        throw new Error('Move not allowed, must be done via miter node group');
    };

    /**
     * Render all attached nodes
     *
     * @param {boolean} [full=false] - determines if we update all related lines instead of just direct attachments
     */
    renderAttachments(full = false) {
        if (full) {
            new ConnectorService(this).render();
            return;
        }
        this.emitNodeEvent('render');
    };

    /**
     * Handle pre rendering actions
     *
     * Enable miter auto commit which is disabled during initialization and commit any mitering changes.
     */
    preRender() {
        this.properties.miter_auto_commit = true;
        this.properties.miter.commit();
    };

    /**
     * Draw node
     *
     * Currently, connectors only display when selected. They are not rendered otherwise.
     */
    draw() {
        if (this.selected) {
            let zoom_factor = 1 / this.paper.view.zoom,
                inner = new this.paper.ps.Path.Circle({
                    center: [0, 0],
                    radius: 3 * zoom_factor,
                    fillColor: this.properties.selected_color
                }),
                outer = new this.paper.ps.Path.Circle({
                    center: [0, 0],
                    radius: 10 * zoom_factor,
                    fillColor: 'rgba(0, 0, 0, 0.5)'
                }),
                item = new this.paper.ps.Group({
                    children: [outer, inner],
                    position: this.state.position,
                    parent: this.getLayer('selected')
                });
            this.setPaperItem('main', item, true);
        }
    };

    /**
     * Normalize properties of node after update
     *
     * @param {object} data
     */
    postUpdate(data) {
        this.state.position = this.state.position.round();
    };

    /**
     * Handle event from attached node
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {string} event - event name
     * @param {object} data - event data
     */
    onAttachedNodeEvent(node, event, data) {
        if (event === 'delete') {
            this.detachSegment(node, {
                run_actions: this.properties.run_detach_actions
            });
        }
    };

    /**
     * Delete connector
     *
     * @param {boolean} [notify=true]
     */
    delete(notify = true) {
        super.delete(notify);
        this.point_cache.remove(this);
    };
}

module.exports = Connector;
