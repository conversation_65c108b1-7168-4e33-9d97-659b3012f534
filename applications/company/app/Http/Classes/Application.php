<?php

declare(strict_types=1);

namespace App\Http\Classes;

/**
 * Class Application
 *
 * @package App\Http\Classes
 */
class Application extends \Core\Components\Http\Classes\Application
{
    /**
     * @var string[] List of components to load
     */
    protected array $components = [
        \Core\Components\Cache\Component::class,
        \Core\Components\Queue\Component::class,
        \Core\Components\Resource\Component::class,
        \Core\Components\RequestValidation\Component::class,
        \Core\Components\Session\Component::class,
        \Core\Components\Auth\Component::class,
        \Core\Components\Asset\Component::class
    ];

    /**
     * @var string[] List of extensions to load after components
     */
    protected array $extensions = [
        \App\Extensions\EmailExtension::class,
        \App\Extensions\ResourceExtension::class,
        \App\Extensions\TextExtension::class,
        \App\Extensions\Http\RouteExtension::class,
        \App\Extensions\Http\MiddlewareExtension::class,
        \App\Extensions\Http\ResponseExtension::class,
        \App\Extensions\SessionExtension::class,
        \Common\Extensions\SessionExtension::class,
        \App\Extensions\Http\ViewExtension::class,
        \App\Extensions\Http\ViewBuilderExtension::class,
        \App\Extensions\AssetExtension::class
    ];

    /**
     * @var string Http Exception handler for app
     */
    protected string $exception_handler = ExceptionHandler::class;
}
