<?php

declare(strict_types=1);

namespace Core\Classes;

use Core\Interfaces\{
    ConsoleExceptionHandlerInterface,
    ExceptionHandlerInterface,
    HttpExceptionHandlerInterface,
    KernelInterface
};
use ErrorException;
use Throwable;

/**
 * Class Error
 *
 * @package Core\Classes
 */
class Error
{
    /**
     * ExceptionHandler constructor
     *
     * @param KernelInterface $kernel
     */
    public function __construct(protected KernelInterface $kernel)
    {
        \set_error_handler([$this, 'errorHandler']);
        \set_exception_handler([$this, 'handle']);
        \register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Get error handler based on current kernel mode
     *
     * @return ExceptionHandlerInterface
     */
    protected function getProperHandler(): ExceptionHandlerInterface
    {
        $mode = $this->kernel->getMode();
        if ($mode === KernelInterface::MODE_AUTO) {
            $mode = php_sapi_name() === 'cli' ? KernelInterface::MODE_CONSOLE : KernelInterface::MODE_HTTP;
        }
        $class = match ($mode) {
            KernelInterface::MODE_CONSOLE => ConsoleExceptionHandlerInterface::class,
            KernelInterface::MODE_HTTP => HttpExceptionHandlerInterface::class
        };
        return $this->kernel->get($class);
    }

    /**
     * Get ErrorException using info provided by error handler
     *
     * @param int $number
     * @param string $message
     * @param string $file
     * @param int $line
     * @return ErrorException
     */
    protected function getErrorException(int $number, string $message, string $file, int $line): ErrorException
    {
        $type = match ($number) {
            \E_ERROR, \E_USER_ERROR, \E_RECOVERABLE_ERROR => 'Fatal Error',
            \E_WARNING, \E_USER_WARNING => 'Warning',
            \E_NOTICE, \E_USER_NOTICE => 'Notice',
            \E_DEPRECATED, \E_USER_DEPRECATED => 'Deprecated',
            \E_STRICT => 'Strict',
            default => 'Unknown Error'
        };
        $severity = match ($number) {
            \E_WARNING, \E_USER_WARNING, \E_NOTICE, \E_USER_NOTICE, \E_DEPRECATED, \E_USER_DEPRECATED, \E_STRICT => 0,
            default => 1
        };
        return new ErrorException("{$type}: {$message}", $number, $severity, $file, $line);
    }

    /**
     * Error handler function to process PHP errors
     *
     * @param int $number
     * @param string $message
     * @param string $file
     * @param int $line
     *
     * @throws ErrorException
     */
    public function errorHandler(int $number, string $message, string $file, int $line): void
    {
        if (!(\error_reporting() & $number)) {
            return;
        }
        $handler = $this->getProperHandler();
        $exception = $this->getErrorException($number, $message, $file, $line);

        \Sentry\captureException($exception);

        if (!$handler->errorShouldHalt($exception)) {
            $handler->handleError($exception);
            return;
        }
        if ($exception->getSeverity() === 1) {
            $handler->handleException($exception);
            exit;
        }
        throw $exception;
    }

    /**
     * Determine if error type is considered fatal
     *
     * @param int $type
     * @return bool
     */
    protected function isFatal(int $type): bool
    {
        return \in_array($type, [\E_ERROR, \E_CORE_ERROR, \E_COMPILE_ERROR, \E_PARSE]);
    }

    /**
     * Script shutdown handler
     */
    public function handleShutdown(): void
    {
        $error = \error_get_last();
        if ($error === null || !$this->isFatal($error['type'])) {
            return;
        }
        $this->handle(
            $this->getErrorException($error['type'], $error['message'], $error['file'], $error['line'])
        );
    }

    /**
     * Handle exception
     *
     * @param Throwable $e
     */
    public function handle(Throwable $e): void
    {
        $handler = $this->getProperHandler();
        $handler->handleException($e);

        \Sentry\captureException($e);
    }
}
