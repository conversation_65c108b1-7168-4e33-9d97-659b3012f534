<div class="section m-integrations-section" data-id="integrations">
    <div class="section-loading" data-js="loader"></div>
    <div class="m-page-menu" data-js="tab-menu">
        {{#each tab_items}}
            <div class="c-pm-tab{{#if @first}} t-active{{/if}}" data-js="tab-item" data-id="{{@key}}">
                <svg class="c-pmt-icon"><use xlink:href="{{#if custom_icon}}#icon--{{icon}}{{else}}#remix-icon--{{icon}}{{/if}}"></use></svg>
                <a class="c-pmt-title">{{title}}</a>
            </div>
        {{/each}}
    </div>
    <div class="c-i--pages" data-js="page-container"></div>
</div>