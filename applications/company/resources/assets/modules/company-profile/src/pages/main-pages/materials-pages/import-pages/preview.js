"use strict";

const accounting = require("accounting");

const Page = require("@ca-package/router/src/page");
const Table = require("@ca-submodule/table").Base;

const {
    remapObject,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/csv");
const {
    validateRecords,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/validation");

const preview_tpl = require("@cam-company-profile-tpl/pages/main-pages/materials-pages/import-pages/preview.hbs");

/**
 * Material Import Preview Page
 *
 * Second step in the material import wizard.
 * Allows users to map CSV columns to material fields and preview the data before proceeding.
 */
class Preview extends Page {
    /**
     * Initializes the Preview page.
     *
     * @param {module:Router.Controller} router - Router controller instance.
     * @param {string} name - Route name.
     * @param {module:Router.Page|null} [parent=null] - Optional parent page.
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            mobile: false,
        });
    };

    /**
     * Resets mapping and dropdown state.
     */
    resetState() {
        this.state.dropdown = {};
    };

    /**
     * Configures a dropdown for field mapping based on CSV headers.
     *
     * @param {string} name - Field name.
     * @param {number} defaultIndex - Default selected column index.
     */
    configureMappingDropdown(name, defaultIndex) {
        this.elem.dropdown[name] = this.elem.root.fxFind(`dropdown-${name}`);
        this.elem.dropdown[name].html("");

        // Configure mapping options based on CSV columns
        if (
            this.state.parent.state.csv_columns &&
            this.state.parent.state.csv_columns.length > 0
        ) {
            for (let [
                index,
                column,
            ] of this.state.parent.state.csv_columns.entries()) {
                this.elem.dropdown[name].append(
                    $("<option></option>")
                        .val(column)
                        .text(column)
                        .prop(
                            "selected",
                            this.parent.state.mapping[name]
                                ? this.parent.state.mapping[name] === column
                                : index === defaultIndex,
                        ),
                );

                // Setting default mapping according to defaultIndex, just if the user not selected a value before
                if (
                    !this.parent.state.mapping[name] &&
                    index === defaultIndex
                ) {
                    this.parent.state.mapping[name] = column;
                }
            }
        }
        // Handle change events
        this.elem.dropdown[name].on("change", () => {
            this.parent.state.mapping[name] = this.elem.dropdown[name].val();
            this.reloadTable();
        });
    };

    /**
     * Reprocesses the table using current field mappings.
     */
    reloadTable() {
        try {
            this.parent.startWorking();

            const csv_data = this.state.parent.state.csv_data;
            if (csv_data) {
                this.getTable().then((table) => {
                    const data = csv_data?.map((row) =>
                        remapObject(this.parent.state.mapping, row),
                    );
                    const materials = this.buildNormalizedMaterialMap(data);
                    this.state.parent.state.import_data = validateRecords(
                        materials,
                        this.parent.state.materials_rules,
                    );

                    if (this.state.mobile) {
                        // Limit data to only 4 rows (just for preview table)
                        table.setTableData(data.slice(0, 4));
                    } else {
                        // Use the full dataset when not in mobile mode
                        table.setTableData(data);
                    }
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            this.parent.resetWorking();
        }
    };

    /**
     * Builds a Map of normalized material objects from raw input data.
     * Each material is passed through safeMaterialType and assigned a numeric `id` based on its index.
     *
     * @param {Array<Object>} data - Raw array of material data.
     * @returns {Map<number, Object>} - Map of sanitized material objects indexed by their original array position.
     */
    buildNormalizedMaterialMap(data) {
        const materialMap = new Map();

        for (let index = 0; index < data.length; index++) {
            const rawMaterial = data[index];
            const material = this.parent.safeMaterialType(rawMaterial);
            material.id = index;
            materialMap.set(index, material);
        }

        return materialMap;
    };

    /**
     * Initializes and returns the table instance, if not already created.
     *
     * @returns {Promise<Table>} Initialized table instance.
     */
    async getTable() {
        if (this.state.table === null || this.state.table === undefined) {
            let table_config = {
                no_header: true,
            };
            if (this.state.mobile) {
                Object.assign(table_config, {
                    paging_enabled: false, // Disable pagination completely
                    dom: "t", // Only show the table, no controls
                    pageLength: 4, // Set page length to 4 rows
                    displayLength: 4, // Alternative property that might be used
                    lengthMenu: [4], // Only allow showing 4 rows
                    info: false, // Hide info text (showing X of Y entries)
                });
            }

            const table = new Table(this.elem.table, table_config);

            table.setToolbar({ filter: false, settings: false });

            table.setColumns({
                id: {
                    label: "ID",
                    orderable: false,
                    class_name: "id-column",
                    value: (data) => {
                        if (!data.id_user_defined) {
                            return "";
                        }
                        return table.trimColumn(
                            data.id_user_defined,
                            5,
                            true,
                            true,
                        );
                    },
                },
                name: {
                    label: "Name",
                    orderable: false,
                    value: (data) => {
                        if (!data.name) {
                            return "";
                        }
                        return table.trimColumn(data.name, 35, true, true);
                    },
                },
                cost: {
                    label: "Cost",
                    orderable: false,
                    class_name: "cost-column",
                    value: (data) => {
                        if (
                            data.cost === null ||
                            data.cost === undefined ||
                            data.cost === ""
                        ) {
                            return "";
                        }
                        if (isNaN(data.cost)) {
                            return table.trimColumn(data.cost, 10, true, true);
                        }
                        return accounting.formatMoney(data.cost);
                    },
                },
                markup: {
                    label: "Markup",
                    orderable: false,
                    class_name: "markup-column",
                    value: (data) => {
                        if (
                            data.markup === null ||
                            data.markup === undefined ||
                            data.markup === ""
                        ) {
                            return "";
                        }
                        if (isNaN(data.markup)) {
                            return table.trimColumn(
                                data.markup,
                                10,
                                true,
                                true,
                            );
                        }
                        return `${parseFloat(data.markup).toFixed(2)}%`;
                    },
                },
                unit: {
                    label: "Unit",
                    orderable: true,
                    class_name: "unit-column",
                    value: (data) => {
                        if (!data.unit) {
                            return "";
                        }
                        return table.trimColumn(data.unit, 12, true, true);
                    },
                },
            });

            table.setData([]);
            this.state.table = table.setup();
        }
        return this.state.table;
    };

    /**
     * Loads the page, configures dropdowns and renders the preview table.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback to proceed after loading.
     */
    async load(request, next) {
        try {
            this.parent.startWorking();
            this.resetState();

            if (!(this.state.parent.state.csv_data?.length > 0)) {
                this.router.navigate("materials.import.upload");
                return;
            }

            this.elem.dropdown = [];
            this.configureMappingDropdown("id_user_defined", 0);
            this.configureMappingDropdown("name", 1);
            this.configureMappingDropdown("cost", 2);
            this.configureMappingDropdown("markup", 3);
            this.configureMappingDropdown("unit", 5);

            await this.getTable();
            this.reloadTable();

            await super.load(request, next);
        } catch (error) {
            console.error(error);
        } finally {
            this.parent.resetWorking();
        }
    };

    /**
     * Unloads the page and removes the preview table.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback to proceed after unloading.
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    /**
     * Initializes DOM elements.
     *
     * @param {jQuery} root - Root DOM element.
     */
    boot(root) {
        super.boot(root);
        this.elem.table = root.fxFind("preview-table-container");
        this.state.mobile = window.outerWidth < 600 || window.outerHeight < 750;
    };

    /**
     * Renders the preview page.
     *
     * @returns {string} Rendered HTML string.
     */
    render() {
        return preview_tpl({
            back_route: "materials.import.upload",
            next_route: "materials.import.review_errors",
        });
    };
}

module.exports = Preview;
