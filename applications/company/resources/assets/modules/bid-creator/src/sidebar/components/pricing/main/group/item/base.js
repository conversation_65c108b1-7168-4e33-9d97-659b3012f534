'use strict';

const Accordion = require('../../../../../accordion');
const List = require('../../../../../list');
const Utils = require('../../../../../../utils');
const LineItem = require('../../../../../../entities/line_item');

const title_tpl = require('@cam-bid-creator-tpl/sidebar/components/pricing/title.hbs');
const line_item_tpl = require('@cam-bid-creator-tpl/sidebar/components/pricing/line_item.hbs');

/**
 * Base class
 *
 * This is the base class for all group item types, not used directly
 *
 * @memberof module:BidCreator/Sidebar/Components/Pricing/Main/Group/Item
 */
class Base {
    /**
     * Constructor
     */
    constructor() {
        /**
         * @private
         */
        this.state = {
            booted: false,
            group: null,
            title: null,
            line_items: new Map,
            item: null,
            list: null,
            no_items_text: 'No items',
            add_action_label: 'Add Line Item',
            total: Utils.getDecimal('0.00'),
            subtotal: Utils.getDecimal('0.00')
        };
    };

    /**
     * Build list instance
     *
     * @returns {List}
     */
    buildList() {
        let list = new List({
            type: List.Type.EDIT,
            no_items_text: this.state.no_items_text,
            action_handler: (item) => {
                this.handleEditAction(item);
            }
        });
        list.addAction({
            type: List.ActionType.ADD,
            label: this.state.add_action_label,
            handler: () => {
                this.handleAddAction();
            }
        });
        return list;
    };

    /**
     * Get list instance (build if it doesn't exist and cache)
     *
     * @returns {List}
     */
    getList() {
        if (this.state.list === null) {
            this.state.list = this.buildList();
        }
        return this.state.list;
    };

    /**
     * Handle list add action
     *
     * @abstract
     */
    handleAddAction() {};

    /**
     * Handle list edit action
     *
     * @abstract
     *
     * @param {Object} item - List item config
     */
    handleEditAction(item) {};

    /**
     * Get item id
     *
     * @returns {string}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get accordion item
     *
     * @returns {Accordion.Item}
     */
    get item() {
        return this.state.item;
    };

    /**
     * Get parent group
     *
     * @returns {Object}
     */
    get group() {
        return this.state.group;
    };

    /**
     * Set parent group
     *
     * @param {Object} value
     */
    set group(value) {
        this.state.group = value;
    };

    /**
     * Calculate group item total
     *
     * Loops through all line items and adds up the total
     *
     * @returns {Decimal}
     */
    calculateTotal() {
        let total = Utils.getDecimal('0.00');
        for (let line_item of this.state.line_items.values()) {
            total = total.plus(line_item.item.total);
        }
        return total;
    };

    /**
     * Trigger recalculation of total
     */
    recalculateTotal() {
        this.updateTotal();
    };

    /**
     * Update internal total value for all line items
     *
     * This function is designed to bubble upwards to the main panel
     *
     * @param {boolean} [update_group=true] - Determines if group is notified of change
     */
    updateTotal(update_group = true) {
        this.state.total = this.calculateTotal();
        if (this.isBooted()) {
            this.renderTitle();
        }
        if (update_group) {
            // tell group about the change
            this.group.updateTotal(this.id);
        }
    };

    /**
     * Get group item total
     *
     * @returns {Decimal}
     */
    getTotal() {
        return this.state.total;
    };

    /**
     * Set subtotal using the previous group items value and adding this items total to it
     *
     * This subtotal is used when calculating other group items down the chain
     *
     * @param {Decimal} subtotal
     */
    setSubtotal(subtotal) {
        this.state.subtotal = subtotal.plus(this.getTotal());
    };

    /**
     * Get subtotal
     *
     * @returns {Decimal}
     */
    getSubtotal() {
        return this.state.subtotal;
    };

    /**
     * Get accordion item title
     *
     * @returns {object}
     */
    getTitle() {
        return title_tpl({
            title: this.state.title,
            unsaved: this.state.title === 'Unsaved Section',
            total: Utils.formatCurrency(this.state.total)
        });
    };

    /**
     * Set accordion item title
     */
    renderTitle() {
        this.state.item.setTitle(this.getTitle());
    };

    /**
     * Set accordion item title
     *
     * @param {string} title
     */
    setTitle(title) {
        this.state.title = title;
        if (this.isBooted()) {
            this.renderTitle();
        }
    };

    /**
     * Get title of list item from line item
     *
     * @param {Object} item
     * @returns {string}
     */
    getListItemTitleFromLineItem(item) {
        let components = false,
            price_overwritten = false;
        if (item.type === LineItem.Type.PRODUCT) {
            components = item.hasComponents();
            price_overwritten = item.isPriceOverridden();
        }
        return line_item_tpl({
            name: item.name,
            quantity: item.quantity.toString(),
            price: item.getAmount(),
            total: item.getTotal(),
            show_icons: components || price_overwritten,
            components,
            price_overwritten
        });
    };

    /**
     * Add line item or update item title
     *
     * @param {Object} info
     */
    renderLineItem(info) {
        let list = this.getList(),
            title = this.getListItemTitleFromLineItem(info.item);
        if (info.list_item_id === undefined) {
            info.list_item_id = list.addItem(title, {
                line_item: info.item
            });
            return;
        }
        list.setItemLabel(info.list_item_id, title);
    };

    /**
     * Handle incoming line item, if it doesn't exist in the internal cache, then add it to the list. otherwise, update
     * the existing list item
     *
     * @param item
     * @param {boolean} [update_total=true] - Determines if group item total is updated
     */
    handleLineItem(item, update_total = true) {
        let info = this.state.line_items.get(item.id);
        if (info === undefined) {
            // create
            info = {
                item: item
            };
            if (this.isBooted()) {
                this.renderLineItem(info);
            }
            this.state.line_items.set(item.id, info);
        } else {
            // update
            this.renderLineItem(info);
        }
        if (update_total) {
            this.updateTotal();
        }
    };

    /**
     * Delete line item from internal cache and remove from list
     *
     * @param {string} item_id - Line item uuid
     */
    deleteLineItem(item_id) {
        let info = this.state.line_items.get(item_id);
        if (info === undefined) {
            return;
        }
        if (info.list_item_id !== undefined) {
            this.getList().deleteItem(info.list_item_id);
        }
        this.state.line_items.delete(item_id);
        this.updateTotal();
    };

    /**
     * Delete item
     */
    delete() {
        this.getList().delete();
        if (this.isBooted()) {
            this.state.item.delete();
        }
        this.group.deleteItem(this.state.id);
    };

    /**
     * Determine if group item is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot group item
     */
    boot() {
        let list = this.getList();
        for (let info of this.state.line_items.values()) {
            this.renderLineItem(info);
        }
        this.state.item = new Accordion.Item(this.getTitle(), list.render());
        this.state.item.on('booted', (event) => {
            list.boot(event.item.elem.panel);
        });
        this.group.accordion.addItem(this.state.item);

        this.state.booted = true;
    };
}

module.exports = Base;
