<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SuccessManager extends Model
{
    use SoftDeletes;

    protected $table = 'successManagers';
    protected $primaryKey = 'successManagerID';
    protected $fillable = [
        'name', 'isAutoAssign', 'isAvailable', 'zendeskGroupName', 'hubspotCompanyOwnerID', 'hubspotContactOwnerID',
        'hubspotDealOwnerID', 'hubspotScheduleDemoLink'
    ];
    protected $casts = [
        'isAutoAssign' => 'bool',
        'isAvailable' => 'bool',
    ];

    public function scopeAutoAssign($query)
    {
        return $query->where('isAutoAssign', 1);
    }
}
