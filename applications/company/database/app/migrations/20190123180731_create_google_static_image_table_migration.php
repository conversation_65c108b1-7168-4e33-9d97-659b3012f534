<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CreateGoogleStaticImageTableMigration extends Migration
{
    public function up()
    {
        $this->newTable('googleStaticImages')
            ->primaryKey('googleStaticImageID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->unsignedTinyInteger('status');
                $table->text('params');
                $table->string('contentType', 100)->nullable();
                $table->unsignedBigInteger('size')->nullable();
                $table->unsignedSmallInteger('hits');
                $table->dateTime('expiresAt');
            })
            ->column('paramsHash', Table::COLUMN_TYPE_BINARY, [
                'length' => 20,
                'after' => 'params'
            ])
            ->indexes(function (Blueprint $table, Table $schema_table) {
                // only add unique index on main table since history will have multiple entries
                if (!$schema_table->isHistory()) {
                    $table->unique(['type', 'paramsHash'], 'type_param_hash_index');
                }
            })
            ->timestamps(true, false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['googleStaticImages'], true);
    }
}
