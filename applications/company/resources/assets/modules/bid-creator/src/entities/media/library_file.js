/**
 * @module BidCreator/Entities/Media/LibraryFile
 */

'use strict';

const lang = require('lodash/lang');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Base = require('./base');

/**
 * @alias module:BidCreator/Entities/Media/LibraryFile
 */
class LibraryFile extends Base {
    /**
     * Constructor
     *
     * @param {Object} data
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super(data);
        let item = null;
        if (lang.isObject(data)) {
            this.fill(['file_id'], data);
            if (lang.isObject(data.item)) {
                item = LibraryFile.cacheEntity(data.item);
            }
        }
        Object.assign(this.state, {
            type: Base.Type.LIBRARY_FILE,
            item: item
        });
        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Cache media entity
     *
     * Stores entities in static cache which any library file can use to help prevent unnecessary calls to the server to
     * get individual media library entities
     *
     * @param {(Object|Api.Entity)} entity
     * @returns {Api.Entity}
     */
    static cacheEntity(entity) {
        if (!(entity instanceof Api.Entity)) {
            entity = new Api.Entity(entity);
        }
        LibraryFile.__cache.set(entity.get('id'), entity);
        return entity;
    };

    /**
     * Get entity by id
     *
     * If entity is already in the internal cache, we return that otherwise an API call is made to retrieve, cache, and
     * return the entity.
     *
     * @param {string} id
     * @returns {Promise}
     */
    static getEntity(id) {
        let entity = LibraryFile.__cache.get(id);
        if (entity !== undefined) {
            return Promise.resolve(entity);
        }
        return new Promise((resolve, reject) => {
            Api.Resources.Media().accept('application/vnd.adg.fx.bid-v1+json').retrieve(id).then((entity) => {
                this.cacheEntity(entity);
                resolve(entity);
            }, (error) => {
                reject(error);
            });
        });
    };

    /**
     * Get file id
     *
     * Alias for item_id just to make this entity make a little more sense
     *
     * @readonly
     *
     * @returns {?string}
     */
    get file_id() {
        return this.item_id;
    };

    /**
     * Set file id
     *
     * This is an alias for the item id which cannot be set directly
     *
     * @param {string} value - Uuid
     */
    set file_id(value) {
        this.setState({
            item_id: value
        });
    };

    /**
     * Get name
     *
     * @returns {string}
     */
    get name() {
        return this.state.item.get('name');
    };

    /**
     * Prepare entity
     *
     * If the media entity exists for this item, then we validate otherwise we fetch and cache the entity then validate.
     * Makes sure we have any prerequisite data before validation.
     *
     * @returns {Promise}
     */
    prepare() {
        return new Promise((resolve, reject) => {
            if (this.state.item !== null) {
                resolve(this);
                return;
            }
            LibraryFile.getEntity(this.item_id).then((entity) => {
                this.state.item = entity;
                resolve(this);
            }, (error) => {
                reject(error);
            });
        });
    };
}

LibraryFile.__cache = new Map;

module.exports = LibraryFile;
