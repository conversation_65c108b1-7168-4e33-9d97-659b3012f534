<?php

declare(strict_types=1);

namespace Common\Models;

use App\Resources\LeadFormFieldResource;
use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Core\Components\Http\StaticAccessors\URI;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class LeadForm extends Model
{
    use UuidTrait;

    protected $table = 'leadForms';
    protected $primaryKey = 'leadFormID';
    protected $fillable = ['leadFormID', 'token', 'companyID', 'title', 'saveButtonLabel', 'defaultAssignedToUserID',  'isActive'];

    /**
     * Convert the model instance to a customized array structure dedicated to render
     *
     * @return array
     */
    public function toRenderArray()
    {
        return [
            'form_url' => URI::route('lead-forms-action.ingest')->build(),
            'token' => $this->token,
            'company_id' => $this->companyID,
            'title' => $this->title,
            'save_button_label' => $this->saveButtonLabel,
            'is_active' => $this->isActive,
            'fields' => $this->fields->map(function ($field) {
                return [
                    'reference' => $field->reference,
                    'field_type' => $field->fieldType,
                    'label' => $field->label,
                    'is_enabled' => $field->isEnabled,
                    'is_required' => $field->isRequired,
                ];
            })->toArray(),
        ];
    }

    public function getMarketingSourceField()
    {
        return $this->fields->where('reference', LeadFormFieldResource::REFERENCE_MARKETING_SOURCE)->first();
    }

    public function getProjectTypeField()
    {
        return $this->fields->where('reference', LeadFormFieldResource::REFERENCE_PROJECT_TYPE)->first();
    }

    /**
     * Relationship with Company
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID');
    }

    public function fields()
    {
        return $this->hasMany(LeadFormField::class, 'leadFormID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

}