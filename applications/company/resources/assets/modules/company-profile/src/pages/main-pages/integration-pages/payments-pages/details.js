'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Tooltip = require('@ca-submodule/tooltip');
const {findChild, jsSelector} = require("@ca-package/dom");
const {createErrorMessage} = require("@cas-notification-toast-js/message/error");
const {createSuccessMessage} = require("@cas-notification-toast-js/message/success");

const payments_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/payments-pages/details.hbs');
const $ = require("jquery");
const {
    APPLICATION_STATUS_PENDING_SIGNATURE,
    IS_READY_TO_PROCESS_TOOLTIP_CONTENT_MAP,
    formatApplicationStatus,
    formatReadyToProcessStatus,
    formatGatewayStatus
} = require("@cam-company-profile-js/pages/main-pages/integration-pages/payments-pages/utilities/constants");

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations'),
            is_payments_active: false,
            ach_acceptance_enabled: false,
            credit_card_acceptance_enabled: false,
            merchant_data: null,
            payment_link: null,
            credit_card_processing_fee: 0.0,
            signature_url: null,
        });
    }

    get status () {
        return new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);
    };


    updateReadyToProcessDisplay(status_id) {
        // Update status display
        if (this.elem.payments_ready_to_process_status) {
            this.elem.payments_ready_to_process_status.html(formatReadyToProcessStatus(status_id));
        }

        // Update tooltip text
        if (this.elem.ready_to_process_tooltip) {
            const new_tooltip_text = IS_READY_TO_PROCESS_TOOLTIP_CONTENT_MAP[status_id] || 'Status unknown';
            if (!Tooltip.updateContent(this.elem.ready_to_process_tooltip, new_tooltip_text)) {
                Tooltip.init(this.elem.ready_to_process_tooltip, {
                    content: new_tooltip_text
                });
            }
        }
    }

    toggleStateDisplay() {
        const has_merchant_data = this.state.merchant_data && Object.keys(this.state.merchant_data).length > 0;
        
        if (has_merchant_data) {
            this.elem.sections.signup?.addClass('t-hidden');
            this.elem.sections.status?.removeClass('t-hidden');
            
            if (this.state.merchant_data.status) {
                this.elem.payments_application_status?.html(formatApplicationStatus(this.state.merchant_data.status));
            }
            if (this.state.merchant_data.gateway_status) {
                this.elem.payments_gateway_status?.html(formatGatewayStatus(this.state.merchant_data.gateway_status));
            }
            if (this.state.merchant_data.is_ready_to_process !== undefined) {
                this.updateReadyToProcessDisplay(this.state.merchant_data.is_ready_to_process);
            }
        } else {
            this.elem.sections.signup?.removeClass('t-hidden');
            this.elem.sections.status?.addClass('t-hidden');
        }
        
        // Toggle header buttons
        this.elem.get_started_button?.toggleClass('t-hidden', has_merchant_data);
        this.elem.settings?.toggleClass('t-hidden', !has_merchant_data);

        // Toggle signature section visibility
        this.toggleSignatureSection();
    }

    toggleSignatureSection() {
        const should_show_signature = this.state.merchant_data &&
            this.state.merchant_data.status === APPLICATION_STATUS_PENDING_SIGNATURE &&
            this.state.merchant_data.signature_url;

        if (should_show_signature) {
            this.elem.signature_section?.removeClass('t-hidden');
        } else {
            this.elem.signature_section?.addClass('t-hidden');
        }
    }

    async fetchPaymentSettings() {
        try {
            const response = await $.ajax({
                url: '/api/integration/payments/settings',
                type: Api.Request.Method.GET,
                contentType: 'application/json',
            });


            if (response) {
                this.state.is_payments_active = !!response.company_settings.is_payments_active;
                this.state.ach_acceptance_enabled = response.company_settings.ach_acceptance_enabled;
                this.state.credit_card_acceptance_enabled = response.company_settings.credit_card_acceptance_enabled;
                this.state.credit_card_processing_fee = response.company_settings.credit_card_processing_fee;
                this.state.merchant_data = response.merchant_data;
                this.state.payment_link = response.action_url;

                this.updatePaymentStatusDisplay();
                
            }
        } catch (error) {
            if (error.status !== 404) {
                console.error('Error fetching payment settings:', error);
                let message = createErrorMessage('Failed to load payment settings');
                this.router.main_route.layout.toasts.addMessage(message);
            }
        }

        this.toggleStateDisplay();
    }

    updatePaymentStatusDisplay() {
        if (this.elem.is_payments_active) {
            this.elem.is_payments_active.html(
                this.status.get(this.state.is_payments_active)
            );
        }

        if (this.elem.accept_credit_card_status) {
            this.elem.accept_credit_card_status.html(
                this.status.get(this.state.credit_card_acceptance_enabled)
            );
        }

        if (this.elem.accept_ach_status) {
            let ach_status =  this.status.get(this.state.ach_acceptance_enabled);
            if (!this.state.merchant_data.is_ach_available) {
                ach_status = '<i>Additional Setup Required - Contact Support</i>'
            }
            this.elem.accept_ach_status.html(ach_status);
        }

        // Update credit card processing fee display
        if (this.elem.credit_card_processing_fee) {
            this.elem.credit_card_processing_fee.text(`${this.state.credit_card_processing_fee}%`);
        }

        // Update payment link display
        if (this.elem.payment_link && this.state.payment_link) {
            this.elem.payment_link_section.show();
            this.elem.payment_link.val(this.state.payment_link);
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        this.state.integrations.showLoader();
        await this.fetchPaymentSettings();
        this.state.integrations.hideLoader();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    setupListeners() {
        this.elem.get_started_button?.on('click', () => {
            this.router.navigate('integrations.payments.setup');
        });

        this.elem.settings?.on('click', (e) => {
            e.preventDefault();
            this.router.navigate('integrations.payments.settings');
            return false;
        });


        this.elem.copy_payment_link_button?.on('click', (e) => {
            e.preventDefault();
            
            const copy_promise = (navigator.clipboard && window.isSecureContext) 
                ? navigator.clipboard.writeText(this.state.payment_link)
                : Promise.reject();
            
            copy_promise
                .then(() => {
                    this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Payment link copied to clipboard'));
                })
                .catch(() => {
                    // Fallback for browsers that don't support clipboard API
                    const textArea = $('<textarea>');
                    textArea.val(this.state.payment_link);
                    $('body').append(textArea);
                    textArea[0].select();
                    textArea[0].setSelectionRange(0, 99999);

                    try {
                        document.execCommand('copy');
                        this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Payment link copied to clipboard'));
                    } catch (err) {
                        this.router.main_route.layout.toasts.addMessage(createErrorMessage('Failed to copy payment link'));
                    }

                    textArea.remove();
                });
            return false;
        });

        this.elem.signature_button?.on('click', (e) => {
            e.preventDefault();
            
            if (this.state.merchant_data?.signature_url) {
                window.open(this.state.merchant_data.signature_url, '_blank');
            }
            
            return false;
        });
    }
    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        
        this.initializeElements(root);
        this.initializeComponents(root);
        this.setupListeners();
    }

    initializeElements(root) {
        // Page sections
        this.elem.sections = {
            signup: findChild(root, jsSelector('signup-section')),
            status: findChild(root, jsSelector('status-section')),
        };

        // Navigation buttons
        this.elem.get_started_button = findChild(root, jsSelector('get-started-button'));
        this.elem.settings = findChild(root, jsSelector('settings-button'));

        // Status elements
        this.elem.payments_application_status = findChild(root, jsSelector('payments-application-status'));
        this.elem.payments_gateway_status = findChild(root, jsSelector('payments-gateway-status'));
        this.elem.payments_ready_to_process_status = findChild(root, jsSelector('payments-ready-to-process-status'));
        this.elem.ready_to_process_tooltip = findChild(root, jsSelector('ready-to-process-tooltip'));
        this.elem.is_payments_active = findChild(root, jsSelector('is-payments-active'));
        this.elem.accept_ach_status = findChild(root, jsSelector('accept-ach-status'));
        this.elem.accept_credit_card_status = findChild(root, jsSelector('accept-credit-card-status'));
        this.elem.credit_card_processing_fee = findChild(root, jsSelector('credit-card-processing-fee'));

        // Payment link elements
        this.elem.payment_link_section = findChild(root, jsSelector('payment-link-section'));
        this.elem.payment_link = findChild(root, jsSelector('payment-link'));
        this.elem.copy_payment_link_button = findChild(root, jsSelector('copy-payment-link-button'));

        // Signature elements
        this.elem.signature_section = findChild(root, jsSelector('signature-section'));
        this.elem.signature_button = findChild(root, jsSelector('signature-button'));

    }

    initializeComponents(root) {
        Tooltip.initAll(root);
    }

    /**
     * Render page
     *
     * @returns {string}
     */

    render() {
        return payments_tpl({
            brand_name: profile_data.brand_name,
            bid_customization_route: 'bid_customization.content'
        });
    };
}

module.exports = Details;