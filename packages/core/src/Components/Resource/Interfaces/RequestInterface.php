<?php

namespace Core\Components\Resource\Interfaces;

use Throwable;

interface RequestInterface
{
    public function store($key, $value);

    public function storage($key = null, $default = null);

    public function prepare();

    public function handle();

    public function run();

    public function rollback();

    public function success();

    public function setException(Throwable $e);

    public function exception();

    public function response();

    public function close();
}
