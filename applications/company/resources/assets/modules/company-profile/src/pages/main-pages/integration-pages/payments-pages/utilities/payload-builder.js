'use strict';

const $ = require("jquery");
const {createErrorMessage} = require("@cas-notification-toast-js/message/error");
const {createSuccessMessage} = require("@cas-notification-toast-js/message/success");

function mapAccountType(account_type) {
    const mapping = {
        'CHECKING': 'BIZ',
        'SAVINGS': 'SAVINGS'
    };
    return mapping[account_type] || account_type;
}

function collectFormData(form_elem, mailing_visible) {
    const form_data = {};
    
    form_elem.find('input, select, textarea').each(function() {
        const $field = $(this);
        const name = $field.attr('name');
        let value = $field.val();

        if (name && value !== null && value !== undefined && value !== '') {
            if (!$field.prop('disabled')) {
                if (name.startsWith('mailing_') && !mailing_visible) {
                    return;
                }
                form_data[name] = value;
            }
        }
    });

    return form_data;
}

function buildAdditionalOwners(form_data, additional_owners_count) {
    const additional_owners = [];
    
    for (let i = 0; i < additional_owners_count; i++) {
        const first_name = form_data[`additional_owner_${i}_first_name`];
        const last_name = form_data[`additional_owner_${i}_last_name`];
        const ownership_pct = form_data[`additional_owner_${i}_ownership_pct`];

        if (first_name && last_name && ownership_pct) {
            additional_owners.push({
                ownerFirstName: first_name,
                ownerLastName: last_name,
                ownershipPct: parseFloat(ownership_pct)
            });
        }
    }
    
    return additional_owners;
}

function buildMerchantObject(form_data) {
    return {
        dbaName: form_data.dba_name,
        legalBusinessName: form_data.legal_business_name,
        taxFilingName: form_data.tax_filing_name,
        taxFilingMethod: form_data.tax_filing_method,
        businessStartDate: form_data.business_start_date,

        demographic: {
            websiteAddress: form_data.website_address,
            businessPhone: form_data.business_phone,

            businessAddress: {
                address1: form_data.business_address_line1,
                address2: form_data.business_address_line2,
                city: form_data.business_city,
                stateCd: form_data.business_state,
                zip: form_data.business_zip,
                countryCd: 'US'
            },

            mailingAddress: {
                address1: form_data.mailing_address_line1,
                address2: form_data.mailing_address_line2,
                city: form_data.mailing_city,
                stateCd: form_data.mailing_state,
                zip: form_data.mailing_zip,
                countryCd: 'US'
            }
        },

        merchantContactInfo: {
            contactName: form_data.contact_name,
            contactEmail: form_data.contact_email,
            contactPhone: form_data.contact_phone
        },

        ownership: {
            ownershipTypeCd: form_data.ownership_type_code,
            ownerOwnershipPct: form_data.owner_ownership_pct,
            driversLicenseNumber: form_data.drivers_license_number,
            driversLicenseStateCd: form_data.drivers_license_state,

            owner: {
                ownerName: form_data.owner_name,
                ownerTitle: form_data.owner_title,
                ownerPhone: form_data.owner_phone,
                ownerMobilePhone: form_data.owner_mobile_phone,
                ownerEmail: form_data.owner_email_address,
                ownerDob: form_data.owner_dob,
                ownerSSN: form_data.owner_ssn,

                ownerAddress: {
                    address1: form_data.owner_address_line1,
                    address2: form_data.owner_address_line2,
                    city: form_data.owner_city,
                    stateCd: form_data.owner_state,
                    zip: form_data.owner_zip,
                    countryCd: 'US'
                }
            }
        },

        bankDetail: {
            depositBank: {
                bankAcctNum: form_data.deposit_account_number,
                bankRoutingNum: form_data.deposit_routing_number,
                bankAcctTypeCd: form_data.deposit_account_type,
                bankName: form_data.deposit_bank_name
            },

            withdrawalBank: {
                bankAcctNum: form_data.withdrawal_account_number,
                bankRoutingNum: form_data.withdrawal_routing_number,
                bankAcctTypeCd: form_data.withdrawal_account_type,
                bankName: form_data.withdrawal_bank_name
            }
        },

        processing: {
            platformDetails: {
                taxId: form_data.tax_id ? form_data.tax_id.replace(/-/g, '') : null
            },
            volumeDetails: {
                averageMonthlyVolume: form_data.average_monthly_volume,
                highTicketAmount: form_data.high_ticket_amount,
                averageTicketAmount: form_data.average_ticket_amount
            }
        }
    };
}

function getSetupPayload(elements, state) {
    const is_mailing_visible = elements.mailing_address_group && !elements.mailing_address_group.hasClass('t-hidden');
    const form_data = collectFormData(elements.setup_form, is_mailing_visible);

    if (!form_data.mailing_address_line1) {
        form_data.mailing_address_line1 = form_data.business_address_line1;
        form_data.mailing_address_line2 = form_data.business_address_line2;
        form_data.mailing_city = form_data.business_city;
        form_data.mailing_state = form_data.business_state;
        form_data.mailing_zip = form_data.business_zip;
        form_data.mailing_country = form_data.business_country;
    }

    // Build additional owners
    const additional_owners_visible = elements.additional_owners_group && !elements.additional_owners_group.hasClass('t-hidden');
    const additional_owners = additional_owners_visible ? 
        buildAdditionalOwners(form_data, state.additional_owners_count) : [];

    // Build final payload
    const payload = {
        ownerSiteUser: {
            firstName: form_data.owner_first_name,
            lastName: form_data.owner_last_name,
            email: form_data.owner_email
        },
        merchant: buildMerchantObject(form_data)
    };

    console.log(form_data);

    if (additional_owners.length > 0) {
        payload.merchant.ownership.additionalOwners = additional_owners;
    }

    return payload;
}

async function submitApplication(elements, router, data) {
    elements.loader?.show();
    elements.go_back_button?.prop('disabled', true);
    elements.submit_setup_button?.prop('disabled', true);

    try {
        const response = await $.ajax({
            url: '/api/integration/payments/copilot/merchant',
            type: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            statusCode: {
                201: function(response) {
                    // Explicitly handle 201 Created response as success
                    console.log('Merchant created successfully:', response);
                    let message = createSuccessMessage('Application submitted successfully');
                    router.main_route.layout.toasts.addMessage(message);
                    setTimeout(() => {
                        router.navigate('integrations.payments.details');
                    }, 1000);
                },
                400: function(xhr) {
                    // Handle validation errors
                    console.error('Validation error:', xhr.responseJSON);
                    const errorData = xhr.responseJSON;
                    let errorMessage = 'Please check your form data and try again.';
                    
                    if (errorData && errorData.error) {
                        errorMessage = errorData.error;
                    }

                    let message = createErrorMessage(errorMessage);
                    router.main_route.layout.toasts.addMessage(message);
                },
                409: function(xhr) {
                    // Handle merchant already exists
                    console.error('Merchant already exists:', xhr.responseJSON);
                    let message = createErrorMessage('A merchant already exists for this company.');
                    router.main_route.layout.toasts.addMessage(message);
                },
                500: function(xhr) {
                    // Handle server errors
                    console.error('Server error:', xhr.responseJSON);
                    let message = createErrorMessage('Server error occurred. Please try again later.');
                    router.main_route.layout.toasts.addMessage(message);
                }
            }

        });
    } catch (error) {
        console.error('Error submitting application:', error);
        
        // Handle actual errors
        let errorMessage = 'Failed to submit application. Please try again.';
        
        if (error.responseJSON && error.responseJSON.error) {
            errorMessage = error.responseJSON.error;
        } else if (error.responseText) {
            try {
                const errorData = JSON.parse(error.responseText);
                if (errorData.error) {
                    errorMessage = errorData.error;
                }
            } catch (parseError) {
                console.warn('Could not parse error response:', parseError);
            }
        }
        
        let message = createErrorMessage(errorMessage);
        router.main_route.layout.toasts.addMessage(message);
    } finally {
        elements.loader?.hide();
        elements.go_back_button?.prop('disabled', false);
        elements.submit_setup_button?.prop('disabled', false);
    }
}

module.exports = {
    getSetupPayload,
    submitApplication
};