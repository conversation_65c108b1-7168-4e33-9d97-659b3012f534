<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class CompanyAchPaymentMethod extends HistoryEntityModel implements Interfaces\CompanyAchPaymentMethodInterface
{
    use Common\CompanyAchPaymentMethodCommon;

    protected $table = 'companyAchPaymentMethods';
    protected $primaryKey = 'companyAchPaymentMethodID';
    protected $fillable = [
        'accountNumber', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUser<PERSON>', 'deletedAt', 'deletedByUserID'
    ];
}
