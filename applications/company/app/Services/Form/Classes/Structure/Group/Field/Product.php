<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Structure\Group\Field;

use App\Interfaces\Resource\FormFieldProductResourceInterface;
use App\Resources\Form\Item\Group\Field\ProductResource;
use App\Services\Form\Exceptions\{FormException, PersistException, ValidationException};
use App\Services\Form\Components\{FieldOptions\ProductOption, Fields\ProductListField};
use App\Services\Form\Interfaces\OrderableInterface;
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait, OrderableTrait};
use Core\Components\Resource\Classes\Entity;
use InvalidArgumentException;
use Throwable;

/**
 * Class Product
 *
 * @package App\Services\Form\Classes\Structure\Group\Field
 */
class Product implements OrderableInterface
{
    use ArrayImportExportTrait;
    use IdentifierTrait;
    use OrderableTrait;

    /**
     * @var array Mapping of type aliases to type
     */
    protected static array $type_alias_map = [
        'product-category' => ProductResource::TYPE_PRODUCT_CATEGORY,
        'product-item' => ProductResource::TYPE_PRODUCT_ITEM
    ];

    /**
     * @var array Mapping of action aliases to action
     */
    protected static array $action_alias_map = [
        'add' => ProductResource::ACTION_ADD,
        'remove' => ProductResource::ACTION_REMOVE
    ];

    /**
     * @var ProductListField Parent field
     */
    protected ProductListField $field;

    /**
     * @var int Product type (category or item)
     */
    protected ?int $item_type = null;

    /**
     * @var string Id of product category or item
     */
    protected ?string $item_id = null;

    /**
     * @var string|null Alias of product category to lookup
     */
    protected ?string $product_category_alias = null;

    /**
     * @var string|null Alias of product item to lookup
     */
    protected ?string $product_item_alias = null;

    /**
     * @var int Determines if product item or category is added/removed
     */
    protected int $action = ProductResource::ACTION_ADD;

    /**
     * Create product option from array
     *
     * @param ProductListField $field
     * @param array $data
     * @return static
     * @throws FormException
     */
    public static function import(ProductListField $field, array $data): self
    {
        return ProductOption::make($field, $data);
    }

    /**
     * Create option and assign to field
     *
     * Used for easy chaining while building forms.
     *
     * @param ProductListField $field
     * @param array $data
     * @return static
     * @throws FormException
     */
    public static function make(ProductListField $field, array $data = [])
    {
        $product = new static($field, $data);
        $field->addProduct($product);
        return $product;
    }

    /**
     * Product constructor
     *
     * @param ProductListField $field
     * @param array $data
     * @throws FormException
     */
    public function __construct(ProductListField $field, array $data = [])
    {
        $this->field = $field;
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Fill out product from array
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'id' => ['string', 'setID'],
            'type' => ['int', 'setItemType'], // alias for item_type
            'item_type' => ['int', 'setItemType'],
            'item_id' => ['string', 'setItemID'],
            'product-category' => ['string', 'setProductCategoryAlias'],
            'product-item' => ['string', 'setProductItemAlias'],
            'action' => ['int', 'setAction'],
            'order' => ['int', 'setOrder']
        ], $data, [
            'type' => static::$type_alias_map,
            'action' => static::$action_alias_map
        ]);
    }

    /**
     * Set item type
     *
     * Used for polymorphic relation to determine if we pull a category or item.
     *
     * @param int $type
     * @return $this
     */
    public function setItemType(int $type): self
    {
        $this->item_type = $type;
        return $this;
    }

    /**
     * Get item type
     *
     * @return int|null
     */
    public function getItemType(): ?int
    {
        return $this->item_type;
    }

    /**
     * Set item id
     *
     * This is the id of the category or item.
     *
     * @param string $id
     * @return $this
     */
    public function setItemID(string $id): self
    {
        $this->item_id = $id;
        return $this;
    }

    /**
     * Get item id
     *
     * @return string
     */
    public function getItemID(): ?string
    {
        return $this->item_id;
    }

    /**
     * Shorthand option to configure product for a specific category
     *
     * @param string $id
     * @return $this
     */
    public function setProductCategoryID(string $id): self
    {
        $this->setItemType(ProductResource::TYPE_PRODUCT_CATEGORY);
        $this->setItemID($id);
        return $this;
    }

    /**
     * Set product category alias to lookup
     *
     * Will be used to pull a product category and then configure this product during save.
     *
     * @param string|null $alias
     * @return $this
     */
    public function setProductCategoryAlias(?string $alias): self
    {
        $this->setItemType(ProductResource::TYPE_PRODUCT_CATEGORY);
        $this->product_category_alias = $alias;
        return $this;
    }

    /**
     * Get product category alias
     *
     * @return string|null
     */
    public function getProductCategoryAlias(): ?string
    {
        return $this->product_category_alias;
    }

    /**
     * Shorthand option to configure product for a specific category
     *
     * @param string $id
     * @return $this
     */
    public function setProductItemID(string $id): self
    {
        $this->setItemType(ProductResource::TYPE_PRODUCT_ITEM);
        $this->setItemID($id);
        return $this;
    }

    /**
     * Set product item alias to lookup
     *
     * Will be used to pull a product item and then configure this product during save.
     *
     * @param string|null $alias
     * @return $this
     */
    public function setProductItemAlias(?string $alias): self
    {
        $this->setItemType(ProductResource::TYPE_PRODUCT_ITEM);
        $this->product_item_alias = $alias;
        return $this;
    }

    /**
     * Get product item alias
     *
     * @return string|null
     */
    public function getProductItemAlias(): ?string
    {
        return $this->product_item_alias;
    }

    /**
     * Set action
     *
     * @param int $action
     * @return $this
     */
    public function setAction(int $action): self
    {
        if (!in_array($action, ProductResource::getActions())) {
            throw new InvalidArgumentException('Invalid action');
        }
        $this->action = $action;
        return $this;
    }

    /**
     * Set action as add
     *
     * Will add the category or item to the list.
     *
     * @return $this
     */
    public function setActionAdd(): self
    {
        return $this->setAction(ProductResource::ACTION_ADD);
    }

    /**
     * Set action as remove
     *
     * Will remove the category or item from the list. Will work at any nested depth.
     *
     * @return $this
     */
    public function setActionRemove(): self
    {
        return $this->setAction(ProductResource::ACTION_REMOVE);
    }

    /**
     * Get action
     *
     * @return int
     */
    public function getAction(): int
    {
        return $this->action;
    }

    /**
     * Get location within array format for use with debugging
     *
     * @return string
     */
    public function getLocation(): string
    {
        return "{$this->field->getLocation()}.products.{$this->field->getProductIndex($this)}";
    }

    /**
     * Prepare product before saving
     *
     * If product category or item alias is defined, then it is requested via the product info helper. If order isn't
     * defined, it is set using position from parent.
     */
    public function prepare(): void
    {
        if (($category_alias = $this->getProductCategoryAlias()) !== null) {
            $this->field->getParent()->getProductInfoHelper()->requestCategory($category_alias);
        } elseif (($item_alias = $this->getProductItemAlias()) !== null) {
            $this->field->getParent()->getProductInfoHelper()->requestItem($item_alias);
        }
        if ($this->getOrder() === null) {
            $this->setOrder($this->field->getProductIndex($this) + 1);
        }
    }

    /**
     * Validate product before saving
     *
     * Both product category and item alias cannot be defined on same product instance. If alias is set for either type,
     * they are fetched from the product item helper to verify their existence.
     *
     * @throws ValidationException
     * @throws FormException
     */
    public function validate(): void
    {
        $category_alias = $this->getProductCategoryAlias();
        $item_alias = $this->getProductItemAlias();
        if ($category_alias !== null && $item_alias !== null) {
            throw new ValidationException('Only category or item alias can be used, not both');
        }
        $helper = $this->field->getParent()->getProductInfoHelper();
        if ($category_alias !== null) {
            if (($item_id = $helper->getCategory($category_alias)) === null) {
                throw new FormException('Unable to find product category with alias: %s', $category_alias);
            }
            $this->setItemID($item_id);
        }
        if ($item_alias !== null) {
            if (($item_id = $helper->getItem($item_alias)) === null) {
                throw new FormException('Unable to find product item with alias: %s', $item_alias);
            }
            $this->setItemID($item_id);
        }
    }

    /**
     * Get entity used to persist via resource
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        return Entity::make([
            'id' => $this->getID(),
            'field_id' => $this->field->getID(),
            'item_type' => $this->getItemType(),
            'item_id' => $this->getItemID(),
            'action' => $this->getAction(),
            'order' => $this->getOrder()
        ]);
    }

    /**
     * Persist product
     *
     * @param FormFieldProductResourceInterface $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FormFieldProductResourceInterface $resource)
    {
        try {
            $resource->updateOrCreate($this->getEntity())->nested()->run();
        } catch (Throwable $e) {
            throw (new PersistException('Unable to persist field product [%s]', $this->getLocation()))->setLastException($e);
        }
    }

    /**
     * Convert to array with any relationships
     *
     * Used as caching format.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->getID(),
            'item_type' => $this->getItemType(),
            'item_id' => $this->getItemID(),
            'action' => $this->getAction(),
            'order' => $this->getOrder()
        ];
    }

    /**
     * Export product to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        return $this->exportToArray([
            'type' => 'getItemType',
            'item_id' => 'getItemID',
            'product-category' => 'getProductCategoryAlias',
            'product-item' => 'getProductItemAlias',
            'action' => 'getAction'
        ], [
            'aliases' => [
                'type' => static::$type_alias_map,
                'action' => static::$action_alias_map
            ]
        ]);
    }
}
