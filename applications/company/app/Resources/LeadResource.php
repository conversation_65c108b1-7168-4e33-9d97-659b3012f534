<?php

declare(strict_types=1);

namespace App\Resources;

use App\ResourceDelegates\LeadDelegate;
use Core\Components\Resource\Classes\Entity;
use App\Traits\Resource\{UserActionTrackingTrait};
use Common\Models\Lead;
use Core\Components\Resource\Classes\Resource;

/**
 * Class LeadResource
 *
 * @package App\Resources
 */
class LeadResource extends Resource
{

    const STATUS_NEW = 1;
    const STATUS_WORKING = 2;
    const STATUS_CONVERTED = 3;
    const STATUS_DEAD = 4;

    const PRIORITY_HOT = 1;
    const PRIORITY_WARM = 2;
    const PRIORITY_COLD = 3;
    const PRIORITY_DEAD = 4;

    const ORIGIN_MANUAL_ADD = 1;
    const ORIGIN_WEBSITE_LEADS_FORM = 2;

    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;

    /**
     * @var string Table name
     */
    protected $table = 'leads';

    /**
     * @var string Model class name
     */
    protected $model = Lead::class;

    protected $generate_id = true;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Get available statuses
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [static::STATUS_NEW, static::STATUS_WORKING, static::STATUS_CONVERTED, static::STATUS_DEAD];
    }

    /**
     * Get available lead origins
     *
     * @return int[]
     */
    public static function getLeadOrigins(): array
    {
        return [static::ORIGIN_MANUAL_ADD, static::ORIGIN_WEBSITE_LEADS_FORM];
    }

    /**
     * Get available priorities
     *
     * @return array
     */
    public static function getPriorities(): array
    {
        return [static::PRIORITY_HOT, static::PRIORITY_WARM, static::PRIORITY_COLD, static::PRIORITY_DEAD];
    }

    /**
     * Get available status names
     *
     * @return array
     */
    public static function getStatusNames(): array
    {
        return [
            static::STATUS_NEW => 'New',
            static::STATUS_WORKING => 'Working',
            static::STATUS_CONVERTED => 'Converted',
            static::STATUS_DEAD => 'Dead'
        ];
    }


    /**
     * Get available lead origin names
     *
     * @return string[]
     */
    public static function getLeadOriginNames(): array
    {
        return [
            static::ORIGIN_MANUAL_ADD => 'Manual Add',
            static::ORIGIN_WEBSITE_LEADS_FORM => 'Website Leads Form'
        ];
    }

    /**
     * Get available priority names
     *
     * @return array
     */
    public static function getPriorityNames(): array
    {
        return [
            null => '',
            static::PRIORITY_HOT => 'Hot',
            static::PRIORITY_WARM => 'Warm',
            static::PRIORITY_COLD => 'Cold',
            static::PRIORITY_DEAD => 'Dead'
        ];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(LeadDelegate::class);
    }

    /**
     * Change a lead to converted status
     *
     * @param int $lead_id
     */
    public function convertLead(int $lead_id): void
    {
        $this->partialUpdate(Entity::make([
            'id' => $lead_id,
            'status' => static::STATUS_CONVERTED
        ]))->run();
    }
}
