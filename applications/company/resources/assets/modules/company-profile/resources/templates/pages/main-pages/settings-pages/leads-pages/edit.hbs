<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-company-settings m-leads-settings">
        <div class="c-cs-wrapper">
            <div class="c-csw-content space-between">
                <div class="f-field flex no-margin" data-js="switch-container">
                    <input class="f-f-input" type="checkbox" id="status_e" data-fx-form-input="switch" data-js="lead-form-status" style="visibility: hidden; position: absolute; left: -9999px;" data-parsley-multiple="status_e"><label for="status_e" class="f-f-switch"></label>
                    <h4 class="c-cswc-title">
                        Lead Website Form
                        <span data-tooltip data-type="info"> Allow leads to be captured from your website by embedding a form.</span>
                    </h4>
                </div>

            </div>
        </div>

        <div data-js="lead-form-description"></div>
        <p>Build a form that potential customers can fill out directly on your website. Once the form is submitted, the customer's information will automatically be added to the Leads section of {{brand_name}}. Simply design your form, copy the provided code, and paste it into any page on your website.</p>

        <div class="t-hidden" data-js="lead-form-settings">
            <div class="lead-form-settings">
                <form class="sidebar" data-js="form">
                    <div class="input-fields">
                        <div class="f-field">
                            <label class="cswc-field-title" for="title">Form Title</label>
                            <input class="f-f-input" type="text" data-js="title_label" id="title_label" name="title_label" value="Contact Us">
                        </div>

                        <div class="f-field">
                            <label class="cswc-field-title" for="save_button_label">Submit Button Label</label>
                            <input class="f-f-input" type="text" data-js="save_button_label" id="save_button_label" name="save_button_label" value="Submit">
                        </div>

                        <div class="f-field">
                            <label class="cswc-field-title" for="save_button_label">Default Assigned To
                                <span class="f-fl-optional">(Optional)</span>
                                <span data-tooltip data-type="info">
                                    Assign a default user to automatically receive new leads from the form. This user can reassign, convert, or edit the lead as needed.</span>
                                </label>
                            <select class="f-f-input" data-js="default_assigned_to" data-fx-form-input="static-dropdown"></select>
                        </div>

                    </div>

                    <div class="optional-inputs">
                        <h4>Optional Inputs</h4>
                        <div class="c-csw-content">
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="email_visibility" id="email_visibility">
                                    </div>
                                </div>

                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="email_options_title">Email</h4>
                                        <div class="t-hidden" data-js="email_options_container">
                                            <div class="f-field flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="email_requirement" id="email_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="email_requirement" class="f-f-switch"></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="phone_visibility" id="phone_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="phone_options_title">Phone #</h4>
                                        <div class="t-hidden" data-js="phone_options_container">
                                            <div class="f-field flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="phone_requirement" id="phone_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="phone_requirement" class="f-f-switch "></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="address_visibility" id="address_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="address_options_title">Address</h4>
                                        <div class="t-hidden" data-js="address_options_container">
                                            <div class="f-field flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="address_requirement" id="address_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="address_requirement" class="f-f-switch "></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="marketing_source_visibility" id="marketing_source_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="marketing_source_options_title">Marketing Source</h4>
                                    <div class="t-hidden" data-js="marketing_source_options_container">
                                        <div class="f-field flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="marketing_source_requirement" id="marketing_source_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="marketing_source_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-title" for="marketing_source_label">Marketing Source Label</label>
                                            <input class="f-f-input" type="text" data-js="marketing_source_label" id="marketing_source_label" name="marketing_source_label" value="Comments">
                                        </div>
                                        <div class="f-field spacing padding">
                                            <div class="f-field flex no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="marketing_source_dropdown" id="marketing_source_dropdown" name="marketing_source_options" value="2" checked>
                                                <label class="dropdown-tooltip" for="marketing_source_dropdown">Dropdown
                                                    <span data-tooltip data-type="info">Use your current Marketing Source dropdown list. This list can be edited in the Marketing section of your top navigation.</span>
                                                </label>
                                            </div>
                                            <div class="f-field flex no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="marketing_source_freeform" id="marketing_source_freeform" name="marketing_source_options" value="1">
                                                <label class="dropdown-tooltip" for="marketing_source_freeform">Freeform
                                                    <span data-tooltip data-type="info">Your customers answers will be shown in the notes section of the lead.</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="project_type_visibility" id="project_type_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="project_type_options_title">Project Type</h4>
                                    <div class="t-hidden" data-js="project_type_options_container">
                                        <div class="f-field flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="project_type_requirement" id="project_type_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="project_type_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="marketing_source_label">Project Type Label</label>
                                            <input class="f-f-input" type="text" data-js="project_type_label" id="project_type_label" name="project_type_label" value="Comments">
                                        </div>
                                        <div class="f-field spacing padding">
                                            <div class="f-field flex no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="project_type_dropdown" id="project_type_dropdown" name="project_type_options" value="2" checked>
                                                <label class="dropdown-tooltip" for="project_type_dropdown">Dropdown
                                                    <span data-tooltip data-type="info">Use your current Project Type dropdown list. This list can be edited in the Project section of your Company Settings.</span>
                                                </label>
                                            </div>
                                            <div class="f-field flex no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="project_type_freeform" id="project_type_freeform" name="project_type_options" value="1">
                                                <label class="dropdown-tooltip" for="project_type_freeform">Freeform
                                                    <span data-tooltip data-type="info">Your customers answers will be shown in the notes section of the lead.</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="customer_notes_visibility" id="customer_notes_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="customer_notes_options_title">Customer Notes</h4>
                                    <div class="t-hidden" data-js="customer_notes_options_container">
                                        <div class="f-field flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="customer_notes_requirement" id="customer_notes_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label  for="customer_notes_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="marketing_source_label">Customer Notes Label</label>
                                            <input class="f-f-input" type="text" data-js="customer_notes_label" id="customer_notes_label" name="customer_notes_label" value="Comments">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>

                <div class="content">
                    <div class="leads-form-edit-preview-container" data-js="form-preview-container"></div>
                </div>

            </div>
        </div>
    </div>
</div>