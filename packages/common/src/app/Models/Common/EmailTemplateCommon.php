<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\Interfaces\EmailTemplateInterface;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait EmailTemplateCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'source' => 'int',
        'type' => 'int',
        'isSendFromSalesperson' => 'bool',
        'status' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function owner()
    {
        return $this->morphTo('owner', 'ownerType', 'ownerID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", EmailTemplateInterface::STATUS_ACTIVE);
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where(function ($query) use ($company) {
            $query->where("{$this->table}.ownerType", EmailTemplateInterface::OWNER_COMPANY)
                ->where("{$this->table}.ownerID", $company);
        });
    }
}
