'use strict';

import filesize from 'filesize';
import lang from 'lodash/lang';
import Uppy from '@uppy/core';
import Dashboard from '@uppy/dashboard';
import XHRUpload from '@uppy/xhr-upload';

import Api from '@ca-package/api';
import Page from '@ca-package/router/src/page';
import {findChild, jsSelector, onClickDestroy, onClickWatcher, onEvent} from "@ca-package/dom";

import {Base as Table} from '@ca-submodule/table';

import {Delete as DeleteModal} from "@cam-setup-wizard-js/modals/media/delete";

import media_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/media.hbs';

export const mime_types = {
    'audio/mpeg3': 1,
    'audio/m4a': 1,
    'audio/ogg': 1,
    'audio/wav': 1,
    'image/jpeg': 2,
    'image/png': 2,
    'image/gif': 2,
    'video/mp4': 3,
    'video/m4v': 3,
    'video/quicktime': 3,
    'video/x-ms-wmv': 3,
    'video/x-msvideo': 3,
    'video/mpeg': 3,
    'video/ogg': 3,
    'video/webm': 3,
    'application/pdf': 4,
    'application/msword': 5,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 5,
    'application/vnd.ms-excel': 6,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 6,
    'application/vnd.ms-powerpoint': 7,
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 7,
    'application/vnd.ms-powerpoint.slideshow.macroenabled.12': 7,
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow': 7,
    'text/csv': 8,
    'text/plain': 9
};

// 1: Audio | 2: Image | 3: Video | 4: PDF | 5: Word | 6: Excel | 7: Powerpoint | 8: CSV | 9: Text | 10: Other
export const file_types = {
    1: 'music',
    2: 'picture',
    3: 'video',
    4: 'pdf',
    5: 'word',
    6: 'excel',
    7: 'empty',
    8: 'empty',
    9: 'empty',
    10: 'empty'
};

export const file_types_name = {
    1: 'Audio',
    2: 'Image',
    3: 'Video',
    4: 'PDF',
    5: 'Word',
    6: 'Excel',
    7: 'Powerpoint',
    8: 'CSV',
    9: 'Text',
    10: 'Other'
};

/**
 * @memberof module:SetupWizard/Pages/MainPages/StepPages
 */
export class Media extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            modals: {},
            previous: {
                route: 'step.products',
                step: parent.steps.PRODUCTS
            },
            next: {
                route: 'step.warranty_packet',
                step: parent.steps.WARRANTY_PACKET
            },
            table: null,
            setup_media: false,
            setup_media_completed_at: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            }
        });
    };

    /**
     * Get table
     *
     * @returns {object}
     */
    get table() {
        return this.state.table;
    };

    /**
     * Start doing work, show loader image
     */
    startWorking() {
        this.state.parent.startWorking();
    };

    /**
     * Stop doing work, hide loader image
     */
    resetWorking() {
        this.state.parent.resetWorking();
    };

    /**
     * Get delete modal
     *
     * @readonly
     *
     @returns {module:SetupWizard/Modals/Media/Delete}
     */
    get delete_modal() {
        if (this.state.modals.delete === undefined) {
            this.state.modals.delete = new DeleteModal(this);
        }
        return this.state.modals.delete;
    };

    /**
     * Trigger delete modal for row
     *
     * @param {Object} data - row data from table
     */
    handleDelete(data) {
        this.delete_modal.open(data);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.state.parent.getPageContainer().scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Get display type icon and text for table row display
     *
     * @param {string} mime_type
     */
    getDisplayType(mime_type) {
        let this_type = mime_types[mime_type],
            key = null,
            name = null;
        if (!lang.isUndefined(this_type)) {
            key = file_types[this_type];
            name = file_types_name[this_type];
        } else {
            key = file_types[10];
            name = file_types_name[10];
        }
        return `<span class="media-icon icon-file-${key}"></span> ${name}`;
    };

    /**
     * Save current step for company
     *
     * @returns {Promise}
     */
    async saveCompanyStep(type) {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: type.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(type.route);
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Save step when user clicks navigation button
     *
     * @param {string} endpoint
     * @param {boolean} unload
     * @returns {Promise}
     */
    async saveStep(endpoint, unload = false) {
        this.state.parent.startWorking();

        try {
            let response = await $.ajax({
                url: window.fx_url.API + `setup-wizard/${endpoint}`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: this.state.next.step
                })
            });
            if (endpoint === 'next') {
                this.state.setup_media_completed_at = response.mediaCompletedAt;
            }
            this.state.parent.resetWorking();
            if (!unload) {
                this.router.navigate(this.state.next.route);
            }
        } catch (error) {
            console.log(error);
            this.state.parent.resetWorking();
            this.setError('Unable to proceed to next step, please try again');
        }
    };

    /**
     * Change skip to next
     */
    changeSkipToNext() {
        this.state.parent.setSkipRoute(null);
        this.state.parent.setNextRoute(this.state.next.route);
        this.state.parent.changeNavState();
    };

    /**
     * Determine if step needs to be saved or skipped
     */
    handleNextClick() {
        let endpoint = this.state.setup_media ? 'next' : 'skip';

        if (endpoint === 'next' && this.state.setup_media_completed_at !== null) {
            this.saveCompanyStep(this.state.next);
            return;
        }
        this.saveStep(endpoint);
    };

    /**
     * Create the media datatable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                window.open(data.file_media_urls.original, '_blank');
            })
            .on('scope_change', (scope) => {
                if (this.state.table.getRows().length > 0) {
                    this.state.setup_media = true;
                    this.changeSkipToNext();
                }
            });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name'
            },
            size: {
                label: 'Size',
                value: (data) => {
                    return filesize(data.size);
                }
            },
            type: {
                label: 'File Type',
                value: (data) => {
                    return this.getDisplayType(data.file.content_type);
                },
            }
        });

        // set row action config
        this.state.table.setRowActions({
            preview: {
                label: 'Preview',
                action: (data) => {
                    window.open(data.file_media_urls.original, '_blank');
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: (data) => {
                    this.handleDelete(data);
                }
            }
        });

        this.state.table.setAjax(Api.Resources.Media, (request) => {
            let file_relations = {
                file_media_urls: {},
                file: {
                    fields: ['content_type']
                }
            };
            request.fields(['id', 'name', 'size'])
                .relations(file_relations).filter('status', Api.Constants.Media.Status.ACTIVE);
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.parent.scrollTopParent();
        this.state.parent.setBackRoute(this.state.previous.route);
        this.state.parent.setSkipRoute(this.state.next.route);
        this.state.parent.setNextRoute(null);
        await super.load(request, next);

        this.state.table.draw();

        let that = this;
        onClickWatcher(this.state.parent.getMainHeader(), jsSelector('button'), function () {
            let $this = $(this);
            if ($this.data('value') === 'next' || $this.data('value') === 'skip') {
                that.handleNextClick();
                return false;
            }
            if ($this.data('value') === 'back') {
                that.saveCompanyStep(that.state.previous);
                return false;
            }
        }, false);

        if (request.data.media) {
            this.state.setup_media = true;
            this.changeSkipToNext();
        }
        if (request.data.media_completed_at !== null) {
            this.state.setup_media_completed_at = request.data.media_completed_at;
        }
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.parent.setSkipRoute(null);
        onClickDestroy(this.state.parent.getMainHeader());

        if (this.state.setup_media && this.state.setup_media_completed_at === null) {
            this.saveStep('next', true);
        }

        request.data.media = this.state.setup_media;
        request.data.media_completed_at = this.state.setup_media_completed_at;

        this.state.parent.updateSetupData(request.data);
        await super.unload(request, next);
    };

    configureUppy() {
        let max_files = 25;
        this.state.uppy = Uppy({
            id: 'media',
            autoProceed: false,
            restrictions: {
                allowedFileTypes: [
                    'audio/mp3',
                    'audio/mpeg',
                    'audio/ogg',
                    'audio/wav',
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'video/ogg',
                    'video/mp4',
                    'video/m4v',
                    'video/quicktime',
                    'video/x-ms-wmv',
                    'video/x-msvideo',
                    'video/mpeg',
                    'video/webm',
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-powerpoint',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'application/vnd.ms-powerpoint.slideshow.macroenabled.12',
                    'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
                    'text/csv',
                    'text/plain'
                ],
                maxNumberOfFiles: max_files,
                maxFileSize: 209715200 // 200mb
            },
            onBeforeUpload: (files) => {
                let uploaded_files = Object.assign({}, files)
                Object.keys(uploaded_files).forEach(file_id => {
                    uploaded_files[file_id].meta['is_bid_media'] = true;
                });
                return uploaded_files;
            }
        })
            .use(XHRUpload, {
                endpoint: Api.Resources.Media().buildUrl(),
                method: 'POST',
                headers: {
                    Accept: 'application/vnd.adg.fx.collection-v1+json'
                },
                fieldName: 'file',
                getResponseData: () => ({}),
                getResponseError: (responseText, response) => {
                    if (response.status === 401) {
                        window.location.href = window.fx_pages.AUTH_LOGIN;
                        return;
                    }
                    let error = JSON.parse(responseText);
                    if (response.status === 422) {
                        let text = [];
                        for (let key of Object.keys(error.errors)) {
                            text.push(error.errors[key]);
                        }
                        return new Error(text.join(', '));
                    }
                    return new Error('Upload failed for unknown reason, please contact support');
                }
            })
            .use(Dashboard, {
                metaFields: [
                    {id: 'name', name: 'Name', placeholder: 'File name'}
                ],
                inline: false,
                locale: {
                    strings: {
                        editing: 'Now Editing %{file}',
                        dashboardWindowTitle: 'Uppy Dashboard Window (Press escape to close)',
                        dashboardTitle: 'Uppy Dashboard',
                    },
                },
                closeModalOnClickOutside: false,
                hideProgressAfterFinish: true,
                proudlyDisplayPoweredByUppy: false,
                note: `1-${max_files} files allowed per upload, up to 200MB each`,
                onRequestCloseModal: () => {
                    this.state.uppy_dashboard.closeModal();
                    let files = Object.assign({}, this.state.uppy.getState().files);
                    if (Object.keys(files).length === 0) {
                        this.state.uppy.reset();
                    }
                }
            });

        this.state.uppy_dashboard = this.state.uppy.getPlugin('Dashboard');

        this.state.uppy
            .on('upload-success', () => {
                this.state.table.draw();
            })
            .on('complete', () => {
                setTimeout(() => {
                    let close_modal = true;
                    let files = this.state.uppy.getState().files;
                    for (let id in files) {
                        let file = files[id];
                        let is_processing = file.progress.preprocess || file.progress.postprocess;
                        if (file.progress.uploadComplete && !is_processing && !file.error) {
                            this.state.uppy.removeFile(file.id);
                            continue;
                        }
                        close_modal = false;
                    }
                    if (this.state.uppy_dashboard.isModalOpen() && close_modal) {
                        this.state.uppy_dashboard.closeModal();
                    }
                }, 1000);
            });
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.table = findChild(root, jsSelector('table'));
        this.elem.add_media = findChild(root, jsSelector('add-media'));

        this.createTable();
        this.configureUppy();

        onEvent(this.elem.add_media, 'click', (e) => {
            e.preventDefault();
            this.state.uppy_dashboard.openModal();
            return false;
        });
    };

    /**
     * Render page
     */
    render() {
        return media_tpl();
    };
}
