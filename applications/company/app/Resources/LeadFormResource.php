<?php
declare(strict_types=1);
namespace App\Resources;

use App\ResourceDelegates\LeadFormDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\LeadForm;
use Core\Components\Resource\Classes\Resource;

/**
 * Class LeadFormResource
 *
 * @package App\Resources
 */
class LeadFormResource extends Resource
{
    const LABEL_DEFAULT_FORM_TITLE = 'Contact Us';
    const LABEL_DEFAULT_SAVE_BUTTON = 'Submit';


    protected $table = 'leadForms';
    protected $model = LeadForm::class;
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;
    protected $allow_no_user = false;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(LeadFormDelegate::class);
    }

}
