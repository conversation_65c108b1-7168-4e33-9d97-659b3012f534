<?php

declare(strict_types=1);

namespace Common\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;

/**
 * Class PhoneRule
 *
 * @package Common\Components\Validation\Rules
 */
class PhoneRule extends Rule
{
    /**
     * Convert phone number into (XXX) XXX-XXXX format
     *
     * @param string $phone
     * @return string
     */
    public static function formatUsPhoneNumber(string $phone): string
    {
        $phone = preg_replace('#[^0-9]#', '', $phone);
        return '(' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6, 4);
    }

    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return array_merge(parent::getMessages(), [
            'us_phone' => '{label} must contain 10 digits'
        ]);
    }

    /**
     * Verify field has 10 digits ignoring any non-numeric characters
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('us_phone')]
    public function usPhone(mixed $field): bool|string
    {
        $phone = preg_replace('#[^0-9]#', '', (string) $field);
        if (strlen($phone) === 10) {
            return true;
        }
        return 'us_phone';
    }

    /**
     * Format number in (XXX) XXX-XXXX format
     *
     * @param mixed $field
     * @return bool
     */
    #[RuleAttribute('us_phone_format')]
    public function usPhoneFormat(mixed &$field): bool
    {
        $field = self::formatUsPhoneNumber((string) $field);
        return true;
    }
}
