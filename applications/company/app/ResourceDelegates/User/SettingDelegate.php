<?php

namespace App\ResourceDelegates\User;

use App\Classes\Func;
use App\FileBuilders\Bid\File\Components;
use App\FileBuilders\Bid\File\Structure;
use App\Resources\Bid\Item\InstallmentPaymentTerm\InstallmentResource;
use App\Resources\User\SettingResource;
use App\Resources\EmailTemplateResource;
use App\Resources\UserResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\UserSetting;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validation;
use Core\Components\Validation\Classes\Validator;
use Core\Components\Validation\Rules\InternetRule;
use Core\Exceptions\AppException;

class SettingDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('user')->resource(UserResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->column('userSettingID')
            ->noSave();

        $list->field('user_id')
            ->column('userID', true)
            ->validation('User Id', 'required|type[int]|check_user_id')
            ->onAction([SettingResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction([SettingResource::ACTION_FILTER], function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[100]|check_name');

        $list->field('value')
            ->validation('Value', 'nullable|optional|dynamic_rules[check_value]')
            ->saveMutator(function ($value, Entity $entity) {
                return SettingResource::getQueryValue($entity->name, $value);
            })
            ->outputMutator(function ($value, UserSetting $setting) {
                return SettingResource::getOutputValue($setting->name, $value);
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, SettingResource $resource)
    {
        $rules->register('check_user_id', function ($id, $params, Validator $validator) use ($resource) {
            $user_resource = $resource->relationResource('user');
            if (($user = $user_resource->find($id)) === null) {
                return 'user_not_found';
            }
            try {
                $user_resource->isModelMutable($user);
            } catch (ImmutableEntityException $e) {
                return ['user_immutable', ['reason' => $e->getMessage()]];
            }

            $validator->getConfig()->store('user', $user);
            return true;
        }, [
            'user_not_found' => 'User not found',
            'user_immutable' => 'User immutable{reason}'
        ]);

        $rules->register('check_name', function ($name, $params, Validator $validator) use ($resource) {
//            if (preg_match('#^(?!_)[a-z0-9_]+(?<!_)$#', $name) !== 1) {
//                return 'name_invalid_format';
//            }
//            if (!$resource->isValidName($name)) {
//                return 'name_not_valid';
//            }
//            if ($validator->errors()->has('user_id')) {
//                return Rules::STOP;
//            }
//            if (($user_id = $validator->data('user_id')) === null) {
//                $user = $resource->acl()->user();
//                if ($user === null) {
//                    throw new AppException('User is required to check name for setting');
//                }
//                $company_id = $user->companyID;
//            }
//            $model = $validator->getconfig()->storage('_model');
//            if (
//                ($model === null || $model->name !== $name) &&
//                $resource->isNameInUse($company_id, $name)
//            ) {
//                return 'name_in_use';
//            }
            return true;
        }, [
            'name_invalid_format' => 'Name is not in the proper format',
            'name_not_valid' => 'Name is not valid',
            'name_in_use' => 'Name already in use'
        ]);


        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('check_value', function ($value, Validator $validator) {
            // if an error has occurred for the name, we skip validating the value until that is resolved since we can't
            // get the proper rules
            if ($validator->errors()->has('name')) {
                return Rules::STOP;
            }
            return SettingResource::getSettingValidationRules($validator->data('name'), $value);
        });
        return $config;
    }

//    public function queryScopeGlobal($query, SettingResource $resource)
//    {
//        $user = $resource->acl()->user();
//        if ($user !== null) {
//            $query->ofCompany($user->companyID);
//        }
//        return $query;
//    }

//    public function actionAllowed($action, SettingResource $resource)
//    {
//        $user = $resource->acl()->user();
//        if ($user === null || $user->primary) {
//            return true;
//        }
//        return false;
//    }

//    public function modelIsMutable(UserSetting $model, SettingResource $resource)
//    {
//        $user = $resource->acl()->user();
//        if ($user === null || ($user->primary && $user->companyID === $model->companyID)) {
//            return true;
//        }
//        return false;
//    }
}
