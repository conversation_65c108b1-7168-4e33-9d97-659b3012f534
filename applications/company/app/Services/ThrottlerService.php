<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Throttler\Exceptions\LockWaitTimeoutException;
use Core\Components\Cache\StaticAccessors\Cache;
use Illuminate\Contracts\Cache\LockTimeoutException;

/**
 * Class ThrottlerService
 *
 * @package App\Services
 */
class ThrottlerService
{
    /**
     * @var string Unique name of throttler
     */
    protected string $name;

    /**
     * @var int Number of times something can execute within defined time
     */
    protected int $limit;

    /**
     * @var int Timespan in milliseconds
     */
    protected int $milliseconds;

    /**
     * @var float Cache of the time per request (milliseconds / limit)
     */
    protected float $time_per_request;

    /**
     * @var string Cache of unique key for storage
     */
    protected string $storage_key;

    /**
     * ThrottlerService constructor
     *
     * @param string $name
     * @param int $limit
     * @param int $milliseconds
     */
    public function __construct(string $name, int $limit, int $milliseconds)
    {
        $this->name = $name;
        $this->limit = $limit;
        $this->milliseconds = $milliseconds;
        $this->time_per_request = (float) ($milliseconds / $limit);
        $this->storage_key = "{$name}:{$limit}:{$milliseconds}";
    }

    /**
     * Get last request
     *
     * @return float
     */
    protected function getLastRequest(): float
    {
        $value = Cache::get("{$this->storage_key}:LASTREQUEST", 0);
        return floatval($value);
    }

    /**
     * Set last request
     *
     * @param float $last_request
     */
    protected function setLastRequest(float $last_request): void
    {
        Cache::set("{$this->storage_key}:LASTREQUEST", $last_request);
    }

    /**
     * Get last ratio
     *
     * @return float
     */
    protected function getLastRatio(): float
    {
        $value = Cache::get("{$this->storage_key}:LASTRATIO", 0.0);
        return floatval($value);
    }

    /**
     * Set last ratio
     *
     * @param float $last_ratio
     */
    protected function setLastRatio(float $last_ratio): void
    {
        Cache::set("{$this->storage_key}:LASTRATIO", $last_ratio);
    }

    /**
     * Get new ratio based on last request
     *
     * @return float
     */
    protected function getNewRatio(): float
    {
        $last_request = $this->getLastRequest();
        $last_ratio = $this->getLastRatio();

        // get difference between requests in milliseconds
        $diff = (microtime(true) - $last_request) * 1000;

        $new_ratio = $last_ratio - $diff;
        $new_ratio = $new_ratio < 0 ? 0 : $new_ratio;
        $new_ratio += $this->time_per_request;

        return $new_ratio;
    }

    /**
     * Run throttler to delay execution between calls
     *
     * @return int Number of milliseconds waited
     * @throws \Core\Exceptions\AppException
     */
    public function run(): int
    {
        $wait = 0;
        $new_ratio = $this->getNewRatio();

        if ($new_ratio > $this->milliseconds) {
            $wait = (int) ceil($new_ratio - $this->milliseconds);
        }
        usleep($wait * 1000);

        try {
            Cache::lock("{$this->storage_key}:LOCK")->block(5, function () {
                $new_ratio = $this->getNewRatio();
                $this->setLastRatio($new_ratio);
                $this->setLastRequest(microtime(true));
            });
        } catch (LockTimeoutException $e) {
            throw (new LockWaitTimeoutException('Unable to establish lock for: %s', $this->storage_key))
                ->setLastException($e);
        }
        return $wait;
    }
}
