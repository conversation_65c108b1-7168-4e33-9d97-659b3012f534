<?php

namespace App\Services;

use App\Classes\Acl;
use App\Resources\Bid\ContentResource;
use App\Resources\ContentPartialResource;
use App\Resources\ContentTemplateResource;
use App\Resources\UnitResource;
use Carbon\Carbon;
use Common\Models\Warranty;
use App\Services\Form\Types\{CompanyType, SystemType};
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Exception;

class CompanyBidDefaultsService
{
    protected $company_id;

    protected $acl;

    protected $units = [
        'each' => [
            'name' => 'Each',
            'abbreviation' => 'Each'
        ],
        'ft' => [
            'name' => 'Foot',
            'abbreviation' => 'Ft'
        ],
        'sq-ft' => [
            'name' => 'Square Foot',
            'abbreviation' => 'Sq Ft'
        ],
        'cu-ft' => [
            'name' => 'Cubic Foot',
            'abbreviation' => 'Cu Ft'
        ],
        'lf' => [
            'name' => 'Linear Foot',
            'abbreviation' => 'LF'
        ],
        'cu-yd' => [
            'name' => 'Cubic Yard',
            'abbreviation' => 'Cu Yd'
        ],
        'gal' => [
            'name' => 'Gallon',
            'abbreviation' => 'Gal'
        ],
        'lb' => [
            'name' => 'Pound',
            'abbreviation' => 'Lb'
        ],
        'hr' => [
            'name' => 'Hour',
            'abbreviation' => 'Hr'
        ],
        'sq-yd' => [
            'name' => 'Square Yard',
            'abbreviation' => 'Sq Yd'
        ],
        'yd' => [
            'name' => 'Yard',
            'abbreviation' => 'Yd'
        ],
        'bag' => [
            'name' => 'Bag',
            'abbreviation' => 'Bag'
        ],
        'pallet' => [
            'name' => 'Pallet',
            'abbreviation' => 'Plt'
        ],
        'roll' => [
            'name' => 'Roll',
            'abbreviation' => 'Roll'
        ],
        'sheet' => [
            'name' => 'Sheet',
            'abbreviation' => 'Sheet'
        ],
        'stick' => [
            'name' => 'Stick',
            'abbreviation' => 'Stk'
        ],
        'ton' => [
            'name' => 'Ton',
            'abbreviation' => 'Tn'
        ],
        'section' => [
            'name' => 'Section',
            'abbreviation' => 'Sect',
        ],
        'board-foot' => [
            'name' => 'Board Foot',
            'abbreviation' => 'Bd Ft',
        ],
        'day' => [
            'name' => 'Day',
            'abbreviation' => 'Day',
        ]
    ];

    protected $content_templates = [
        'bid-cover-main' => [
            'type' => ContentTemplateResource::TYPE_BID_COVER,
            'name' => 'Main',
            'file_base' => 'bid_cover_main',
            'is_default' => true
        ],
        'bid-cover-main-no-image' => [
            'type' => ContentTemplateResource::TYPE_BID_COVER,
            'name' => 'Main - No Image',
            'alias' => 'bid-cover-main-no-image',
            'file_base' => 'bid_cover_main_no_image',
            'is_default' => false
        ],
        'bid-intro-main' => [
            'type' => ContentTemplateResource::TYPE_BID_INTRO,
            'name' => 'Main',
            'file_base' => 'bid_intro_main',
            'is_default' => true
        ],
        'bid-sections-main' => [
            'type' => ContentTemplateResource::TYPE_BID_SECTIONS,
            'name' => 'Main',
            'file_base' => 'bid_sections_main',
            'is_default' => true
        ],
        'bid-line-items-main' => [
            'type' => ContentTemplateResource::TYPE_BID_LINE_ITEMS,
            'name' => 'Full',
            'file_base' => 'bid_line_items_main',
            'is_default' => false
        ],
        'bid-line-items-section-total' => [
            'type' => ContentTemplateResource::TYPE_BID_LINE_ITEMS,
            'name' => 'Section Total Only',
            'file_base' => 'bid_line_items_section_total',
            'is_default' => true
        ],
        'bid-line-items-quantity-total' => [
            'type' => ContentTemplateResource::TYPE_BID_LINE_ITEMS,
            'name' => 'Total with Quantities',
            'file_base' => 'bid_line_items_quantity_total',
            'is_default' => true
        ],
        'bid-line-items-total-only' => [
            'type' => ContentTemplateResource::TYPE_BID_LINE_ITEMS,
            'name' => 'Total Only',
            'file_base' => 'bid_line_items_total_only',
            'is_default' => false
        ],
        'bid-terms-conditions-main' => [
            'type' => ContentTemplateResource::TYPE_BID_TERMS_CONDITIONS,
            'name' => 'Main',
            'file_base' => 'bid_terms_conditions_main',
            'is_default' => true
        ],
        'bid-terms-conditions-main-with-salesperson-signature' => [
            'type' => ContentTemplateResource::TYPE_BID_TERMS_CONDITIONS,
            'name' => 'Main with Salesperson Signature',
            'file_base' => 'bid_terms_conditions_main_with_salesperson',
            'is_default' => false
        ],
        'bid-images-main-2-column' => [
            'type' => ContentTemplateResource::TYPE_BID_IMAGES,
            'name' => 'Main - 2 Column',
            'file_base' => 'bid_images_main_2_column',
            'is_default' => true
        ],
        'bid-images-main-1-column' => [
            'type' => ContentTemplateResource::TYPE_BID_IMAGES,
            'name' => 'Main - 1 Column',
            'file_base' => 'bid_images_main_1_column',
            'is_default' => false
        ],
        'bid-media-main' => [
            'type' => ContentTemplateResource::TYPE_BID_MEDIA,
            'name' => 'Main',
            'file_base' => 'bid_media_main',
            'is_default' => true
        ],
        'scope-of-work-project-info' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_PROJECT_INFO,
            'name' => 'Main',
            'file_base' => 'scope_of_work_project_info',
            'is_default' => true
        ],
        'scope-of-work-materials-list' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_MATERIALS_LIST,
            'name' => 'Main',
            'file_base' => 'scope_of_work_materials_list',
            'is_default' => true
        ],
        'scope-of-work-materials-list-total-components' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_MATERIALS_LIST,
            'name' => 'Main (Total by Components)',
            'file_base' => 'scope_of_work_materials_list_total_components',
            'is_default' => false
        ],
        'scope-of-work-sections' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_SECTIONS,
            'name' => 'Main',
            'file_base' => 'scope_of_work_sections',
            'is_default' => true
        ],
        'scope-of-work-images' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_IMAGES,
            'name' => 'Main',
            'file_base' => 'scope_of_work_images',
            'is_default' => true
        ],
        'scope-of-work-terms-conditions' => [
            'type' => ContentTemplateResource::TYPE_SCOPE_OF_WORK_TERMS_CONDITIONS,
            'name' => 'Main',
            'file_base' => 'scope_of_work_terms_conditions',
            'is_default' => false
        ]
    ];

    protected $content_partials = [
        'end-of-cover' => [
            'name' => '1 - End of Cover'
        ],
        'intro' => [
            'name' => '2 - Intro',
            'file' => 'bid_intro'
        ],
        'after-payment-terms' => [
            'name' => '3 - After Payment Terms'
        ],
        'before-signature' => [
            'name' => '4 - Before Signature'
        ]
    ];

    protected $bid_content = [
        'contract' => [
            'name' => 'Contract',
            'type' => ContentResource::TYPE_CONTRACT,
            'file' => 'contract',
            'is_default' => true,
            'is_locked' => false,
            'is_required' => false,
            'is_answer_required' => false
        ],
        'disclaimer' => [
            'name' => 'Sample Disclaimer',
            'type' => ContentResource::TYPE_DISCLAIMER,
            'file' => 'disclaimer',
            'is_default' => true,
            'is_locked' => false,
            'is_required' => false,
            'is_answer_required' => false
        ],
        'waiver' => [
            'name' => 'Sample Waiver',
            'type' => ContentResource::TYPE_WAIVER,
            'file' => 'waiver',
            'is_default' => true,
            'is_locked' => false,
            'is_required' => false,
            'is_answer_required' => false
        ],
        'warranty' => [
            'name' => 'Sample Warranty',
            'type' => ContentResource::TYPE_WARRANTY,
            'file' => 'warranty',
            'is_default' => true,
            'is_locked' => false,
            'is_required' => false,
            'is_answer_required' => false
        ]
    ];

    protected $warranties = [
        'sample_lifetime_warranty' => [
            'name' => 'Sample Lifetime Warranty',
            'file' => 'sample_lifetime_warranty',
            'type' => Warranty::TYPE_CERTIFICATE
        ]
    ];

    public function __construct($company_id)
    {
        $this->company_id = $company_id;
        $this->acl = Acl::make();
    }

    protected function createUnits()
    {
        $resource = UnitResource::make($this->acl);
        foreach ($this->units as $alias => &$unit) {
            $unit['id'] = $resource->create(Entity::make([
                'owner_type' => UnitResource::OWNER_TYPE_COMPANY,
                'owner_id' => $this->company_id,
                'alias' => $alias,
                'name' => $unit['name'],
                'abbreviation' => $unit['abbreviation']
            ]))->run();
            unset($unit);
        }
    }

    protected function createContentTemplates()
    {
        $resource = ContentTemplateResource::make($this->acl);
        $base_path = Path::resource('content-templates/');
        foreach ($this->content_templates as $alias => $template) {
            $entity = Entity::make([
                'company_id' => $this->company_id,
                'alias' => $alias,
                'type' => $template['type'],
                'name' => $template['name'],
                'is_default' => $template['is_default']
            ]);
            $content_file = "{$base_path}content/{$template['file_base']}.hbs";
            if (file_exists($content_file)) {
                $entity->set('content', file_get_contents($content_file));
            }
            $styles_file = $base_path . "styles/{$template['file_base']}.css";
            if (file_exists($styles_file)) {
                $entity->set('styles', file_get_contents($styles_file));
            }
            $resource->create($entity)->run();
        }
    }

    protected function createContentPartials()
    {
        $resource = ContentPartialResource::make($this->acl);
        $base_path = Path::resource('content-partials/');
        foreach ($this->content_partials as $alias => $partial) {
            $entity = Entity::make([
                'company_id' => $this->company_id,
                'name' => "Bid - {$partial['name']}",
                'alias' => "bid-{$alias}"
            ]);
            if (isset($partial['content'])) {
                $entity->set('content', $partial['content']);
            } else if (isset($partial['file'])) {
                $file = "{$base_path}{$partial['file']}.hbs";
                if (!file_exists($file)) {
                    throw new AppException('Unable to find file %s', $partial['file']);
                }
                $entity->set('content', file_get_contents($file));
            }
            $resource->create($entity)->run();
        }
    }

    protected function createForms()
    {
        $custom_services = SystemType::getByAlias($this->acl, 'custom-services');
        $field_notes = SystemType::getByAlias($this->acl, 'field-notes');
        $scope_of_work = SystemType::getByAlias($this->acl, 'scope-of-work');

        CompanyType::createFromSystemType($custom_services)
            ->setCompanyID($this->company_id)
            ->save(Acl::make()->setCompanyID($this->company_id));

        CompanyType::createFromSystemType($field_notes)
            ->setCompanyID($this->company_id)
            ->save(Acl::make()->setCompanyID($this->company_id));

        CompanyType::createFromSystemType($scope_of_work)
            ->setCompanyID($this->company_id)
            ->save(Acl::make()->setCompanyID($this->company_id));
    }

    protected function createBidContent()
    {
        $resource = ContentResource::make($this->acl);
        $base_path = Path::resource('bid-content/');
        foreach ($this->bid_content as $key => $content) {
            $entity = Entity::make([
                'company_id' => $this->company_id,
                'name' => $content['name'],
                'type' => $content['type'],
                'is_default' => $content['is_default'],
                'is_locked' => $content['is_locked'],
                'is_required' => $content['is_required'],
                'is_answer_required' => $content['is_answer_required']
            ]);
            if (isset($content['file'])) {
                $file = "{$base_path}{$content['file']}.html";
                if (!file_exists($file)) {
                    throw new AppException('Unable to find file %s', $content['file']);
                }
                $entity->set('content', file_get_contents($file));
            }
            $resource->create($entity)->run();
        }
    }

    protected function createWarranties()
    {
        $base_path = Path::resource('warranties/');
        foreach ($this->warranties as $key => $warranty) {
            $warranty_content = '';
            if (isset($warranty['file'])) {
                $file = "{$base_path}{$warranty['file']}.html";
                if (!file_exists($file)) {
                    throw new AppException('Unable to find file %s', $warranty['file']);
                }
                $warranty_content = file_get_contents($file);
            }
            Warranty::create([
                'companyID' => $this->company_id,
                'name' => $warranty['name'],
                'warranty' => $warranty_content,
                'type' => $warranty['type'],
                'lastUpdated' => Carbon::now('UTC')
            ]);
        }
    }

    public function run()
    {
        try {
            DB::transaction(function () {
                $this->createUnits();
                $this->createContentTemplates();
                $this->createContentPartials();
                $this->createForms();
                $this->createBidContent();
                $this->createWarranties();
            });
        } catch (Exception $e) {
            throw (new AppException('Unable to setup company defaults - Reason: %s', $e->getMessage()))
                ->setLastException($e);
        }
    }
}
