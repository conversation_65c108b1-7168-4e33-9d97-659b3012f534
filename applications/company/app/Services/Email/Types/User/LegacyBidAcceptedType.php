<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Common\Models\CustomBid;
use Common\Models\User;
use Core\Components\DB\StaticAccessors\DB;
use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Formatter\IntlMoneyFormatter;
use Money\Parser\DecimalMoneyParser;
use NumberFormatter;
use Ramsey\Uuid\Uuid;

/**
 * Class LegacyBidAcceptedType
 *
 * @package App\Services\Email\Types\User
 */
class LegacyBidAcceptedType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        if (!isset($payload['evaluation_id'])) {
            throw new TypeException('Evaluation id not defined in payload');
        }

        $evaluation = CustomBid::query()->select([
            'customer.firstName as customerFirstName', 'customer.lastName as customerLastName', 'customer.businessName as customerBusinessName',
            'evaluation.evaluationUUID', DB::raw('IF(project.projectSalesperson IS NULL, evaluation.evaluationCreatedByID, project.projectSalesperson) AS userID'),
            'project.projectID', 'property.address as propertyAddress', 'property.address2 as propertyAddress2',
            'project.projectDescription AS project_name', 'customBid.bidTotal AS bid_total'
        ])
            ->join('evaluation', 'evaluation.evaluationID', '=', 'customBid.evaluationID')
            ->join('project', 'project.projectID', '=', 'evaluation.projectID')
            ->join('property', 'property.propertyID', '=', 'project.propertyID')
            ->join('customer', 'customer.customerID', '=', 'property.customerID')
            ->whereKey($payload['evaluation_id'])
            ->first();
        if ($evaluation === null) {
            throw new TypeException('Unable to find evaluation: %d', $payload['evaluation_id']);
        }

        if (($user = User::find($evaluation->userID)) === null) {
            throw new TypeException('Unable to find user: %d', $evaluation->userID);
        }

        $this->setup($user->company, [$user]);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($evaluation->evaluationUUID));

        $this->addAdditionalRecipients($message);

        $property_address = $evaluation->propertyAddress;
        $property_address .= $evaluation->propertyAddress2 !== null ? ", {$evaluation->propertyAddress2}" : '';

        $bid_total = (string) BigDecimal::of($evaluation->bid_total)->toScale(2, RoundingMode::HALF_DOWN);

        $currencies = new ISOCurrencies();
        $money = (new DecimalMoneyParser($currencies))->parse($bid_total, new Currency('USD'));
        $formatter = new NumberFormatter('en_US', NumberFormatter::CURRENCY);
        $bid_total = (new IntlMoneyFormatter($formatter, $currencies))->format($money);

        $message->subject("Your Bid Has Been Accepted: {$evaluation->project_name} ({$bid_total})");

        $domain = $this->getDomain();

        $business_display = '';
        if ($evaluation->customerBusinessName !== null) {
            $business_display = ' (' . $evaluation->customerBusinessName . ')';
        }

        $template_vars = [
            'customer_name' => $evaluation->customerFirstName . ' ' . $evaluation->customerLastName . $business_display,
            'property_address' => $property_address,
            'project_name' => $evaluation->project_name,
            'bid_total' => $bid_total,
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilder()->path('projects/'.$evaluation->projectID)->build(),
            'unsubscribe_link' => $this->newUrlBuilder()->path("user/profile")->build()
        ];

        $this->template->content = Template::fetch('emails.html.user.legacy-bid-accepted', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.legacy-bid-accepted', $template_vars));

        return $message;
    }
}
