/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */

'use strict';

const Segment = require('../segment');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment
 */
class Dimension extends Segment {
    /**
     * Create node
     *
     * @param {object} data
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    createNode(data) {
        let node = super.createNode(data);
        node.createDimensionNode('main', false);
        return node;
    };

    /**
     * Create duplicate node
     *
     * Dimension segment node will create main dimension line when duplicating automatically, just need to render it
     * after the new duplicate node is done rendering.
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    createDuplicateNode() {
        let node = super.createDuplicateNode();
        node.renderDimensionNode('main');
        return node;
    };
}

module.exports = Dimension;
