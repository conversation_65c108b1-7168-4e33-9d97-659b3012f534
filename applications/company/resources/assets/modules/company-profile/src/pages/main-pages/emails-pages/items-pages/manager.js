'use strict';

const moment = require('moment-timezone');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const emails_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/manager.hbs');
class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            modals: {},
            table: null,
            table_scope: {},
            table_loaded: false,
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            delete: {
                path: '/delete/{email_id}',
                modal: require('./manager-pages/delete'),
                bindings: {
                    email_id: 'uuid'
                }
            }
        };
    };

    /**
     * Create the emails datatable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('emails.items.update', {
                    email_id: data.id
                });
            }).on('scope_change', (scope) => {
                this.state.table_scope = scope;
            });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Internal Name',
                orderable: false
            },
            is_send_from_salesperson: {
                label: 'Send From',
                value: (data) => {
                    let send_from_salesperson = data.is_send_from_salesperson;
                    if (send_from_salesperson === null) {
                        return 'Company';
                    }
                    return send_from_salesperson ? 'Salesperson' : 'Company';
                },
                orderable: false
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
                orderable: false
            }
        });

        // set row action config
        this.state.table.setRowActions({
            edit: {
                label: 'Edit',
                action: (data) => {
                    this.router.navigate('emails.items.update', {
                        email_id: data.id
                    });
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                visible: data => data.type === Api.Constants.EmailTemplates.Type.BID_FOLLOW_UP,
                action: (data) => {
                    this.router.navigate('emails.items.manager.delete', {
                        email_id: data.id
                    });
                }
            }
        });

        if (profile_data.features.bid_follow_ups) {
            // set buttons config
            this.state.table.setButtons({
                add_email: {
                    label: 'New Email',
                    action: () => {
                        this.router.navigate('emails.items.create');
                    },
                    type_class: 't-primary'
                }
            });
        }

        this.state.table.setAjax(Api.Resources.EmailTemplates, (request) => {
            request
                .fields(['id', 'name', 'type', 'source', 'is_send_from_salesperson', 'updated_at', 'created_by_user_name'])
                .filter('status', Api.Constants.EmailTemplates.Status.ACTIVE)
                .sort('type', 'asc')
                .sort('name', 'asc')
                .all();
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        this.state.table.draw();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return emails_tpl();
    };
}

module.exports = Manager;