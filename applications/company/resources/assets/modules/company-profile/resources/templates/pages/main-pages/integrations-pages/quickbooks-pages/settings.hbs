<div class="c-i--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>QuickBooks Settings</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="cancel" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-integrations">
        <div class="c-i-error" data-js="error"></div>
        <form class="c-i-form" data-js="form">
            <div class="c-if-half">
                <div class="f-field">
                    <label class="f-f-label">
                        Default Service For QB Invoice
                        <span data-tooltip data-type="info">
                        The Product/Service type to be used for the QuickBooks Invoice.
                    </span>
                    </label>
                    <select class="f-f-input" name="default_service_id" data-js="default_service_id">
                        <option value="">-- Select One --</option>
                    </select>
                </div>
            </div>
            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="use-quickbooks-invoice" data-fx-form-input="switch" data-js="use_quickbooks_invoice">
                        <label for="use-quickbooks-invoice" class="f-f-label">
                            Use QuickBooks Invoice
                            <span data-tooltip data-type="info">
                                Turn this setting off to use the software's invoices rather than QuickBooks.<br>
                                Note: Invoices will still be created in QuickBooks.
                            </span>
                        </label>
                    </div>
                </div>
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="allow-credit-card" data-fx-form-input="switch" data-js="allow_credit_card">
                        <label for="allow-credit-card" class="f-f-label">
                            Allow Online Credit Card Payment
                            <span data-tooltip data-type="info">
                                 ACH Payment Setting in the QuickBooks Invoice.
                            </span>
                        </label>
                    </div>
                </div>
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="allow-ach" data-fx-form-input="switch" data-js="allow_ach">
                        <label for="allow-ach" class="f-f-label">
                            Allow Online ACH Payment
                            <span data-tooltip data-type="info">
                                Credit Card Payment Setting in the QuickBooks Invoice.
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


