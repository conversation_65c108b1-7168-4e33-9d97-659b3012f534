<?php

declare(strict_types=1);

namespace App\Extensions;

use App\Classes\ApiLogger;
use App\Classes\Log;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Resource\Classes\Resource;
use Core\Interfaces\KernelInterface;

/**
 * Class ResourceExtension
 *
 * Load any classes related to resources
 *
 * @package App\Extensions
 */
class ResourceExtension
{
    /**
     * Load classes into registry
     *
     * @param KernelInterface $kernel
     */
    public function load(KernelInterface $kernel)
    {
        $kernel->singleton(ApiLogger::class, function (KernelInterface $kernel) {
            return new ApiLogger($kernel->get('config'), $kernel->get(Auth::class));
        });

        Resource::setLogger(Log::create('resource'));
    }
}
