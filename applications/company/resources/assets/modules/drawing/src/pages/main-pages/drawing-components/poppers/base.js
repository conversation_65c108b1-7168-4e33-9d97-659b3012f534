/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Poppers
 */

'use strict';

const Popper = require('popper.js');

const wrapper_tpl = require('@cam-drawing-tpl/pages/main-pages/drawing-components/popper.hbs');

const Types = {
    LINE_LENGTH: 1,
    RECTANGLE_SIZE: 2
};

/**
 * Base popper class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Poppers
 * @abstract
 */
class Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.PopperManager} manager
     */
    constructor(manager) {
        this.elem = {};
        this.state = {
            manager,
            name: null,
            popper_options: {
                placement: 'top'
            },
            popper: null,
            reference_elem: null,
            position: null
        };
    };

    /**
     * Get available types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Type() {
        return Types;
    };

    /**
     * Get classes
     *
     * @readonly
     *
     * @returns {Map<number, typeof module:Drawing/Pages/MainPages/DrawingComponents/Poppers.Base>}
     */
    static get Classes() {
        if (this.__classes === null) {
            this.__classes = new Map([
                [Types.LINE_LENGTH, require('./line_length')],
                [Types.RECTANGLE_SIZE, require('./rectangle_size')]
            ]);
        }
        return this.__classes;
    };

    /**
     * Get type
     *
     * @readonly
     *
     * @returns {number}
     */
    get type() {
        return this.state.type;
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Create Popper.js instance properly configured with reference element
     *
     * @returns {Popper}
     */
    createPopper() {
        let dimension = 2,
            reference = {
                getBoundingClientRect: () => {
                    let position = this.state.manager.view.getViewPointFromProject(this.state.position);
                    return {
                        top: position.y,
                        right: position.x,
                        bottom: position.y,
                        left: position.x,
                        width: dimension,
                        height: dimension,
                    };
                },
                clientWidth: dimension,
                clientHeight: dimension
            };
        return new Popper(reference, this.elem.root, this.state.popper_options);
    };

    /**
     * Initialize Popper with position
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} position
     */
    initPopper(position) {
        this.setPosition(position, false);
        this.state.popper = this.createPopper();
    };

    /**
     * Update Popper instance
     */
    updatePopper() {
        this.state.popper.scheduleUpdate();
    };

    /**
     * Set position and update Popper instance
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} position
     * @param {boolean} [update=true]
     */
    setPosition(position, update = true) {
        this.state.position = position;
        if (update) {
            this.updatePopper();
        }
    };

    /**
     * Handle zoom change
     */
    zoomChange() {
        this.updatePopper();
    };

    /**
     * Destroy popper
     */
    destroy() {
        this.state.popper.destroy();
        this.elem.root.remove();
        this.state.manager.unregister(this);
    };

    /**
     * Boot popper
     *
     * @param {jQuery} root
     */
    boot(root) {
        this.elem.root = root;
    };

    /**
     * Get content for popper
     *
     * @returns {string}
     */
    getContent() {
        return '';
    };

    /**
     * Render popper
     */
    render() {
        return wrapper_tpl({
            name: this.name,
            content: this.getContent()
        });
    };
}

Base.__classes = null;

module.exports = Base;
