'use strict';

const EventEmitter = require('events');
const get = require('lodash/get');
const set = require('lodash/set');

const item_tpl = require('@cas-accordion-tpl/item.hbs');

/**
 * @memberof module:Accordion
 */
class Item {
    /**
     * Constructor
     *
     * @param {string} title - Title of item
     * @param {string} panel_content - Panel content
     * @param {Object} [storage={}] - Extra data storage for item
     */
    constructor(title, panel_content, storage = {}) {
        Item.__idx++;
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            accordion: null,
            rendered: false,
            booted: false,
            events: new EventEmitter,
            id: Item.__idx,
            title: title,
            panel_content: panel_content,
            active: false,
            storage: storage
        };
    };

    /**
     * Id of item
     *
     * @returns {number}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get accordion controller instance
     *
     * Note: only available after boot
     *
     * @returns {null|module:Accordion.Controller}
     */
    get accordion() {
        return this.state.accordion;
    };

    /**
     * Set accordion controller instance
     *
     * @param {module:Accordion.Controller} value
     */
    set accordion(value) {
        this.state.accordion = value;
    };

    /**
     * Get active status
     *
     * @returns {boolean}
     */
    get active() {
        return this.state.active;
    };

    /**
     * Add event listener
     *
     * @param {string} event
     * @param {function} closure
     * @returns {module:Accordion.Item}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Set title
     *
     * @param {string} title
     */
    setTitle(title) {
        this.state.title = title;
        if (this.isBooted()) {
            this.elem.title_text.text(title);
        }
    };

    /**
     * Store custom data with item by key
     *
     * Note: can use dot syntax to handle nested values
     *
     * @param {string} key
     * @param {*} value
     * @returns {module:Accordion.Item}
     */
    store(key, value) {
        set(this.state.storage, key, value);
        return this;
    };

    /**
     * Get custom data from storage by key
     *
     * Note: can use dot syntax to handle nested values
     *
     * @param {?string} [key=null]
     * @param {*} [_default=null]
     * @returns {*}
     */
    storage(key = null, _default = null) {
        if (key === null) {
            return this.state.storage;
        }
        return get(this.state.storage, key, _default);
    };

    /**
     * Toggle open or close state of panel
     */
    toggle() {
        if (!this.state.active) {
            this.elem.root.addClass(this.state.config.open_class);
            this.elem.panel.stop().slideDown(this.state.config.panel_slide_time);
            this.state.active = true;
            return;
        }
        this.elem.root.removeClass(this.state.config.open_class);
        this.state.active = false;
        this.elem.panel.stop().slideUp(this.state.config.panel_slide_time);
    };

    /**
     * Delete item
     *
     * @emits module:Accordion.Item~deleted
     */
    delete() {
        if (this.isBooted()) {
            this.elem.root.remove();
        }
        this.state.events.emit('deleted', {
            item: this
        });
        this.state.accordion.deleteItem(this.state.id);
    };

    /**
     * Determines if item is booted or not
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot accordion item
     *
     * @emits module:Accordion.Item~booted
     */
    boot() {
        this.elem.root = this.accordion.elem.items.fxChildren('item', {id: this.state.id});
        this.elem.title_text = this.elem.root.fxFind('title-text');
        this.elem.panel = this.elem.root.fxChildren('panel');

        this.state.config = this.accordion.config('item');

        this.state.booted = true;
        this.state.events.emit('booted', {
            item: this
        });
    };

    /**
     * Render item
     *
     * @returns {string}
     */
    render() {
        this.state.rendered = true;
        return item_tpl({
            id: this.state.id,
            title: this.state.title,
            panel_content: this.state.panel_content
        });
    };
}

Item.__idx = 0;

/**
 * Booted event
 *
 * @event module:Accordion.Item~booted
 * @type {Object}
 * @property {module:Accordion.Item} item - Item which was booted
 */

module.exports = Item;
