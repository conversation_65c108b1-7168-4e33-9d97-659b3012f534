<?php

namespace Core\Components\Resource\Traits\Request;

use Core\Components\Resource\Classes\Scope;

trait ScopeTrait
{
    protected $scope = null;

    public function scope(Scope $scope)
    {
        $this->scope = $scope;
        return $this;
    }

    public function hasScope()
    {
        return $this->scope !== null;
    }

    public function getScope()
    {
        if (!$this->hasScope()) {
            $this->scope = new Scope;
        }
        return $this->scope;
    }
}
