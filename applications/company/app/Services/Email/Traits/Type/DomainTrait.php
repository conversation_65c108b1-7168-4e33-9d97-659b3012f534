<?php

namespace App\Services\Email\Traits\Type;

use App\Services\DomainService;
use Common\Models\Domain;
use Core\Components\Http\StaticAccessors\URI;
use Core\StaticAccessors\App;

/**
 * Trait DomainTrait
 *
 * @package App\Services\Email\Traits\Type
 */
trait DomainTrait
{
    /**
     * @var null|\Common\Models\Domain
     */
    protected $domain = null;

    /**
     * Get domain service instance
     *
     * @return DomainService
     */
    protected function getDomainService()
    {
        return App::get(DomainService::class);
    }

    /**
     * Load domain model based on instance context
     *
     * @return \Common\Models\Domain
     */
    abstract protected function loadDomain();

    /**
     * Set domain
     *
     * @param Domain $domain
     */
    protected function setDomain(Domain $domain)
    {
        $this->domain = $domain;
    }

    /**
     * Get and cache domain
     *
     * @return \Common\Models\Domain
     */
    protected function getDomain()
    {
        if ($this->domain === null) {
            $this->setDomain($this->loadDomain());
        }
        return $this->domain;
    }

    /**
     * Get new url builder instance based on domain data
     *
     * @return \Core\Components\Http\Classes\URLBuilder
     */
    protected function newUrlBuilder()
    {
        return URI::create()->host($this->getDomain()->domain);
    }

    /**
     * Get new url builder instance based on domain data using route name
     *
     * @param string $name
     * @param array $data
     * @return \Core\Components\Http\Classes\URLBuilder
     */
    protected function newUrlBuilderFromRoute($name, array $data = [])
    {
        return URI::route($name, $data)->host($this->getDomain()->domain);
    }
}
