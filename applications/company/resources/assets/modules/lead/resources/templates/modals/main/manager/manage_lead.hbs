<form class="m-lead-form" data-js="form">
    <div class="c-lf-error" data-js="error"></div>
    <div class="c-lf-grid t-contact">
        <h5>Contact Info</h5>
        <div class="f-field">
            <label class="f-f-label">First Name</label>
            <input class="f-f-input" type="text" value="" data-js="first_name" />
        </div>
        <div class="f-field">
            <label class="f-f-label">Last Name</label>
            <input class="f-f-input" type="text" value="" data-js="last_name" />
        </div>
        <div class="f-field t-business">
            <label class="f-f-label">
                Business Name
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="business_name" />
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Email
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="email" />
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Phone
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="phone_number" />
        </div>
    </div>
    <div class="c-lf-grid t-location">
        <h5>Location</h5>
        <div class="f-field t-address">
            <label class="f-f-label">
                Address
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="address" />
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Address 2
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="address_2" />
        </div>
        <div class="f-field">
            <label class="f-f-label">
                City
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="city" />
        </div>
        <div class="f-field">
            <label class="f-f-label">
                State
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <select class="f-f-input" data-js="state">
                <option></option>
                <optgroup label="States">
                    {{#each states}}
                        <option value="{{@key}}">{{this}}</option>
                    {{/each}}
                </optgroup>
                <optgroup label="US Territories">
                    {{#each us_territories}}
                        <option value="{{@key}}">{{this}}</option>
                    {{/each}}
                </optgroup>
                <optgroup label="Provinces">
                    {{#each provinces}}
                        <option value="{{@key}}">{{this}}</option>
                    {{/each}}
                </optgroup>
            </select>
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Zip
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="" data-js="zip" />
        </div>
    </div>
    <div class="c-lf-grid t-details">
        <h5>Details</h5>
        <div class="f-field">
            <label class="f-f-label">
                Project Type
                <span data-tooltip data-type="info">
                    Primary users can create Project Types in the Company Profile > Company Settings > Projects Section.
                </span>
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <select class="f-f-input" data-js="project_type_id" placeholder="-- Select One --"></select>
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Priority
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <select class="f-f-input" data-js="priority" placeholder="-- Select One --">
                {{#each priorities}}
                    {{#unless hidden}}
                        <option value="{{value}}">{{label}}</option>
                    {{/unless}}
                {{/each}}
            </select>
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Assigned To
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <select class="f-f-input" data-js="assigned_to_user_id" data-fx-form-input="static-dropdown"></select>
        </div>
        <div class="f-field">
            <label class="f-f-label">
                Marketing Source
                <span data-tooltip data-type="info">
                    Primary users can edit Marketing Sources in Marketing via the top navigation bar
                </span>
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <select class="f-f-input" data-js="marketing_type_id" placeholder="-- Select One --"></select>
        </div>
        <div class="f-field t-notes">
            <label class="f-f-label">
                Notes
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <textarea class="f-f-input" data-fx-form-input="wysiwyg" data-js="notes"></textarea>
        </div>
    </div>
</form>
