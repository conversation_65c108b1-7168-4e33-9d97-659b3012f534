@use '~@cac-sass/base';
@use '~@uppy/core/dist/style' as uppy-core;
@use '~@uppy/dashboard/dist/style' as uppy-dashboard;

%uppy-status-bar {
    .uppy-StatusBar {
        margin-top: base.unit-rem-calc(20px);
        &.is-waiting {
            margin-top: 0;
        }
    }
    .uppy-StatusBar-statusIndicator {
        margin-right: base.unit-rem-calc(15px) !important;
    }
}

.f-field {
    .f-f-file-upload {}
        .f-ffu-files {
            display: flex;
        }
            .f-ffuf-button {
                flex: 0 0 auto;
                margin-right: base.unit-rem-calc(12px);
            }
            $list-item-dimension: base.unit-rem-calc(30px);
            .f-ffuf-list {
                display: flex;
                justify-content: flex-end;
                flex-wrap: wrap;
                height: $list-item-dimension;
                overflow: hidden;
            }
                .f-ffufl-item {
                    flex: 0 0 auto;
                    position: relative;
                    margin-left: base.unit-rem-calc(10px);
                    width: $list-item-dimension;
                    height: $list-item-dimension;
                    border-radius: base.unit-rem-calc(2px);
                    overflow: hidden;
                }
                    .f-ffufli-overflow {
                        display: none;
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: $list-item-dimension;
                        height: $list-item-dimension;
                        line-height: $list-item-dimension;
                        color: base.$color-white-default;
                        font-size: base.unit-rem-calc(18px);
                        font-weight: 500;
                        text-align: center;
                        background-color: rgba(75, 83, 102, 0.69);
                        z-index: 3;
                        &.t-show {
                            display: block;
                        }
                        &.t-large {
                            font-size: base.unit-rem-calc(13px);
                        }
                    }
                    .f-ffufli-loading {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        background-image: url('~@cac-public/images/loading_small.svg');
                        background-size: contain;
                        z-index: 1;
                    }
                    > img {
                        position: relative;
                        display: block;
                        width: $list-item-dimension;
                        height: $list-item-dimension;
                        z-index: 2;
                    }
        .f-ffu-status-bar {
            @extend %uppy-status-bar;
        }
}
