<?php

namespace App\Http\Controllers\ApiV1;

use App\Exceptions\Api\UnprocessableEntityException;
use App\Traits\Resource\Controller\PolyActionTrait;
use Carbon\Carbon;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\RequestValidation\Classes\Validation;
use Core\Components\Resource\Classes\Entity;
use App\ResourceMediaHandlers\Drawing\ImageHandler;
use App\Resources\DrawingResource;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Exception;
use Ramsey\Uuid\UuidInterface;

class DrawingController
{
    use PolyActionTrait;

    protected $resource = DrawingResource::class;
    protected $max_version = 2;

    public function __construct()
    {
        $this->registerFormat('bid-v1', 'application/vnd.adg.fx.bid-v1+json');
        $this->registerFormat('drawing-v2', 'application/vnd.adg.fx.drawing-v2+json');
        $this->registerFormat('drawing-v2-search', 'application/vnd.adg.fx.drawing-v2-search+json');
    }

    public function modifyImage($id, ResourceRequest $request)
    {
        try {
            /** @var ImageHandler $handler */
            $handler = $this->getResource()->getMediaHandler('image');
            $handler->setStartTime($request->getRequest()->input()->server('REQUEST_TIME_FLOAT'));
            $handler->save($id, $request->entity());
            return Response::create('', 204);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function lock($id)
    {
        try {
            $this->getResource()->update(Entity::make([
                'id' => $id,
                'is_locked' => true
            ]))
                ->partial()
                ->run();
            return Response::create('', 204);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function unlock($id)
    {
        try {
            $this->getResource()->update(Entity::make([
                'id' => $id,
                'is_locked' => false
            ]))
                ->partial()
                ->run();
            return Response::create('', 204);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    public function sync(Validation $validation)
    {
        $validation = $validation->config([
            'last_synced_at' => [
                'label' => 'Last Synced At',
                'rules' => 'nullable|optional|iso8601_date|to_carbon|to_utc'
            ],
            'additional_ids.*' => [
                'label' => 'Additional Ids',
                'rules' => 'required|uuid',
                'group_rules' => [
                    'type' => 'array'
                ]
            ]
        ]);
        $validator = $validation->run();
        if (!$validator->valid()) {
            throw UnprocessableEntityException::fromValidator($validator);
        }
        $last_synced_at = $validator->data('last_synced_at');
        if ($last_synced_at === null) {
            $last_synced_at = Carbon::now('UTC')->subDays(14)->startOfDay();
        }
        $additional_ids = array_map(function (UuidInterface $uuid) {
            return $uuid->getBytes();
        }, $validator->data('additional_ids'));
        $drawing_scope = Scope::make()
            ->format('drawing-v2')
            ->with(['nodes'])
            ->query(function ($query) use ($last_synced_at, $additional_ids) {
                return $query->where('drawings.lastModifiedAt', '>=', $last_synced_at)
                    ->where(function ($query) use ($additional_ids) {
                        $query->where('drawings.createdByUserID', Auth::user()->getKey());
                        if (count($additional_ids) > 0) {
                            $query->orWhereIn('drawings.drawingID', $additional_ids);
                        }
                    });
            });
        $drawings = $this->getResource(2)->collection()
            ->scope($drawing_scope)
            ->run();

        return Response::json([
            'drawings' => $drawings->toArray(),
            'last_synced_at' => Carbon::now('UTC')->toIso8601String()
        ]);
    }

    public function finalize($id)
    {
        try {
            $this->getResource()->update(Entity::make([
                'id' => $id,
                'status' => DrawingResource::STATUS_FINALIZED
            ]))
                ->partial()
                ->run();
            return Response::create('', 204);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
}
