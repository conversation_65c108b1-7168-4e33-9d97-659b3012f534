<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Media extends HistoryEntityModel implements Interfaces\MediaInterface, ScopeSearchInterface
{
    use Common\MediaCommon;

    protected $table = 'media';
    protected $primaryKey = 'mediaID';
    protected $fillable = [
        'mediaID', 'companyID', 'name', 'description', 'isBidMedia', 'fileID', 'status', 'archivedAt', 'archivedByUserID',
        'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
