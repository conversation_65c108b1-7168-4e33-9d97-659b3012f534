<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\IntakeUserCount;

/**
 * Class IntakeUserCountSource
 *
 * @package Common\DataSources
 */
class IntakeUserCountSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'intake_user_counts';

    /**
     * Setup intake user counts by importing data into database
     */
    public function setup(): void
    {
        $user_counts = $this->loadData();
        $counter = 0;
        foreach($user_counts as $user_count) {
            IntakeUserCount::create([
                'intakeUserCountID' => $user_count['id'],
                'name' => $user_count['name'],
                'order' => ++$counter
            ]);
        }
    }
}
