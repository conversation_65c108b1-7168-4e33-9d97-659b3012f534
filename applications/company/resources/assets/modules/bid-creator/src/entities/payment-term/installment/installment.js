'use strict';

const lang = require('lodash/lang');
const includes = require('lodash/includes');
const uuid4 = require('uuid/v4');
const isUuid = require('validator/lib/isUUID');

const Number = require('@cac-js/utils/number');

const Entity = require('../../../entity');

const AmountTypes = {
    TOTAL: 1,
    PERCENTAGE: 2
};
const DueTimeFrames = {
    AT_BID_ACCEPTANCE: 1,
    AFTER_BID_ACCEPTANCE: 2,
    BEFORE_PROJECT_START: 3,
    AFTER_PROJECT_START: 4,
    BEFORE_PROJECT_COMPLETION: 5,
    AT_PROJECT_COMPLETION: 7,
    AFTER_PROJECT_COMPLETION: 6,
    AT_CLOSING: 8
};

/**
 * @memberof module:BidCreator/Entities/PaymentTerm/Installment
 */
class Installment extends Entity {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super();

        Object.assign(this.state, {
            id: !lang.isNil(data.id) ? data.id : null,
            name: null,
            amount_type: AmountTypes.PERCENTAGE,
            amount: Number.of('0'),
            due_time_frame: null,
            order: null
        });

        // only fill certain fields since they need to be validated, we don't want users setting things like id's
        this.fill(['name', 'amount_type', 'due_time_frame', 'order'], data);
        // convert percentage to be between 0 and 100 to make it easier to work with
        if (!lang.isNil(data.amount)) {
            let amount = Number.ofInput(data.amount);
            if (this.amount_type === Installment.AmountType.PERCENTAGE) {
                amount = amount.mul('100');
            }
            this.amount = amount;
        }
        // if we are loading an existing entity, we don't want the state to be changed which could trigger an
        // unnecessary save
        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Get available amount types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get AmountType() {
        return AmountTypes;
    };

    /**
     * Due Time Frames
     *
     * @readonly
     *
     * @returns {object}
     */
    static get DueTimeFrame() {
        return DueTimeFrames;
    };

    /**
     * Get id
     *
     * If no id is set, one will be generated and returned
     *
     * @readonly
     *
     * @returns {string} - Uuid
     */
    get id() {
        if (lang.isNil(this.state.id)) {
            this.setState({
                id: uuid4()
            }, false);
        }
        return this.state.id;
    };

    /**
     * Set name
     *
     * @param {string} value
     */
    set name(value) {
        if (!lang.isString(value)) {
            throw new Error('Name is not valid');
        } else if (value.length < 1 || value.length > 100) {
            throw new Error('Name must be between 1 and 100 characters');
        }
        this.setState({
            name: value
        });
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {?string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Set amount type
     *
     * @param {number} amount_type
     */
    set amount_type(amount_type) {
        this.setState({amount_type});
    };

    /**
     * Get amount type
     *
     * @returns {number}
     */
    get amount_type() {
        return this.state.amount_type;
    };

    /**
     * Set amount
     *
     * @param {(string|Decimal)} amount
     */
    set amount(amount) {
        amount = Number.ofInput(amount);
        if (amount.lte('0')) {
            throw new Error('Amount must be greater than 0');
        }
        this.setState({amount});
    };

    /**
     * Get amount
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get amount() {
        return this.state.amount;
    };

    /**
     * Set due time frame
     *
     * @param {number} value
     */
    set due_time_frame(value) {
        if (!includes(DueTimeFrames, value)) {
            throw new Error('Due Time Frame is not valid');
        }
        this.setState({
            due_time_frame: value
        });
    };

    /**
     * Get due time frame
     *
     * @readonly
     *
     * @returns {number}
     */
    get due_time_frame() {
        return this.state.due_time_frame;
    };

    /**
     * Set order
     *
     * @param {number} value
     */
    set order(value) {
        if (!lang.isNumber(value)) {
            throw new Error('Order must be a number');
        }
        this.setState({
            order: value
        });
    };

    /**
     * Get order
     *
     * @readonly
     *
     * @returns {number}
     */
    get order() {
        return this.state.order;
    };

    /**
     * Get payload for API save request
     *
     * @returns {Object}
     */
    getPayload() {
        let amount = this.amount_type === AmountTypes.PERCENTAGE ? this.amount.div('100') : this.amount;
        return {
            id: this.id,
            name: this.name,
            due_time_frame: this.due_time_frame,
            amount_type: this.amount_type,
            amount: amount.toString(),
            order: this.order
        };
    };

    /**
     * Validate installment
     *
     * @returns {Array}
     */
    validate() {
        let errors = [];
        if (!isUuid(this.id, 4)) {
            errors.push('Invalid id');
        }
        let required = {
            name: 'Name',
            amount_type: 'Amount Type',
            amount: 'Amount',
            due_time_frame: 'Due Time Frame',
            order: 'Order'
        };
        for (let field in required) {
            if (!lang.isNil(this[field])) {
                continue;
            }
            errors.push(`${required[field]} is required`);
        }
        if (
            errors.length === 0 &&
            this.amount_type === AmountTypes.PERCENTAGE &&
            (this.amount.lte('0') || this.amount.gt('100'))
        ) {
            errors.push('Amount must be between 0 and 100 for percentage types');
        }
        return errors;
    };

    /**
     * Prepare payment term
     *
     * Fetch/prep any necessary data before saving
     *
     * @returns {Promise<module:BidCreator/Entities/PaymentTerm/Installment.Installment>}
     */
    async prepare() {
        let errors = this.validate();
        if (errors.length > 0) {
            throw errors;
        }
        return this;
    };
}

module.exports = Installment;
