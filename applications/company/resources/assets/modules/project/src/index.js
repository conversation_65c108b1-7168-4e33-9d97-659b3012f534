/**
 * @module Project
 */

'use strict';

const Router = require('@ca-package/router');

/**
 * Main controller for project module
 *
 * @memberof module:Project
 */
class Controller {
    /**
     * Project constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.state = {
            layout
        };
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('Projects');
        this.state.router = new Router(require('./pages/main'), {
            base_path: '/projects',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}

module.exports = Controller;
