'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Gas extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Gas.Type.GAS);
        Object.assign(this.state, {
            label: 'Gas',
            icon: 'module--drawing--tools--gas',
            node_type: Node.Entity.Type.GAS
        });
    };
}

module.exports = Gas;
