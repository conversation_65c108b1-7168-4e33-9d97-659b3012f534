@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/grid';
@use '~@cac-sass/app/highlight';
@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-modal-sass/modal';
@use '~@cas-form-input-sass/date-time';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/wysiwyg';
@use '~@cas-tooltip-sass/tooltip';

.m-lead {
    @include base.full-width-height;
    .c-l-components {
        @include base.full-width-height;
    }
    .c-lc-component {
        @include base.full-width-height;
        &.t-manager {
            background-color: base.$color-background;
            padding: base.unit-rem-calc(24px);
            @include base.respond-to('<small') {
                padding: 0;
            }
        }
        &.t-hidden {
            display: none;
        }
    }
        .c-lcc-component {
            padding-top: base.unit-rem-calc(16px);
            width: 100%;
            height: 100%;
            position: relative;
            &.t-hidden {
                display: none;
            }
        }
            .c-lccc-loading {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.20) url('~@cac-public/images/loading.svg') no-repeat center;
                background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
                z-index: 120;
                display: none;
            }
            .c-lccc-pages {
                width: 100%;
                height: 100%;
            }
                .c-lcccp-page {
                    width: 100%;
                    height: 100%;
                    &.t-hidden {
                        display: none;
                    }
                }
                    .c-lcccpp-inner {
                        width: base.unit-rem-calc(1200px);
                        margin: 0 auto;
                        @include base.respond-to('<xlarge') {
                            width: 100%;
                            margin: 0;
                            padding: 0 base.unit-rem-calc(20px);
                        }
                    }

    .m-list {
        @include base.full-width-height;
        .c-l-table {
            @include base.full-width-height;
            padding: base.unit-rem-calc(12px);
            background-color: base.$color-white-default;
            border-radius: base.unit-rem-calc(12px);
            box-shadow: base.$elevation-level-1;
            @include base.respond-to('<small') {
                padding: base.unit-rem-calc(12px) 0;
                border-radius: 0;
                box-shadow: none;
            }
        }
    }
}

.m-lead-form {
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(32px);
    .c-lf-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-lf-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        row-gap: base.unit-rem-calc(8px);
        column-gap: base.unit-rem-calc(16px);
        .f-f-label {
            text-wrap: nowrap;
        }
        @include base.respond-to('<xsmall') {
            .f-field {
                grid-column: span 2;
            }
        }
        >h5 {
            grid-column: span 2;
        }
        &.t-location {
            @include base.respond-to('<xsmall') {
                .f-field, {
                    grid-column: span 3;
                    &.t-address {
                        grid-column: span 3;
                    }
                }
            }
            >h5 {
                grid-column: span 3;
            }
            grid-template-columns: repeat(3, 1fr);
            .t-address {
                grid-column: span 2;
            }
        }
        &.t-details {
            margin-bottom: base.unit-rem-calc(24px);
        }
        .t-business {
            grid-column: span 2;
        }
        .t-notes {
            grid-column: span 2;
        }
    }
}

.t-manage-lead {
    overflow: hidden;
}

.m-lead-details-modal {
    overflow: hidden;
    .c-ldm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    }
    .c-ldmh-text {
        flex: 1;
        align-self: center;
        @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
        color: base.$color-grey-dark-4;
        padding-left: base.unit-rem-calc(4px);
        @include base.respond-to('<small') {
            @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
        }
    }
    .c-ldmh-close {
        display: flex;
        justify-content: center;
        align-items: center;
        width: base.unit-rem-calc(32px);
        height: base.unit-rem-calc(32px);
        outline: 0 transparent solid;
        border-radius: base.unit-rem-calc(32px);
        color: base.$color-grey-light-2;
        transition: all 0.2s ease-out;
        .c-ldmhc-icon {
            @include base.svg-icon('default-24');
        }
        @include base.respond-to('hover') {
            &:hover {
                scale: 1.1;
                outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
            }
            &:active {
                color: base.$color-grey-dark-1;
            }
        }
    }


            .c-ldmccw-content {
                @include base.typo-paragraph;
                &.t-no-margin {
                    margin-bottom: 0;
                }
            }
            .c-ldmccw-link {
                @include base.button-icon-tertiary;
                height: base.unit-rem-calc(24px);
                padding: 0 0 base.unit-rem-calc(2px) base.unit-rem-calc(8px) !important;
            }
}
/* Task information content wrapper*/
.m-content {
    display: flex;
    flex-direction: column;
    padding: base.unit-rem-calc(24px);
    height: calc(100% - 57px);
    overflow: auto;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
    }
        .c-c-wrapper {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(8px);
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            background-color: base.$color-white-default;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            box-shadow: base.$elevation-level-2;
            border-radius: base.unit-rem-calc(12px);
            margin-bottom: base.unit-rem-calc(16px);
        }
        .c-cw-header {
            display: flex;
            justify-content: space-between;
            gap: base.unit-rem-calc(16px);
            margin-bottom: base.unit-rem-calc(8px);
            .c-cwh-button {
                @include base.button-text-icon-tertiary;
            }
        }
            .c-cwh-button {
                @include base.button-text-icon-tertiary;
                padding-right: base.unit-rem-calc(7px) !important;
            }
        .c-cw-section {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(8px);
            &.t-hidden {
                display: none;
            }
        }
            .c-cws-row {
                min-height: base.unit-rem-calc(24px);
                display: flex;
                align-items: start;
                gap: base.unit-rem-calc(8px);
                >p {
                    @include base.typo-paragraph-medium;
                    margin-bottom: 0;
                }
                &.t-hidden {
                    display: none;
                }
                &.t-tasks {
                    margin-top: base.unit-rem-calc(4px);
                }
            }
                .c-cwsr-title {
                    min-height: base.unit-rem-calc(24px);
                    display: flex;
                    align-items: center;
                    >p {
                        @include base.typo-paragraph-medium;
                        margin-bottom: 0;
                    }
                    &.t-hidden {
                        display: none;
                    }
                }
                .c-cwsr-line {
                    flex: 1;
                    min-width: base.unit-rem-calc(16px);
                    border-bottom: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
                    padding-top: base.unit-rem-calc(12px);
                }
                .c-cwsr-button {
                    display: flex;
                    align-items: center;
                    gap: base.unit-rem-calc(8px);
                    height: base.unit-rem-calc(24px);
                    > a {
                        color: base.$color-primary-default;
                    }
                    > svg {
                        @include base.svg-icon('default-18');
                        color: base.$color-primary-default;
                    }
                    @include base.respond-to('hover') {
                        &:hover {
                            > a, svg {
                                color: base.$color-primary-light-1;
                            }
                        }
                    }
                }
                .c-cwsr-content-wrapper {
                    min-height: base.unit-rem-calc(24px);
                    display: flex;
                    align-items: center;
                    color: base.$color-grey-dark-1;
                    text-align: right;
                    text-align: -webkit-right;
                    &.t-status {
                        justify-content: space-between;
                        align-items: flex-start;
                    }
                    > p {
                        margin-bottom: 0;
                    }
                    &.t-email {
                        word-break: break-word;
                    }
                }
                    .c-cwsrc-content {
                        margin: 0;
                        .h-text {
                            height: base.unit-rem-calc(24px);
                            line-height: 1.4;
                        }
                    }
            .c-cws-actions {
                display: flex;
                gap: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(3px);
                min-height: base.unit-rem-calc(40px);
                border-radius: base.unit-rem-calc(40px);
                background: base.$color-background-form;
                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                margin: 0 base.unit-rem-calc(-4px);
                .c-cwsa-button {
                    @include base.button-text-icon-tertiary;
                    flex: 1;
                    border-radius: base.unit-rem-calc(32px);
                    background: transparent;
                    transition: all 0.2s ease-in-out;
                    &.t-convert {
                        display: none;
                        color: base.$color-green-default;
                        @include base.respond-to('hover') {
                            &:hover {
                                background: base.$color-green-default;
                                color: base.$color-white-default;
                            }
                        }
                        &.t-show {
                            display: inline-flex;
                        }
                    }
                    &.t-working {
                        display: none;
                        @include base.respond-to('hover') {
                            &:hover {
                                background: base.$color-primary-default;
                                color: base.$color-white-default;
                            }
                        }
                        &.t-show {
                            display: inline-flex;
                        }
                    }
                    &.t-kill {
                        display: none;
                        color: base.$color-red-default;
                        @include base.respond-to('hover') {
                            &:hover {
                                background: base.$color-red-default;
                                color: base.$color-white-default;
                            }
                        }
                        &.t-show {
                            display: inline-flex;
                        }
                    }
                }
            }
    .c-c-tasks {
        display: flex;
        justify-content: space-between;
        align-items: end;
        gap: base.unit-rem-calc(8px);
        margin-bottom: base.unit-rem-calc(16px);
        .c-ca-button {
            &.t-tertiary {
                @include base.button-text-icon-tertiary;
                padding: 0 base.unit-rem-calc(8px) !important;
            }
            &.t-primary {
                @include base.button-text-primary($rounded: true);
            }
        }
    }
    .c-c-notes {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(12px) base.unit-rem-calc(8px);
        margin-bottom: base.unit-rem-calc(16px);
        border-radius: base.unit-rem-calc(12px);
        background: linear-gradient(180deg, rgba(255, 223, 142, 1.00) 0%,rgba(255, 214, 109, 1.00) 100%);
        &::before {
            content:"";
            position: absolute;
            bottom: base.unit-rem-calc(-1px);
            right: base.unit-rem-calc(-1px);
            border-style: solid;
            border-width: base.unit-rem-calc(32px) base.unit-rem-calc(32px) 0 0;
            border-color: base.$color-yellow-light-3 base.$color-white-default;
            box-shadow: base.unit-rem-calc(-2px) base.unit-rem-calc(-2px) base.unit-rem-calc(4px) base.unit-rem-calc(-1px) rgba(153, 112, 0, 0.20);
            border-radius: base.unit-rem-calc(16px) 0 0 0;
        }
        &::after {
            z-index: -1;
            position: absolute;
            content: "";
            bottom: base.unit-rem-calc(8px);
            right: base.unit-rem-calc(40px);
            width: calc(100% - 48px);
            top: 80%;
            background: transparent;
            box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.25);
            transform: rotate(1deg);
        }
        &.t-hidden {
            display: none;
        }
        .c-cn-content {
            padding: 0 base.unit-rem-calc(8px);
            &.t-hidden {
                display: none;
            }
            .c-cnc-title.t-header, {
                margin-bottom: base.unit-rem-calc(8px);
            }
        }
        .c-cn-line {
            border-bottom: base.unit-rem-calc(1.5px) dashed base.$color-yellow-light-3;
            &.t-hidden {
                display: none;
            }
        }
    }
}