<?php

declare(strict_types=1);

namespace Common\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use ZxcvbnPhp\Zxcvbn;

/**
 * Class InternetRule
 *
 * @package Common\Components\Validation\Rules
 */
class InternetRule extends \Core\Components\Validation\Rules\InternetRule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return array_merge(parent::getMessages(), [
            'password' => '{label} is not valid - {warning}'
        ]);
    }

    /**
     * Verify password meets minimum score
     *
     * @param string $field
     * @param int $min_score
     * @return bool|array
     */
    #[RuleAttribute('password')]
    public function password(mixed $field, mixed $min_score): bool|array
    {
        $result = (new Zxcvbn())->passwordStrength((string) $field);
        if ($result['score'] >= (int) $min_score) {
            return true;
        }
        return ['password', [
            'warning' => !empty($result['feedback']['warning']) ? $result['feedback']['warning'] : 'Does not minimum score requirements'
        ]];
    }
}
