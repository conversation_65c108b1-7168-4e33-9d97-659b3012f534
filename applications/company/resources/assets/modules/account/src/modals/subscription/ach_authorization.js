'use strict';

const Modal = require('@ca-submodule/modal').Legacy;
const modal_ach_tpl = require('@cam-account-tpl/modals/subscription/ach_authorization.hbs');

const lang = require('lodash/lang');

module.exports = class extends Modal {
    constructor() {
        super(Modal.Size.TINY, false, modal_ach_tpl());

        this.elem.close = this.elem.root.find('.close');
        this.elem.confirm = this.elem.root.find('.confirm');
        this.elem.name = this.elem.root.find('[data-name]');
        this.elem.number = this.elem.root.find('[data-number]');
        this.elem.date = this.elem.root.find('[data-date]');
        this.elem.total = this.elem.root.find('[data-total]');

        this.elem.close.on('click.fx', (e) => {
            e.preventDefault();
            this.close();
            return false;
        });

        this.elem.confirm.on('click.fx', (e) => {
            e.preventDefault();
            this.fire('confirm');
            this.close();
            return false;
        });
    };

    open(config) {
        if (lang.isObject(config)) {
            this.elem.name.text(config.name);
            this.elem.number.text(config.number);
            this.elem.date.text(config.date);
            this.elem.total.text(config.total);

            super.open();
            this.elem.root.scrollTop(0);
        }
    };
};
