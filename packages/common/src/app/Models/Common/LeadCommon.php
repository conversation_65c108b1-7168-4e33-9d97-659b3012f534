<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\Customer;
use Common\Models\ProjectType;
use Common\Models\MarketingType;
use Common\Models\Task;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;

trait LeadCommon
{
    use ScopeSearchTrait;

    protected $castMap = [
        'leadID' => 'int',
        'companyID' => 'int',
        'status' => 'int',
        'origin' => 'int',
        'priority' => 'int',
        'marketingTypeID' => 'int',
        'assignedToUserID' => 'int',
        'convertedByUserID' => 'int',
        'workingByUserID' => 'int',
        'deadByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['workingAt', 'convertedAt', 'deadAt'];
    protected $searchColumns = [
        'email', 'businessName', 'firstName', 'lastName', 'address', 'address2', 'city', 'state', 'zip', 'phoneNumber',
        'notes', 'workingNotes', 'deadNotes'
    ];

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assignedToUserID', 'userID');
    }

    public function customer()
    {
        return $this->hasOne(Customer::class, 'leadID');
    }

    public function convertedByUser()
    {
        return $this->belongsTo(User::class, 'convertedByUserID', 'userID');
    }

    public function deadByUser()
    {
        return $this->belongsTo(User::class, 'deadByUserID', 'userID');
    }

    public function projectType()
    {
        return $this->belongsTo(ProjectType::class, 'projectTypeID', 'projectTypeID');
    }

    public function marketingType()
    {
        return $this->belongsTo(MarketingType::class, 'marketingTypeID', 'marketingTypeID');
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'tasks', 'associationType', 'associationID');
    }

    public function workingByUser()
    {
        return $this->belongsTo(User::class, 'workingByUserID', 'userID');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        $query->where("{$this->table}.assignedToUserID", $user)
            ->orWhere("{$this->table}.createdByUserID", $user);
    }
}
