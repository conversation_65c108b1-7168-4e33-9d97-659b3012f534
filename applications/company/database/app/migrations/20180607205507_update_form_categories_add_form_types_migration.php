<?php

use App\Classes\DB\Migration;
use Carbon\Carbon;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

class UpdateFormCategoriesAddFormTypesMigration extends Migration
{
    public function up()
    {
        $this->updateTable('formCategories')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('status')->after('type');
                $table->dateTime('archivedAt')->nullable()->after('order');
                $table->unsignedInteger('archivedByUserID')->nullable()->after('archivedAt');
            })
            ->alter();

        $this->newTable('formTypes')
            ->primaryKey('formTypeID', false)
            ->columns(function (Blueprint $table) {
                $table->string('name', 100);
            })
            ->timestamps(false, false)
            ->useHistory(false)
            ->create();

        $now = Carbon::now('UTC');
        DB::table('formTypes')->insert([
            [
                'formTypeID' => 14,
                'name' => 'Bid',
                'createdAt' => $now,
                'updatedAt' => $now
            ]
        ]);
    }

    public function down()
    {
        $this->updateTable('formCategories')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['status', 'archivedAt', 'archivedByUserID']);
            })
            ->alter();

        $this->dropTables(['formTypes']);
    }
}
