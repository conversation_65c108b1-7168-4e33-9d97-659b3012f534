<?php

namespace App\Classes\FX\Reports;

use App\Classes\FX\Report;
use App\Traits\FX\Report\StartEndDateTrait;

/**
 * Class EmployeeCustomerLeadReport
 *
 * Note: this was moved from old SalesReport class and modified slightly
 *
 * @package App\Classes\FX\Reports
 */
class EmployeeCustomerLeadReport extends Report
{
    use StartEndDateTrait;

    /**
     * Run report
     *
     * @return array
     * @throws \Exception
     */
    public function run()
    {
        $sql = <<<SQL
SELECT
  leads,
  costPerLead,
  bids,
  averageProjectBid,
  sales,
  averageProjectSold,
  grossSales,
  grossBids,
  leadsResult.employee
FROM
  (
      (SELECT
         IFNULL(project.projectSalesperson, 0) AS projectSalesperson_1,
         COUNT(*) AS leads,
         IF(
             project.projectSalesperson IS NULL,
             '<Unspecified Salesperson>',
             CONCAT(u.userFirstName, ' ', u.userLastName)
         ) AS employee,
         IFNULL(totalMarketingCosts, 0) AS totalMarketingCosts,
         IFNULL((totalMarketingCosts / COUNT(*)), 0) AS costPerLead
       FROM customer
         INNER JOIN
         (SELECT
            MIN(projects_result.projectID) AS first_projectID,
            projects_result.customerID
          FROM
            (SELECT
               p_0.projectID,
               p_0.projectSalesperson,
               p_0.customerID
             FROM project AS p_0
               JOIN customer AS c_1
                 ON c_1.customerID = p_0.customerID
             WHERE c_1.companyID = :companyID
                   AND c_1.createdAt >= :startDate
                   AND c_1.createdAt <= :endDate
                   AND c_1.deletedAt IS NULL
                   AND p_0.deletedAt IS NULL
            ) AS projects_result
          GROUP BY customerID
         ) AS projects_result_aggregate ON projects_result_aggregate.customerID = customer.customerID
         JOIN project ON projects_result_aggregate.first_projectID = project.projectID
         LEFT JOIN user AS u ON project.projectSalesperson = u.userID
         JOIN
         (SELECT SUM(spendAmount) AS totalMarketingCosts
          FROM marketingSpend AS s
            LEFT JOIN marketingType AS t
              ON t.marketingTypeID = s.marketingTypeID
          WHERE t.companyID = :companyID
                AND s.startDate <= :endDate
                AND s.endDate >= :startDate
         )
           AS marketingCostsResult
       WHERE customer.companyID = :companyID
         AND customer.deletedAt IS NULL
       GROUP BY projectSalesperson_1) AS leadsResult

      LEFT JOIN

      (SELECT
         IFNULL(project.projectSalesperson, 0) AS projectSalesperson_1,
         SUM(IF(projects_result_aggregate.bids > 0, 1, 0)) AS bids,
         SUM(projects_result_aggregate.bids)               AS grossBids,
         IFNULL((SUM(projects_result_aggregate.bids) / SUM(IF(projects_result_aggregate.bids > 0, 1, 0))), 0) AS averageProjectBid
       FROM customer
         INNER JOIN
         (SELECT
            MIN(projects_result.projectID) AS first_projectID,
            projects_result.customerID,
            SUM(projects_result.bids)      AS bids
          FROM
            (SELECT
               p_0.projectID,
               p_0.customerID,
               IFNULL(bids.bids, 0) + IFNULL(customBids.bids, 0) AS bids
             FROM project AS p_0
               LEFT JOIN
               (SELECT
                  p_1.projectID,
                  SUM(IF(eb.bidTotal IS NULL, 0, eb.bidTotal + IFNULL(eb.bidScopeChangeTotal, 0))) AS bids,
                  c_1.customerID
                FROM evaluation AS e
                  LEFT JOIN evaluationBid AS eb
                    ON eb.evaluationID = e.evaluationID
                  JOIN project AS p_1
                    ON p_1.projectID = e.projectID
                  JOIN customer AS c_1
                    ON c_1.customerID = p_1.customerID
                WHERE c_1.companyID = :companyID
                      AND p_1.projectCancelled IS NULL
                      AND e.evaluationCancelled IS NULL
                      AND e.deletedAt IS NULL
                      AND eb.bidFirstSent IS NOT NULL
                      AND eb.bidFirstSent >= :startDate
                      AND eb.bidFirstSent <= :endDate
                GROUP BY e.projectID
               ) AS bids ON p_0.projectID = bids.projectID
               LEFT JOIN
               (SELECT
                  p_1.projectID,
                  SUM(IF(ec.bidTotal IS NULL, 0, ec.bidTotal + IFNULL(ec.bidScopeChangeTotal, 0))) AS bids,
                  c_1.customerID
                FROM evaluation AS e
                  LEFT JOIN customBid AS ec
                    ON ec.evaluationID = e.evaluationID
                  JOIN project AS p_1
                    ON p_1.projectID = e.projectID
                  JOIN customer AS c_1
                    ON c_1.customerID = p_1.customerID
                WHERE c_1.companyID = :companyID
                      AND p_1.projectCancelled IS NULL
                      AND e.evaluationCancelled IS NULL
                      AND e.deletedAt IS NULL
                      AND ec.bidFirstSent IS NOT NULL
                      AND ec.bidFirstSent >= :startDate
                      AND ec.bidFirstSent <= :endDate
                GROUP BY e.projectID
               ) AS customBids ON p_0.projectID = customBids.projectID
               JOIN customer AS c_1
                 ON c_1.customerID = p_0.customerID
             WHERE c_1.companyID = :companyID
                   AND p_0.projectCancelled IS NULL
                   AND p_0.deletedAt IS NULL
            ) AS projects_result
          GROUP BY customerID
         ) AS projects_result_aggregate ON projects_result_aggregate.customerID = customer.customerID
         JOIN project ON projects_result_aggregate.first_projectID = project.projectID
         LEFT JOIN user AS u ON project.projectSalesperson = u.userID
         JOIN
         (SELECT SUM(spendAmount) AS totalMarketingCosts
          FROM marketingSpend AS s
            LEFT JOIN marketingType AS t
              ON t.marketingTypeID = s.marketingTypeID
          WHERE t.companyID = :companyID
                AND s.startDate <= :endDate
                AND s.endDate >= :startDate
         )
           AS marketingCostsResult
       WHERE customer.companyID = :companyID
             AND customer.deletedAt IS NULL
             AND project.projectCancelled IS NULL
       GROUP BY projectSalesperson_1) AS bidsResult
        ON bidsResult.projectSalesperson_1 = leadsResult.projectSalesperson_1

      LEFT JOIN

      (SELECT
         IFNULL(project.projectSalesperson, 0) AS projectSalesperson_1,
         SUM(
             projects_result_aggregate.sales)               AS grossSales,
         SUM(IF(projects_result_aggregate.sales > 0, 1, 0)) AS sales,
         IFNULL((SUM(projects_result_aggregate.sales)
                 / SUM(IF(projects_result_aggregate.sales > 0, 1, 0))),
                0)                                          AS averageProjectSold
       FROM customer
         INNER JOIN
         (SELECT
            MIN(projects_result.projectID) AS first_projectID,
            projects_result.customerID,
            SUM(projects_result.sales)     AS sales
          FROM
            (SELECT
               p_0.projectID,
               p_0.customerID,
               IFNULL(bids.sales, 0) + IFNULL(customBids.sales, 0) AS sales
             FROM project AS p_0
               LEFT JOIN
               (SELECT
                  p_1.projectID,
                  SUM(IF(eb.bidTotal IS NULL, 0, eb.bidTotal + IFNULL(eb.bidScopeChangeTotal, 0))) AS sales,
                  c_1.customerID
                FROM evaluation AS e
                  LEFT JOIN evaluationBid AS eb
                    ON eb.evaluationID = e.evaluationID
                  JOIN project AS p_1
                    ON p_1.projectID = e.projectID
                  JOIN customer AS c_1
                    ON c_1.customerID = p_1.customerID
                WHERE c_1.companyID = :companyID
                      AND p_1.projectCancelled IS NULL
                      AND e.evaluationCancelled IS NULL
                      AND e.deletedAt IS NULL
                      AND eb.bidFirstSent IS NOT NULL
                      AND eb.bidAccepted IS NOT NULL
                      AND eb.bidAccepted >= :startDate
                      AND eb.bidAccepted <= :endDate
                GROUP BY e.projectID
               ) AS bids ON p_0.projectID = bids.projectID
               LEFT JOIN
               (SELECT
                  p_1.projectID,
                  SUM(IF(ec.bidTotal IS NULL, 0, ec.bidTotal + IFNULL(ec.bidScopeChangeTotal, 0))) AS sales,
                  c_1.customerID
                FROM evaluation AS e
                  LEFT JOIN customBid AS ec
                    ON ec.evaluationID = e.evaluationID
                  JOIN project AS p_1
                    ON p_1.projectID = e.projectID
                  JOIN customer AS c_1
                    ON c_1.customerID = p_1.customerID
                WHERE c_1.companyID = :companyID
                      AND p_1.projectCancelled IS NULL
                      AND e.evaluationCancelled IS NULL
                      AND e.deletedAt IS NULL
                      AND ec.bidFirstSent IS NOT NULL
                      AND ec.bidAccepted IS NOT NULL
                      AND ec.bidAccepted >= :startDate
                      AND ec.bidAccepted <= :endDate
                GROUP BY e.projectID
               ) AS customBids ON p_0.projectID = customBids.projectID
               JOIN customer AS c_1
                 ON c_1.customerID = p_0.customerID
             WHERE c_1.companyID = :companyID
                   AND p_0.projectCancelled IS NULL
                   AND p_0.deletedAt IS NULL
            ) AS projects_result
          GROUP BY customerID
         ) AS projects_result_aggregate ON projects_result_aggregate.customerID = customer.customerID
         JOIN project ON projects_result_aggregate.first_projectID = project.projectID
         LEFT JOIN user AS u ON project.projectSalesperson = u.userID
         JOIN
         (SELECT SUM(spendAmount) AS totalMarketingCosts
          FROM marketingSpend AS s
            LEFT JOIN marketingType AS t
              ON t.marketingTypeID = s.marketingTypeID
          WHERE t.companyID = :companyID
                AND s.startDate <= :endDate
                AND s.endDate >= :startDate
         )
           AS marketingCostsResult
       WHERE customer.companyID = :companyID
             AND customer.deletedAt IS NULL
             AND project.projectCancelled IS NULL
       GROUP BY projectSalesperson_1) AS salesResult
        ON salesResult.projectSalesperson_1 = leadsResult.projectSalesperson_1)
SQL;

        $query = $this->getDb()->prepare($sql);
        $query->bindValue(':companyID', $this->getCompanyID());
        $query->bindValue(':startDate', $this->getStartDate()->toDateTimeString());
        $query->bindValue(':endDate', $this->getEndDate()->toDateTimeString());
        $query->execute();

        $results = $this->fetchResults($query);

        $headers = [
            'employee' => [
                'name' => 'Employee',
                'description' => ''
            ],
            'leads' => [
                'name' => 'Leads',
                'description' => ''
            ],
            'bids' => [
                'name' => 'Bids',
                'description' => ''
            ],
            'sales' => [
                'name' => 'Sales',
                'description' => ''
            ],
            'grossSales' => [
                'name' => 'Gross Sales',
                'description' => 'Sum of Accepted Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'grossBids' => [
                'name' => 'Gross Bids',
                'description' => 'Sum of All Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'costPerLead' => [
                'name' => 'Average Cost Per Lead',
                'description' => 'Cost Per Lead = Total Marketing Costs/Leads',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'averageProjectBid' => [
                'name' => 'Average Project Bid',
                'description' => 'Average Project Bid = Gross Bids/Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'averageProjectSold' => [
                'name' => 'Average Project Sold',
                'description' => 'Average Project Sold = Gross Sales/Sales',
                'type' => Report::VALUE_TYPE_MONEY
            ]
        ];

        $return = [
            'headers' => $headers,
            'data' => []
        ];
        foreach ($results as $employee_data) {
            foreach ($employee_data as $key => &$value) {
                if (!isset($headers[$key]['type'])) {
                    continue;
                }
                $value = $this->formatValue($headers[$key]['type'], $value);
            }
            $return['data'][] = $employee_data;
        }
        return $return;
    }
}
