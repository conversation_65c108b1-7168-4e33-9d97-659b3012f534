<?php

namespace Core\Components\Http\Interfaces\Route;

/**
 * Interface BuilderInterface
 *
 * @package Core\Components\Http\Interfaces\Route
 */
interface BaseBuilderInterface
{
    /**
     * @param string $name
     * @return $this
     */
    public function name($name);

    /**
     * @param string $name
     * @param string $regex
     * @param array $config
     * @return $this
     */
    public function bind($name, $regex, array $config = []);

    /**
     * @param string $name
     * @param array $args
     * @return $this
     */
    public function middleware($name, array $args = []);

    /**
     * @param string $name
     * @return $this
     */
    public function middlewareGroup($name);

    /**
     * @return string
     */
    public function getName();

    /**
     * @return string
     */
    public function getPrefix();

    /**
     * @return string
     */
    public function getControllerPrefix();

    /**
     * @return array
     */
    public function getBindings();

    /**
     * @return array
     */
    public function getMiddleware();
}
