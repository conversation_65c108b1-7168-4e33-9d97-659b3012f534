<?php

namespace App\ResourceDelegates;

use App\Classes\Acl;
use App\Classes\Func;
use App\ResourceJobs\Company\GoogleDisconnectJob;
use App\ResourceJobs\Company\MarketingListPushJob;
use App\ResourceJobs\Company\SupportListPushJob;
use App\ResourceMediaHandlers\Company\Logo\DocumentThumbnailHandler;
use App\ResourceMediaHandlers\Company\Logo\DrawingThumbnailHandler;
use App\ResourceMediaHandlers\Company\Logo\EmailThumbnailHandler;
use App\ResourceMediaHandlers\Company\Logo\ProfileThumbnailHandler;
use App\ResourceMediaHandlers\Company\LogoHandler;
use App\Resources\Company\IntakeResource;
use App\Resources\Company\Invoice\CreditResource;
use App\Resources\Company\PaymentMethodResource;
use App\Resources\Company\PhoneResource;
use App\Resources\Company\SetupResource;
use App\Resources\Company\SubscriptionResource as CompanySubscriptionResource;
use App\Resources\CompanyResource;
use App\Resources\CustomerResource;
use App\Resources\Customer\PhoneResource as CustomerPhoneResource;
use App\Resources\EmailTemplateResource;
use App\Resources\FileResource;
use App\Resources\ProjectTypeResource;
use App\Resources\Project\EventResource;
use App\Resources\ProjectResource;
use App\Resources\PropertyResource;
use App\Resources\RegistrationResource;
use App\Resources\ResultTypeResource;
use App\Resources\SubscriptionResource;
use App\Resources\UserResource;
use App\Services\SuccessManagerAssignmentService;
use App\Services\CompanyBidDefaultsService;
use App\Services\CompanySettingService;
use App\Services\PaymentService;
use App\Services\TrainingService;
use App\Services\WisetackService;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use App\Traits\ResourceDelegate\TrainingActionTrait;
use Carbon\Carbon;
use Common\Models\{BidItemInstallmentPaymentTermInstallment,
    Company,
    MarketingType,
    ProjectCostCategory,
    ProjectCostType,
    Timezone,
    TrainingAction};
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Core\StaticAccessors\App;
use Core\StaticAccessors\Path;
use Faker\Factory;
use LasseRafn\InitialAvatarGenerator\InitialAvatar;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

class CompanyDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;
    use TrainingActionTrait;

    /**
     * Map of new timezone id's to legacy system values
     *
     * timezone_id => [<hour offset>, <observes daylight savings>]
     *
     * @var array
     */
    protected $timezones = [
        1 => ['-18000', true],
        2 => ['-21600', true],
        3 => ['-25200', true],
        4 => ['-25200', false],
        5 => ['-28800', true],
        6 => ['-32400', true],
        7 => ['-36000', true],
        8 => ['-36000', false]
    ];

    /**
     * Build relation list shared by all resource instances
     *
     * @param RelationList $list
     * @return RelationList
     */
    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('credits')->modelRelation('invoiceCredits')->resource(CreditResource::class);
        $list->oneOrMany('current_subscription')->modelRelation('currentSubscription')
            ->resource(CompanySubscriptionResource::class);
        $list->oneOrMany('default_payment_method')->modelRelation('defaultPaymentMethod')
            ->resource(PaymentMethodResource::class);
        $list->oneOrMany('email_templates')->modelRelation('emailTemplates')->resource(EmailTemplateResource::class);
        $list->oneOrMany('intake')->resource(IntakeResource::class);
        $list->oneOrMany('project_types')->modelRelation('projectTypes')->resource(ProjectTypeResource::class);
        $list->oneOrMany('result_types')->modelRelation('resultTypes')->resource(ResultTypeResource::class);
        $list->oneOrMany('logo')->resource(FileResource::class);
        $list->oneOrMany('payment_methods')->modelRelation('paymentMethods')
            ->resource(PaymentMethodResource::class);
        $list->oneOrMany('phones')->resource(PhoneResource::class);
        $list->oneOrMany('registration')->resource(RegistrationResource::class);
        $list->oneOrMany('setup')->resource(SetupResource::class);
        $list->custom('settings')
            ->dataCallback(function (Company $company, Entity $entity) {
                $entity->set('settings', (new CompanySettingService($company->getKey()))->getList());
                return $entity;
            });
        $list->oneOrMany('subscription')->resource(SubscriptionResource::class);
        $list->oneOrMany('subscriptions')->resource(CompanySubscriptionResource::class);
        $list->oneOrMany('users')->resource(UserResource::class);

        return $list;
    }

    /**
     * Configure relation list for specific resource instance
     *
     * @param RelationList $list
     * @param CompanyResource $resource
     * @return RelationList
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function configureRelations(RelationList $list, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        // if a user exists and they are not a primary, we disable the settings relationship
        if ($user !== null && !$user->primary) {
            $list->get('settings')->disable();
        }
        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('companyID')
            ->noSave()
            ->onAction(CompanyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('reseller_id')
            ->column('resellerID')
            ->validation('Reseller Id', 'required|type[int]');

        $list->field('success_manager_id')
            ->column('successManagerID', true)
            ->validation('Success Manager Id', 'required|type[int]')
            ->immutable();

        $list->field('subscription_id')
            ->column('subscriptionID', true)
            ->validation('Subscription Id', 'required|type[int]|check_subscription_id')
            ->immutable();

        $list->field('is_subscription_locked')
            ->column('isSubscriptionLocked')
            ->validation('Is Subscription Locked', 'required|type[bool]')
            ->immutable();

        $list->field('registration_id')
            ->typeUuid()
            ->column('registrationID')
            ->validation('Registration Id', 'nullable|optional|uuid|check_registration_id');

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]|check_status')
            ->onAction([CompanyResource::ACTION_CREATE], function (Field $field) {
                return $field->disable();
            });

        $list->field('signup_status')
            ->validation('Signup Status', 'required|type[int]|in_array[signup_statuses]')
            ->column('signupStatus')
            ->onAction([CompanyResource::ACTION_CREATE], function (Field $field) {
                return $field->disable();
            });

        $list->field('trial_at')
            ->typeDateTime()
            ->column('trialAt')
            ->immutable();

        $list->field('first_active_at')
            ->typeDateTime()
            ->column('firstActiveAt')
            ->immutable();

        $list->field('active_at')
            ->typeDateTime()
            ->column('activeAt')
            ->immutable();

        $list->field('suspended_at')
            ->typeDateTime()
            ->column('suspendedAt')
            ->immutable();

        $list->field('trial_expires_at')
            ->typeDateTime()
            ->column('trialExpiresAt')
            ->immutable();

        $list->field('is_setup_wizard')
            ->column('isSetupWizard');

        $list->field('setup_wizard_step')
            ->column('setupWizardStep');

        $list->field('setup_wizard_completed_at')
            ->typeDateTime()
            ->column('setupWizardCompletedAt');

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[255]');

        $list->field('address')
            ->validation('Address', 'trim|nullable|optional|max_length[100]');

        $list->field('address_2')
            ->column('address2')
            ->validation('Address 2', 'trim|nullable|optional|max_length[50]');

        $list->field('city')
            ->validation('City', 'trim|nullable|optional|max_length[40]');

        $list->field('state')
            ->validation('State', 'trim|nullable|optional|max_length[25]');

        $list->field('zip')
            ->validation('Zip', 'trim|nullable|optional|max_length[12]');

        $list->field('billing_address')
            ->column('billingAddress')
            ->validation('Billing Address 1', 'trim|nullable|optional|max_length[100]');

        $list->field('billing_address_2')
            ->column('billingAddress2')
            ->validation('Billing Address 2', 'trim|nullable|optional|max_length[50]');

        $list->field('billing_city')
            ->column('billingCity')
            ->validation('Billing City', 'trim|nullable|optional|max_length[40]');

        $list->field('billing_state')
            ->column('billingState')
            ->validation('Billing State', 'trim|nullable|optional|max_length[25]');

        $list->field('billing_zip')
            ->column('billingZip')
            ->validation('Billing Zip', 'trim|nullable|optional|max_length[12]');

        $list->field('website')
            ->validation('Website', 'trim|nullable|max_length[255]|domain_only');

        // @todo make an on demand field and remove join from global scope
        $list->field('phone_number')
            ->rawColumn('companyPhones.phoneNumber', 'phone_number', true)
            ->immutable();

        $list->field('primary_phone_number')
            ->onDemand()
            ->label('Primary Phone')
            ->query(function ($query, $scope_builder, $table_alias) {
                return $query->join('companyPhones as cp', function ($join) use ($table_alias) {
                    $join->on('cp.companyID', '=', "{$table_alias}.companyID")
                        ->where('cp.isPrimary', true)
                        ->whereNull('cp.deletedAt');
                });
            })
            ->rawColumn('cp.phoneNumber', 'primary_phone_number')
            ->onAction(CompanyResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('logo_file_id')
            ->typeUuid()
            ->column('logoFileID', true)
            ->validation('Logo File Id', 'nullable|optional|uuid|check_logo_file_id');

        $list->field('color')
            ->validation('Color', 'required|hex_color')
            ->saveMutator(function ($value) {
                return "#{$value}";
            })
            ->outputMutator(function ($value) {
                return str_replace('#', '', $value);
            });

        $list->field('color_hover')
            ->column('colorHover')
            ->validation('Color Hover', 'required|hex_color')
            ->saveMutator(function ($value) {
                return "#{$value}";
            })
            ->outputMutator(function ($value) {
                return str_replace('#', '', $value);
            });

        $list->field('cover_letter')
            ->column('coverLetter')
            ->validation('Cover Letter', 'nullable');

        $list->field('cover_letter_last_updated')
            ->typeDateTime()
            ->column('coverLetterLastUpdated')
            ->immutable();

        $list->field('email_from')
            ->column('emailFrom')
            ->validation('Email From', 'trim|required|email|max_length[100]')
            ->onAction(CompanyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('trim|email');
            });

        $list->field('email_reply')
            ->column('emailReply')
            ->validation('Email Reply', 'trim|required|email|max_length[100]')
            ->onAction(CompanyResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('trim|email');
            });

        $list->field('default_invoices')
            ->column('defaultInvoices')
            ->validation('Default Invoice', 'required|numeric');

        $list->field('invoice_split_bid_acceptance')
            ->column('invoiceSplitBidAcceptance')
            ->validation('Invoice Split - Bid Acceptance', 'required|type[string]|numeric');

        $list->field('invoice_split_project_complete')
            ->column('invoiceSplitProjectComplete')
            ->validation('Invoice Split - Project Complete', 'required|type[string]|numeric');

        $list->field('timezone_id')
            ->column('timezoneID')
            ->validation('Timezone Id', 'required|type[int]|check_timezone_id');

        $list->field('timezone')
            ->immutable();

        $list->field('daylight_savings')
            ->column('daylightSavings')
            ->immutable();

        $list->field('recently_completed_status')
            ->column('recentlyCompletedStatus')
            ->validation('Recently Completed Status', 'required|numeric');

        $list->field('customer_profile_id')
            ->column('customerProfileID')
            ->immutable()
            ->onAction([CompanyResource::ACTION_UPDATE, CompanyResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->accessLevelPrivate()->mutable()->validation('Customer Profile Id', 'nullable|optional|numeric');
            });

        $list->field('latitude')
            ->validation('Latitude', 'required|coordinate[latitude]');

        $list->field('longitude')
            ->validation('Longitude', 'required|coordinate[longitude]');

        // @todo remove in future in favor of 'status' field
        $list->field('is_active')
            ->column('isActive')
            ->immutable();

        $list->field('zendesk_organization_id')
            ->column('zendeskOrganizationID')
            ->immutable();

        $this->timestampFields($list);

        return $list;
    }

    public function configureFields(FieldList $list, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user !== null && !$user->admin) {
            $list->modify([
                'reseller_id', 'registration_id', 'subscription_id', 'is_subscription_locked', 'status', 'signup_status'
            ], function (Field $field) {
                return $field->immutable()
                    ->onAction([CompanyResource::ACTION_UPDATE, CompanyResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                        return $field->accessLevelPrivate()->mutable();
                    });
            });

            $list->modify(['registration_id'], function (Field $field) {
                return $field->disable();
            });
        } else {
            $list->get('subscription_id')->mutable();
            $list->get('zendesk_organization_id')->mutable();
            $list->get('is_subscription_locked')->onAction([CompanyResource::ACTION_UPDATE, CompanyResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->mutable();
            });
            $list->get('trial_expires_at')->onAction([CompanyResource::ACTION_UPDATE, CompanyResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->mutable();
            });
        }
        return $list;
    }

    public function requestValidationFields(FieldList $list, Request $request)
    {
        $is_creating = $request->isAction(CompanyResource::ACTION_CREATE | CompanyResource::ACTION_NESTED_CREATE);

        $phones = $list->field('phones');
        $rules = 'nullable|optional|type[array]|min_count[1]';
        if ($is_creating) {
            $rules = 'required|type[array]|min_count[1]';
        }
        $phones->validation('Phones', $rules);

        if ($is_creating) {
            // require primary user on create
            $list->field('primary_user')
                ->validation('Primary User', 'required|type[array]');
        }

        $list->field('settings')
            ->validation('Settings', 'nullable|optional|type[array]|min_count[1]');

        $list->field('project_types')
            ->validation('Project Types', 'nullable|optional|type[array]|min_count[1]');

        $list->field('result_types')
            ->validation('Result Types', 'nullable|optional|type[array]');
        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('logo')
            ->directoryName('logos')
            ->urlSlug('company-logos')
            ->versions(function (MediaType $type) {
                $type->original()->handler(LogoHandler::class);
                $type->variant('profile_thumbnail')
                    ->directoryName('profile-thumbnail', true)
                    ->handler(ProfileThumbnailHandler::class);
                $type->variant('email_thumbnail')
                    ->directoryName('email-thumbnail', true)
                    ->handler(EmailThumbnailHandler::class);
                $type->variant('document_thumbnail')
                    ->directoryName('document-thumbnail', true)
                    ->handler(DocumentThumbnailHandler::class);
                $type->variant('drawing_thumbnail')
                    ->directoryName('drawing-thumbnail', true)
                    ->handler(DrawingThumbnailHandler::class);
            });

        return $list;
    }

    public function validationRules(Rules $rules, CompanyResource $resource)
    {
        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [
                    CompanyResource::STATUS_SIGNUP
                ],
                CompanyResource::STATUS_SIGNUP => [
                    CompanyResource::STATUS_TRIAL, CompanyResource::STATUS_ACTIVE
                ],
                CompanyResource::STATUS_TRIAL => [
                    CompanyResource::STATUS_ACTIVE, CompanyResource::STATUS_DORMANT
                ],
                CompanyResource::STATUS_ACTIVE => [
                    CompanyResource::STATUS_DORMANT, CompanyResource::STATUS_SUSPENDED
                ],
                CompanyResource::STATUS_SUSPENDED => [
                    CompanyResource::STATUS_ACTIVE, CompanyResource::STATUS_DORMANT
                ],
                CompanyResource::STATUS_DORMANT => [
                    CompanyResource::STATUS_ACTIVE
                ]
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        $rules->register('check_subscription_id', function ($id) use ($resource) {
            if ($resource->relationResource('subscription')->entityExists($id)) {
                return true;
            }
            return 'check_subscription_id';
        }, [
            'check_subscription_id' => 'Unable to find subscription'
        ]);

        $rules->register('check_registration_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('registration')->entityExists($id->toString())) {
                return true;
            }
            return 'check_registration_id';
        }, [
            'check_registration_id' => 'Unable to find registration'
        ]);

        $rules->register('domain_only', function (&$domain) {
            $strs = ['http://', 'https://', '//'];
            foreach ($strs as $str) {
                if (!str_starts_with($domain, $str)) {
                    continue;
                }
                $domain = str_replace($str, '', $domain);
                break;
            }
            return true;
        });

        $rules->register('check_logo_file_id', function ($id) use ($resource) {
            $logo = $resource->relationResource('logo');
            if (($file = $logo->find($id)) === null) {
                return 'file_not_found';
            }
            if ($file->type !== FileResource::TYPE_COMPANY_LOGO) {
                return 'file_invalid_type';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} is not a company logo'
        ]);

        $rules->register('check_timezone_id', function ($id) {
            if (Timezone::whereKey($id)->count() === 1) {
                return true;
            }
            return 'check_timezone_id';
        }, [
            'check_timezone_id' => 'Unable to find timezone'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', CompanyResource::getStatuses());
        $config->store('signup_statuses', CompanyResource::getSignupStatuses());

        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $model_data['companyUUID'] = Uuid::uuid4()->getBytes();
        $model_data['successManagerID'] = (new SuccessManagerAssignmentService)->getNextSuccessManagerID();
        $model_data['status'] = Company::STATUS_SIGNUP;
        $model_data['signupStatus'] = $request->storage('signup_status_override', Company::SIGNUP_STATUS_ADDRESS);
        $model_data['isSubscriptionLocked'] = 1;
        $model_data['isActive'] = 1;
        $model_data['isSetupWizard'] = 1;
        $model_data['setupWizardStep'] = CompanyResource::SETUP_WIZARD_STEP_INSTRUCTION;

        $now = Carbon::now('UTC');
        $base_path = Path::resource('company-content/');

        $cover_letter = "{$base_path}cover_letter.html";
        if (!file_exists($cover_letter)) {
            throw new AppException('Unable to find file cover letter');
        }
        $model_data['coverLetter'] = file_get_contents($cover_letter);
        $model_data["coverLetterLastUpdated"] = $now;

        // convert timezone id into legacy system values
        [$timezone, $daylight_savings] = $this->timezones[$model_data['timezoneID']];
        $model_data['timezone'] = $timezone;
        $model_data['daylightSavings'] = $daylight_savings ? 1 : 0;

        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $model = $request->getModel();

        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case Company::STATUS_SIGNUP:
                    $model_data['isSubscriptionLocked'] = 1;
                    $model_data['isActive'] = 1;
                case Company::STATUS_ACTIVE:
                    $model_data['isSubscriptionLocked'] = 1;
                    $model_data['suspendedAt'] = null;
                    $model_data['dormantAt'] = null;
                    $model_data['isActive'] = 1;
                    if ($model->firstActiveAt === null) {
                        $model_data['firstActiveAt'] = Carbon::now('UTC');
                    }
                    if ($model->status !== Company::STATUS_SUSPENDED) {
                        $model_data['activeAt'] = Carbon::now('UTC');
                    }
                    break;
                case Company::STATUS_SUSPENDED:
                    $model_data['suspendedAt'] = Carbon::now('UTC');
                    $model_data['dormantAt'] = null;
                    $model_data['isActive'] = 0;
                    break;
                case Company::STATUS_DORMANT:
                    // unlock subscription when a company goes dormant since this means they decided to leave or stop
                    // communicating, they just have to start over like a new company
                    $model_data['isSubscriptionLocked'] = 0;
                    $model_data['suspendedAt'] = null;
                    $model_data['dormantAt'] = Carbon::now('UTC');
                    $model_data['isActive'] = 0;
                    $request->store('dormant', true);
                    break;
            }
        }

        // transition company status to trial or complete if signup status is complete
        if (
            isset($model_data['status']) &&
            $model_data['status'] === $model->status &&
            $model_data['status'] === Company::STATUS_SIGNUP &&
            $model_data['signupStatus'] === Company::SIGNUP_STATUS_COMPLETE
        ) {
            $subscription = $request->resource()->relationResource('subscription')->find($model->subscriptionID);
            $model_data['status'] =  Company::STATUS_TRIAL;
            $model_data['trialAt'] = Carbon::now('UTC');
            // @todo this is old code when we allowed them to start a trial without a subscription.
//            if ($request->storage('subscription_option_id', null) === null && $subscription->isTrial) {
//                $model_data['status'] =  Company::STATUS_TRIAL;
//                $model_data['trialAt'] = Carbon::now('UTC');
//            } else if ($request->storage('subscription_option_id', null) !== null) {
//                $model_data['status'] = Company::STATUS_ACTIVE;
//                $model_data['activeAt'] = Carbon::now('UTC');
//            }

            if ($subscription->isTrial) {
                $start = Carbon::now('UTC');
                if ($subscription->delayIntervalLength !== null) {
                    switch ($subscription->delayIntervalUnit) {
                        case SubscriptionResource::INTERVAL_UNIT_DAY:
                            $start->addDays($subscription->delayIntervalLength);
                            break;
                        case SubscriptionResource::INTERVAL_UNIT_MONTH:
                            $start->addMonths($subscription->delayIntervalLength);
                            break;
                        case SubscriptionResource::INTERVAL_UNIT_YEAR:
                            $start->addYears($subscription->delayIntervalLength);
                            break;
                    }
                }
                $model_data['trialExpiresAt'] = $start->format('Y-m-d');
            }

            if (in_array($model_data['status'], [Company::STATUS_ACTIVE, Company::STATUS_TRIAL])) {
                $company_id = $request->getFields()->primaryField()->outputValueFromModel($model);

                /** @var UserResource $user_resource */
                $user_resource = $request->resource()->relationResource('users');
                $user_id = $user_resource->getFirstPrimaryUserByCompanyID($company_id);
                $primary_user = $user_resource
                    ->entity($user_id)
                    ->scope(Scope::make()->fields(['id', 'email']))
                    ->run();

                $this->createExampleCustomer($company_id, $request, $primary_user);
            }
        }

        $last_updated_fields = [
            'coverLetter'
        ];

        foreach ($last_updated_fields as $field) {
            if (!isset($model_data[$field]) || $model_data[$field] === $model->{$field}) {
                continue;
            }
            $model_data[$field . 'LastUpdated'] = Carbon::now('UTC');
        }

        if (isset($model_data['timezoneID']) && $model_data['timezoneID'] !== $model->timezoneID) {
            [$timezone, $daylight_savings] = $this->timezones[$model_data['timezoneID']];
            $model_data['timezone'] = $timezone;
            $model_data['daylightSavings'] = $daylight_savings ? 1 : 0;
        }

        // check for training actions
        if ($this->isTraining($request)) {
            $training_columns = [
                'website' => TrainingAction::UPDATE_COMPANY_WEBSITE,
                'color' => TrainingAction::UPDATE_COMPANY_COLORS,
                'colorHover' => TrainingAction::UPDATE_COMPANY_COLORS,
                'emailFrom' => TrainingAction::UPDATE_COMPANY_FROM_ADDRESS,
                'emailReply' => TrainingAction::UPDATE_COMPANY_REPLY_TO_ADDRESS
            ];
            foreach ($training_columns as $column => $action) {
                if (!isset($model_data[$column]) || $model_data[$column] === $model->{$column}) {
                    continue;
                }
                $this->completeTrainingAction($request, $action);
            }
        }

        return $model_data;
    }

    /**
     * Create primary user using data nested in create request
     *
     * @param int $company_id
     * @param CreateRequest $request
     * @return Entity
     * @throws ValidationException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    protected function createNestedPrimaryUser($company_id, CreateRequest $request)
    {
        $user = $request->getValidatedEntity()->get('primary_user');
        try {
            $user_defaults = [
                'role_primary' => true,
                'role_project_management' => false,
                'role_sales' => true,
                'role_installation' => true,
                'role_marketing' => false,
                'role_bid_creation' => false,
                'role_bid_verification' => false,
                'role_timecard_approver' => false,
                'role_metrics' => false,
                'calendar_bg_color' => '000000',
                'calendar_text_color' => 'ffffff',
                'is_active' => true,
                'settings' => [
                    'email_notification_task_assigned' => true,
                    'app_notification_task_assigned' => true,

                    'email_notification_task_due' => true,
                    'app_notification_task_due' => true,

                    'email_notification_lead_assigned' => true,
                    'app_notification_lead_assigned' => true,

                    'email_notification_project_assigned' => true,
                    'app_notification_project_assigned' => true,

                    'email_notification_appointment_scheduled' => true,
                    'app_notification_appointment_scheduled' => true,

                    'email_notification_bid_viewed' => true,
                    'app_notification_bid_viewed' => true,

                    'email_notification_bid_accepted' => true,
                    'app_notification_bid_accepted' => true,

                    'email_notification_bid_rejected' => true,
                    'app_notification_bid_rejected' => true,
                ]
            ];

            $entity = Entity::make(array_merge($user, $user_defaults));
            $entity->set('company_id', $company_id);
            return $request->resource()->relationResource('users')
                ->create($entity)
                ->scope(Scope::make())
                ->store('marketing_list_push', false)
                ->store('first_primary_user', true)
                ->nested()
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create primary user - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'primary_user' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createNestedPhones($company_id, CreateRequest $request)
    {
        $phones = $request->getValidatedEntity()->get('phones', []);
        if (count($phones) === 0) {
            return;
        }
        try {
            $request->resource()->relationResource('phones')
                ->batchCreate(Collection::fromArray($phones))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create phones - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'phones' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createDefaults(Acl $acl, $company_id)
    {
        $now = Carbon::now('UTC');
        // create default parent marketing type for Word of Mouth
        $parent_wom = MarketingType::create([
            'marketingTypeName' => 'Word of Mouth',
            'companyID' => $company_id,
            'dateAdded' => $now,
            'dateUpdated' => $now,
            'isRepeatBusiness' => true
        ]);
        $parent_id_wom = $parent_wom->getKey();

        // create default parent marketing type for Print
        $parent_print = MarketingType::create([
            'marketingTypeName' => 'Print',
            'companyID' => $company_id,
            'dateAdded' => $now,
            'dateUpdated' => $now
        ]);
        $parent_id_print = $parent_print->getKey();

        // create default parent marketing type for Digital
        $parent_digital = MarketingType::create([
            'marketingTypeName' => 'Digital',
            'companyID' => $company_id,
            'dateAdded' => $now,
            'dateUpdated' => $now
        ]);
        $parent_id_digital = $parent_digital->getKey();

        // create default parent marketing type for Events
        $parent_events = MarketingType::create([
            'marketingTypeName' => 'Events',
            'companyID' => $company_id,
            'dateAdded' => $now,
            'dateUpdated' => $now
        ]);
        $parent_id_events = $parent_events->getKey();

        // create default parent marketing type for Other
        $parent_other = MarketingType::create([
            'marketingTypeName' => 'Other',
            'companyID' => $company_id,
            'dateAdded' => $now,
            'dateUpdated' => $now
        ]);
        $parent_id_other = $parent_other->getKey();

        $child_marketing_types = [
            [
                'parent_id' => $parent_id_wom,
                'name' => 'Repeat Business',
                'is_repeat_business' => true
            ],
            [
                'parent_id' => $parent_id_wom,
                'name' => 'Customer Referral'
            ],
            [
                'parent_id' => $parent_id_wom,
                'name' => 'Employee Referral'
            ],
            [
                'parent_id' => $parent_id_print,
                'name' => 'Company Vehicle'
            ],
            [
                'parent_id' => $parent_id_print,
                'name' => 'Door Hanger'
            ],
            [
                'parent_id' => $parent_id_print,
                'name' => 'Flyer'
            ],
            [
                'parent_id' => $parent_id_digital,
                'name' => 'Google Ads'
            ],
            [
                'parent_id' => $parent_id_digital,
                'name' => 'Social Media'
            ],
            [
                'parent_id' => $parent_id_digital,
                'name' => 'Website (organic)'
            ],
            [
                'parent_id' => $parent_id_events,
                'name' => 'Home Show'
            ],
            [
                'parent_id' => $parent_id_other,
                'name' => 'Other'
            ]
        ];

        foreach ($child_marketing_types as $type) {
            $is_repeat_business = isset($type['is_repeat_business']) && $type['is_repeat_business'];
            MarketingType::create([
                'marketingTypeName' => $type['name'],
                'parentMarketingTypeID' => $type['parent_id'],
                'companyID' => $company_id,
                'dateAdded' => $now,
                'dateUpdated' => $now,
                'isRepeatBusiness' => $is_repeat_business
            ]);
        }


        // default project cost categories
        $category_commission = ProjectCostCategory::create([
            'companyID' => $company_id,
            'name' => 'Commission',
            'createdAt' => $now,
            'updatedAt' => $now
        ]);
        $category_labor = ProjectCostCategory::create([
            'companyID' => $company_id,
            'name' => 'Labor',
            'createdAt' => $now,
            'updatedAt' => $now
        ]);
        $category_materials = ProjectCostCategory::create([
            'companyID' => $company_id,
            'name' => 'Materials',
            'createdAt' => $now,
            'updatedAt' => $now
        ]);
        $category_other = ProjectCostCategory::create([
            'companyID' => $company_id,
            'name' => 'Other',
            'createdAt' => $now,
            'updatedAt' => $now
        ]);

        $child_cost_types = [
            [
                'title' => 'Commission (User)',
                'category_id' => $category_commission->projectCostCategoryID,
                'type' => ProjectCostType::TYPE_COMMISSION,
                'can_delete' => 0
            ],
            [
                'title' => 'Labor',
                'category_id' => $category_labor->projectCostCategoryID,
                'type' => ProjectCostType::TYPE_GENERAL,
                'can_delete' => 1
            ],
            [
                'title' => 'Materials',
                'category_id' => $category_materials->projectCostCategoryID,
                'type' => ProjectCostType::TYPE_GENERAL,
                'can_delete' => 1
            ],
            [
                'title' => 'Other',
                'category_id' => $category_other->projectCostCategoryID,
                'type' => ProjectCostType::TYPE_GENERAL,
                'can_delete' => 1
            ]
        ];

        foreach ($child_cost_types as $cost_type) {
            ProjectCostType::create([
                'companyID' => $company_id,
                'type' => $cost_type['type'],
                'projectCostCategoryID' => $cost_type['category_id'],
                'title' => $cost_type['title'],
                'canDelete' => $cost_type['can_delete'],
                'createdAt' => $now,
                'updatedAt' => $now
            ]);
        }

        // setup new bid
        $bid_defaults = new CompanyBidDefaultsService($company_id);
        $bid_defaults->run();

        // add payment term installment defaults
        $default_installments = [
            [
                'name' => 'Deposit',
                'due_time_frame' => BidItemInstallmentPaymentTermInstallment::DUE_TIME_FRAME_AT_BID_ACCEPTANCE,
                'amount_type' => BidItemInstallmentPaymentTermInstallment::AMOUNT_TYPE_PERCENTAGE,
                'amount' => '0.5'
            ],
            [
                'name' => 'Final Payment',
                'due_time_frame' => BidItemInstallmentPaymentTermInstallment::DUE_TIME_FRAME_AT_PROJECT_COMPLETION,
                'amount_type' => BidItemInstallmentPaymentTermInstallment::AMOUNT_TYPE_PERCENTAGE,
                'amount' => '0.5'
            ]
        ];
        $setting_service = new CompanySettingService($company_id);
        $setting_service->set('payment_term_installments', $default_installments);
        $setting_service->set('bid_follow_up_notifications', false);
        $setting_service->set('bid_follow_up_notifications_config', null);
        $setting_service->set('appointment_reminder_scheduling_window', 36);
        $setting_service->save();
    }

    protected function createCompanySetup($company_id, CreateRequest $request)
    {
        try {
            $setup_resource = $request->resource()->relationResource('setup');
            $setup_resource->create(Entity::make([
                'company_id' => $company_id
            ]))->run();
        } catch (ValidationException $e) {
            $exception = new ValidationException('Unable to create company setup entry - Reason: %s', $e->getMessage());
            throw $exception;
        }
    }

    protected function createCompanyEmails($company_id, CreateRequest $request)
    {
        // email default configuration
        $emails = [
            [
                'type' => EmailTemplateResource::TYPE_NEW_CUSTOMER,
                'name' => 'New Customer',
                'subject' => '{company_name} Introduction',
                'template' => 'add_customer',
            ],
            [
                'type' => EmailTemplateResource::TYPE_SALES_APPOINTMENT,
                'name' => 'Sales Appointment',
                'subject' => '{company_name} Appointment Confirmation - {address}',
                'template' => 'sales_appointment'
            ],
            [
                'type' => EmailTemplateResource::TYPE_SALES_APPOINTMENT_REMINDER,
                'name' => 'Sales Appointment Reminder',
                'subject' => '{company_name} Appointment Reminder - {address}',
                'template' => 'sales_appointment_reminder'
            ],
            [
                'type' => EmailTemplateResource::TYPE_CUSTOMER_BID,
                'name' => 'Customer Bid',
                'subject' => '{company_name} {bid_name} - {address}',
                'template' => 'bid'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_ACCEPTED,
                'name' => 'Bid Accepted',
                'subject' => '{company_name} Bid Has Been Accepted - {address}',
                'template' => 'bid_accept'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_REJECTED,
                'name' => 'Bid Rejected',
                'subject' => '{company_name} Bid Has Been Rejected - {address}',
                'template' => 'bid_reject'
            ],
            [
                'type' => EmailTemplateResource::TYPE_INSTALLATION_APPOINTMENT,
                'name' => 'Installation Appointment',
                'subject' => '{company_name} Installation Confirmation - {address}',
                'template' => 'installation_appointment'
            ],
            [
                'type' => EmailTemplateResource::TYPE_INSTALLATION_APPOINTMENT_REMINDER,
                'name' => 'Installation Appointment Reminder',
                'subject' => '{company_name} Installation Reminder - {address}',
                'template' => 'installation_appointment_reminder'
            ],
            [
                'type' => EmailTemplateResource::TYPE_WARRANTIES,
                'name' => 'Warranties',
                'subject' => '{company_name} Warranties - {address}',
                'template' => 'final_report'
            ],
            [
                'type' => EmailTemplateResource::TYPE_INVOICE,
                'name' => 'Invoice',
                'subject' => '{company_name} Invoice - {address}',
                'template' => 'invoice'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_FOLLOW_UP,
                'name' => 'Bid Follow-Up 1',
                'subject' => '{company_name} Evaluation and Bid Follow-Up - {address}',
                'template' => 'follow_up_1'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_FOLLOW_UP,
                'name' => 'Bid Follow-Up 2',
                'subject' => '{company_name} Evaluation and Bid Follow-Up - {address}',
                'template' => 'follow_up_2'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_FOLLOW_UP,
                'name' => 'Bid Follow-Up 3',
                'subject' => '{company_name} Evaluation and Bid Follow-Up - {address}',
                'template' => 'follow_up_3'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_FOLLOW_UP,
                'name' => 'Bid Follow-Up 4',
                'subject' => '{company_name} Evaluation and Bid Follow-Up - {address}',
                'template' => 'follow_up_4'
            ],
            [
                'type' => EmailTemplateResource::TYPE_BID_FOLLOW_UP,
                'name' => 'Bid Follow-Up 5',
                'subject' => '{company_name} Evaluation and Bid Follow-Up - {address}',
                'template' => 'follow_up_5'
            ]
        ];

        $new_emails = [];
        $base_path = Path::resource('company-content/');
        foreach ($emails as $email_defaults) {
            $file = "{$base_path}email-templates/{$email_defaults['template']}.html";
            if (!file_exists($file)) {
                throw new AppException('Unable to find file %s', $email_defaults['template']);
            }
            $new_emails[] = [
                'source' => EmailTemplateResource::SOURCE_SYSTEM,
                'type' => $email_defaults['type'],
                'name' => $email_defaults['name'],
                'subject' => $email_defaults['subject'],
                'content' => file_get_contents($file),
                'is_send_from_salesperson' => false,
                'status' => EmailTemplateResource::STATUS_ACTIVE
            ];
        }

        try {
            $request->resource()->relationResource('email_templates')
                ->batchCreate(Collection::fromArray($new_emails))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('owner_id', $company_id);
                    $entity->set('owner_type', EmailTemplateResource::OWNER_TYPE_COMPANY);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create emails - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'emails' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createAuthorizeNetProfile($company_id, CreateRequest $request)
    {
        $resource = $request->resource();

        $name = $resource->getFields()->get('name')->outputValueFromModel($request->getModel());

        /** @var PaymentService $payment_service */
        $payment_service = App::get(PaymentService::class);
        $profile_id = $payment_service->createCustomerProfile($company_id, $name);

        $resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE);

        $resource->partialUpdate(Entity::make([
            'id' => $company_id,
            'customer_profile_id' => $profile_id
        ]))->store('marketing_list_push', false)->run();

        $resource->restoreAccessLevel();

        // if request is rolled back, delete profile with authorize
        $request->attach('rollback', function () use ($payment_service, $profile_id) {
            $payment_service->deleteCustomerProfile($profile_id);
        });
    }

    /**
     * Save settings contained in request (if available)
     *
     * @param int $company_id
     * @param CreateRequest|UpdateRequest $request
     * @throws AppException
     * @throws BatchHandleException
     * @throws ValidationException
     */
    protected function saveSettings($company_id, $request)
    {
        $settings = $request->getValidatedEntity()->get('settings', []);

        if ($settings === null || count($settings) === 0) {
            return;
        }

        try {
            $setting_service = new CompanySettingService($company_id);
            foreach ($settings as $name => $value) {
                $setting_service->set($name, $value);
            }
            $setting_service->save();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to save settings - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'settings' => $errors
                ]);
            }
            throw $exception;
        }
    }

    /**
     * Update project types contained in request (if available)
     *
     * @param int $company_id
     * @param UpdateRequest $request
     * @throws AppException
     * @throws BatchHandleException
     * @throws ValidationException
     */
    protected function updateProjectTypes($company_id, UpdateRequest $request)
    {
        $project_types = $request->getValidatedEntity()->get('project_types', []);
        if ($project_types === null || count($project_types) === 0) {
            return;
        }

        try {
            /** @var ProjectTypeResource $project_types_resource */
            $project_types_resource = $request->resource()->relationResource('project_types');
            $ids = $project_types_resource->batchUpdateOrCreate(Collection::fromArray($project_types))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->run();

            $project_types_resource->archiveMissingProjectTypesByCompany($company_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update project types - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'project_types' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function updateResultTypes($company_id, UpdateRequest $request)
    {
        $result_types = $request->getValidatedEntity()->get('result_types', []);
        if ($result_types === null || count($result_types) === 0) {
            return;
        }

        try {
            /** @var ResultTypeResource $result_types_resource */
            $result_types_resource = $request->resource()->relationResource('result_types');
            $ids = $result_types_resource->batchUpdateOrCreate(Collection::fromArray($result_types))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->run();

            $result_types_resource->archiveMissingResultTypesByCompany($company_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update result types - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'result_types' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createDefaultProjectTypes($company_id, CreateRequest $request)
    {
        $types = [['name' => 'Sample Project Type']];
        $new_types = [];
        foreach ($types as $type) {
            $new_types[] = [
                'name' => $type['name'],
                'status' => ProjectTypeResource::STATUS_ACTIVE
            ];
        }

        try {
            $request->resource()->relationResource('project_types')
                ->batchCreate(Collection::fromArray($new_types))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create default project types - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'project_types' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createDefaultResultTypes($company_id, CreateRequest $request) {
        $types = [
            ['name' => 'Won'],
            ['name' => 'Won - Price'],
            ['name' => 'Won - Competitor'],
            ['name' => 'Lost'],
            ['name' => 'Lost - Price'],
            ['name' => 'Lost - Competitor'],
            ['name' => 'Lost - No authority'],
            ['name' => 'Lost - Bad Timing'],
            ['name' => 'Cancel'],
            ['name' => 'Cancel - No bid needed'],
            ['name' => 'Cancel - Timing'],
            ['name' => 'Cancel - No Show'],
        ];

        $new_types = [];
        foreach ($types as $type) {
            $new_types[] = [
                'name' => $type['name'],
                'status' => ResultTypeResource::STATUS_ACTIVE,
                'type' => ResultTypeResource::TYPE_PROJECT,
            ];
        }

        try {
            $request->resource()->relationResource('result_types')
                ->batchCreate(Collection::fromArray($new_types))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create default result types - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors(['result_types' => $errors]);
            }
            throw $exception;
        }
    }

    /**
     * Create example customer on company
     *
     * @param int $company_id
     * @param UpdateRequest $request
     * @param Entity $primary_user
     */
    protected function createExampleCustomer($company_id, UpdateRequest $request, Entity $primary_user)
    {
        $faker = Factory::create();
        $acl = $request->resource()->acl();
        $model = $request->getModel();

        $acl->setCompanyID($company_id);
        $customer_id = CustomerResource::make($acl)->create(Entity::make([
            'company_id' => $company_id,
            'business_name' => $faker->optional()->company,
            'first_name' => $faker->firstName,
            'last_name' => $faker->lastName,
            'email' => $primary_user->email,
            'address' => $model->address,
            'address_2' => $model->address2,
            'city' => $model->city,
            'state' => $model->state,
            'zip' => $model->zip,
            'is_unsubscribed' => false,
            'phones' => [
                [
                    'type' => CustomerPhoneResource::TYPE_CELL,
                    'number' => '************',
                    'description' => 'Cell',
                    'is_primary' => true
                ]
            ]
        ]))->run();

        $property_id = PropertyResource::make($acl)->create(Entity::make([
            'customer_id' => $customer_id,
            'address' => $model->address,
            'address_2' => $model->address2,
            'city' => $model->city,
            'state' => $model->state,
            'zip' => $model->zip,
            'latitude' => $model->latitude,
            'longitude' => $model->longitude
        ]))->run();

        $project_id = ProjectResource::make($acl)->create(Entity::make([
            'property_id' => $property_id,
            'customer_id' => $customer_id,
            'description' => 'Example Project',
            'salesperson_user_id' => $primary_user->id
        ]))->run();

        $start_at = Carbon::now($model->_timezone->timezone)->nextWeekday()->setTime(8, 0, 0);
        $end_at = $start_at->copy()->addHour();
        EventResource::make($acl)->create(Entity::make([
            'project_id' => $project_id,
            'source' => EventResource::SOURCE_CUSTOMER_ADD,
            'type' => EventResource::TYPE_EVALUATION,
            'user_id' => $primary_user->id,
            'start_at' => $start_at->toIso8601String(),
            'end_at' => $end_at->toIso8601String(),
            'is_all_day' => false,
            'is_pending' => false,
            'send_notifications' => false
        ]))->run();
    }

    /**
     * Create initial logo image for company
     *
     * Used so they can immediately start using the software after creation
     *
     * @param CreateRequest $request
     * @param Entity $primary_user
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     */
    protected function createInitialLogo(CreateRequest $request, Entity $primary_user)
    {
        /** @var CompanyResource $resource */
        $resource = $request->resource();
        /** @var LogoHandler $handler */
        $handler = $resource->getMediaHandler('logo');

        $model = $request->getModel();

        $word_blacklist = ['INC', 'LLC', 'CORP', 'THE', 'AND', 'OF', 'OR'];

        $font_size = [
            1 => 0.5,
            2 => 0.5,
            3 => 0.33,
            4 => 0.25
        ];

        $name = preg_replace('#[^a-zA-Z0-9 ]+#', '', $model->name);
        $name = explode(' ', mb_strtoupper($name));
        $name = array_filter($name, fn(string $word): bool => $word !== '' && !in_array($word, $word_blacklist));
        $name = array_slice($name, 0, count($font_size));
        $name = array_map(fn(string $word): string => mb_substr($word, 0, 1), $name);
        $name = implode('', $name);

        $logo_path = Func::createTempFile(null, false);
        (new InitialAvatar())->name($name)
            ->fontSize($font_size[mb_strlen($name)])
            ->background('#005AD0')
            ->color('#FFFFFF')
            ->length(4) // max length
            ->rounded()
            ->smooth()
            ->size(200)
            ->imagick()
            ->generate()
            ->encode('png')
            ->save($logo_path);

        $request->attach('rollback', function () use ($logo_path) {
            if (!file_exists($logo_path)) {
                return;
            }
            if (unlink($logo_path) === false) {
                throw new AppException('Unable to delete temp logo: %s', $logo_path);
            }
        });

        $handler->save($model, Entity::make()->addFile('file', 'Temp_Logo.png', $logo_path), [
            'created_by_user_id' => $primary_user->id
        ]);
    }

    public function createSaveAfter(CreateRequest $request)
    {
        /** @var Company $model */
        $model = $request->getModel();
        $company_id = $request->getFields()->primaryField()->outputValueFromModel($model);
        $acl = $request->resource()->acl()->setCompanyID($company_id);

        $primary_user = $this->createNestedPrimaryUser($company_id, $request);
        $this->createNestedPhones($company_id, $request);
        $this->createCompanySetup($company_id, $request);
        $this->createCompanyEmails($company_id, $request);
        $this->createDefaults($acl, $company_id);
        $this->saveSettings($company_id, $request);
        $this->createDefaultProjectTypes($company_id, $request);
        $this->createDefaultResultTypes($company_id, $request);

        $this->createInitialLogo($request, $primary_user);
        TrainingService::assignCompanyModules($model);

        /** @var UserResource $user_resource */
        $user_resource = $request->resource()->relationResource('users');
        $user_resource->pushUserInfoByCompanyID($request->getModel()->getKey());

        if ($request->storage('marketing_list_push', true)) {
            MarketingListPushJob::enqueue($company_id);
        }
        SupportListPushJob::enqueue($company_id);

        // handle last to prevent rollbacks from causing too many external requests
        $this->createAuthorizeNetProfile($company_id, $request);
    }

    public function anyUpdateSaveBefore(UpdateRequest $request, $model_data)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (array_key_exists('logoFileID', $model_data) && $model_data['logoFileID'] === null && $model->logoFileID !== null) {
            /** @var FileResource $logo_resource */
            $logo_resource = $resource->relationResource('logo');
            $logo_resource->delete(Entity::make([
                'id' => $resource->getFields()->get('logo_file_id')->outputValueFromModel($model)
            ]))
                ->run();
        }
    }

    protected function updateNestedPhones($company_id, UpdateRequest $request)
    {
        $phones = $request->getValidatedEntity()->get('phones', []);
        if ($phones === null || count($phones) === 0) {
            return;
        }
        try {
            /** @var \App\Resources\Company\PhoneResource $phone_resource */
            $phone_resource = $request->resource()->relationResource('phones');
            $ids = $phone_resource->batchUpdateOrCreate(Collection::fromArray($phones))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($company_id) {
                    $entity->set('company_id', $company_id);
                    return $entity;
                })
                ->attach('request', function ($request) {
                    // since update or create requests are proxies you have to apply your request specific changes via
                    // another hook
                    $request->attach('request', function ($request) {
                        $request->attach('validation_field_config', function (FieldConfig $config) {
                            $config->store('check_primary', false);
                            return $config;
                        });
                        return $request;
                    });
                    return $request;
                })
                ->run();
            $phone_resource->deleteMissingPhonesByCompanyID($company_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update phones - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'phones' => $errors
                ]);
            }
            throw $exception;
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        $company_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());

        $this->updateNestedPhones($company_id, $request);

        /** Extract the Wisetack pricing plan before running saveSettings()
        *   We handle the pricing plan separately to ensure atomic updates with the Wisetack API.
        *   This prevents partial updates if the external API call fails.
        */
        $settings = $request->getValidatedEntity()->get('settings', []);
        $pricing_plan = $settings['wisetack_current_pricing_plan'] ?? null;
        if ($pricing_plan !== null) {
            unset($settings['wisetack_current_pricing_plan']);
            $request->getValidatedEntity()->set('settings', $settings);
        }

        $this->saveSettings($company_id, $request);
        $this->updateProjectTypes($company_id, $request);
        $this->updateResultTypes($company_id, $request);

        if ($request->storage('dormant', false)) {
            GoogleDisconnectJob::enqueue($company_id);
        }

        /** @var UserResource $user_resource */
        $user_resource = $request->resource()->relationResource('users');
        $user_resource->pushUserInfoByCompanyID($request->getModel()->getKey());

        if ($request->storage('marketing_list_push', true)) {
            MarketingListPushJob::enqueue($company_id);
        }
        SupportListPushJob::enqueue($company_id);

        // Update the Wisetack pricing plan last, ensuring transactional integrity with the API
        if ($pricing_plan !== null) {
            $wisetack = new WisetackService(null);
            $wisetack->updateMerchantPricingPlan($company_id, $pricing_plan);
        }
    }



    public function close(Request $request)
    {
        $this->recordCompletedTrainingActions($request);
    }

    public function queryScopeGlobal($query, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user !== null) {
            $query->ofCompany($user->companyID);
        }
        $query->leftJoin('companyPhones', function ($join) {
            $join->on('companyPhones.companyID', '=', 'companies.companyID')
                ->where('companyPhones.isPrimary', 1);
        });
        return $query;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'account-v1':
                $scope->fields(['status', 'trial_expires_at'])->with([
                    'credits',
                    'current_subscription',
                    'default_payment_method' => [
                        'fields' => ['item_type'],
                            'with' => [
                                'item' => [
                                    'poly_scopes' => [
                                        PaymentMethodResource::TYPE_ACH => [
                                            'fields' => ['account_number']
                                        ],
                                        PaymentMethodResource::TYPE_CREDIT_CARD => [
                                            'fields' => ['number']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]);
                break;
            case 'setup-v1':
                $scope->fields(['id', 'name'], true);
                $scope->with([
                    'intake' => [
                        'fields' => ['id', 'industry_id', 'marketing_source_id', 'user_count_id'],
                            'with' => [
                                'features'
                            ]
                        ]
                    ]);
                break;
        }
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    public function actionAllowed($action, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null || $user->admin) {
            return true;
        }
        if ($action === CompanyResource::ACTION_CREATE || $action === CompanyResource::ACTION_GET_COLLECTION) {
            return false;
        }
        return $user->primary;
    }

    public function modelIsMutable(Company $model, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null || $user->admin || ($user->primary && $user->companyID === $model->companyID)) {
            return true;
        }
        return false;
    }

    public function modelIsDeletable(Company $model, CompanyResource $resource)
    {
        $user = $resource->acl()->user();
        if (!$user->admin) {
            return false;
        }
        return true;
    }
}
