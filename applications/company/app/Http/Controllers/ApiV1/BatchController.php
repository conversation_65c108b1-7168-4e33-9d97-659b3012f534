<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1;

use App\Classes\ApiLogger;
use App\Http\Responses\BatchApiResponse;
use App\Resources;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Resource\Http\Requests\ResourceBatchRequest;

/**
 * Class BatchController
 *
 * @package App\Http\Controllers\ApiV1
 */
class BatchController
{
    /**
     * Handle batch request
     *
     * @param ResourceBatchRequest $request
     * @param ApiLogger $logger
     * @return JSONResponse
     * @throws \Throwable
     */
    public function handle(ResourceBatchRequest $request, ApiLogger $logger): JSONResponse
    {
        $available_resources = [
            'app-notification-distribution' => [
                'resource' => Resources\AppNotificationDistributionResource::class,
                'actions' => ['partial-update']
            ],
            'customer' => [
                'resource' => Resources\CustomerResource::class,
                'actions' => ['delete']
            ],
            'bid-item' => [
                'resource' => Resources\Bid\ItemResource::class,
                'actions' => ['partial-update']
            ],
            'bid-item-section' => [
                'resource' => Resources\Bid\Item\SectionResource::class
            ],
            'bid-item-section-form' => [
                'resource' => Resources\Bid\Item\Section\FormResource::class
            ],
            'bid-item-content' => [
                'resource' => Resources\Bid\Item\ContentResource::class,
                'actions' => ['create', 'update', 'update-or-create', 'partial-update', 'delete']
            ],
            'bid-item-custom-drawing' => [
                'resource' => Resources\Bid\Item\CustomDrawingResource::class,
                'actions' => ['delete']
            ],
            'bid-item-drawing' => [
                'resource' => Resources\Bid\Item\DrawingResource::class,
                'actions' => ['sync']
            ],
            'bid-item-line-item' => [
                'resource' => Resources\Bid\Item\LineItemResource::class
            ],
            'bid-item-media' => [
                'resource' => Resources\Bid\Item\MediaResource::class,
                'actions' => ['poly-create', 'partial-update', 'delete']
            ],
            'bid-item-payment-term' => [
                'resource' => Resources\Bid\Item\PaymentTermResource::class
            ],
            'form-item-entry-group' => [
                'resource' => Resources\Form\Item\Entry\GroupResource::class
            ],
            'form-item-entry-group-field-file' => [
                'resource' => Resources\Form\Item\Entry\Group\FieldFileResource::class,
                'actions' => ['sync', 'delete']
            ],
            'form-item-entry-group-field-option' => [
                'resource' => Resources\Form\Item\Entry\Group\FieldOptionResource::class,
                'actions' => ['sync']
            ],
            'form-item-entry-group-field-product' => [
                'resource' => Resources\Form\Item\Entry\Group\FieldProductResource::class,
                'actions' => ['sync']
            ],
            'form-item-entry-group-field-value' => [
                'resource' => Resources\Form\Item\Entry\Group\FieldValueResource::class,
                'actions' => ['save']
            ],
            'project' => [
                'resource' => Resources\ProjectResource::class,
                'actions' => ['partial-update']
            ],
            'product-item' => [
                'resource' => Resources\Product\ItemResource::class,
                'actions' => ['partial-update']
            ]
        ];

        // register available resources in batch and their associated type names
        foreach ($available_resources as $type => $config) {
            $request->resource($type, $config['resource'], $config['actions'] ?? null);
        }

        $batch_api_response = new BatchApiResponse($request, $logger);

        return $batch_api_response->response();
    }
}
