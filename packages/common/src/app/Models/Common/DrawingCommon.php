<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\DrawingNode;
use Common\Models\Project;
use Common\Models\File;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait DrawingCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'version' => 'int',
        'subversion' => 'int',
        'projectID' => 'int',
        'status' => 'int',
        'zoomLevel' => 'float',
        'gridUnit' => 'int',
        'showWallLengths' => 'bool',
        'config' => 'array',
        'isRepairPlanValid' => 'bool',
        'isLocked' => 'bool',
        'finalizedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['startedAt', 'lastModifiedAt', 'lockedAt', 'finalizedAt'];
    protected $searchColumns = ['name'];

    public static function getStatuses()
    {
        return [
            static::STATUS_IN_PROGRESS => 'In Progress',
            static::STATUS_FINALIZED => 'Finalized'
        ];
    }

    public static function getGridUnits()
    {
        return [
            static::GRID_UNIT_FEET => 'Feet'
        ];
    }

    public function createdByUser()
    {
        return $this->belongsTo(User::class, 'createdByUserID', 'userID');
    }

    public function image()
    {
        return $this->belongsTo(File::class, 'imageFileID', 'fileID');
    }

    public function nodes()
    {
        return $this->hasMany(DrawingNode::class, 'drawingID');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'projectID', 'projectID');
    }

    public function repairPlan()
    {
        return $this->belongsTo(File::class, 'repairPlanFileID', 'fileID');
    }

    public function scopeWithProject($query)
    {
        return $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
    }

    public function scopeWithProperty($query)
    {
        return $this->scopeWithProject($query)
            ->join('property', 'property.propertyID', '=', 'project.propertyID');
    }

    public function scopeWithCustomer($query)
    {
        return $this->scopeWithProperty($query)
            ->join('customer', 'customer.customerID', '=', 'property.customerID');
    }

    public function scopeWithCreatedByUser($query)
    {
        return $query->join('user', 'user.userID', '=', "{$this->table}.createdByUserID");
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }

        return $this->scopeWithCreatedByUser($query)->where('user.companyID', $company);
    }
}
