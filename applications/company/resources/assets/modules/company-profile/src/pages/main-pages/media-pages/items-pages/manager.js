'use strict';

const filesize = require("filesize");
const moment = require("moment-timezone");
const Uppy = require("@uppy/core");
const XHRUpload = require("@uppy/xhr-upload");
const Dashboard = require("@uppy/dashboard");
const Webcam = require("@uppy/webcam");

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const DeleteMediaModal = require('../../../../modals/media/delete');

const media_tpl = require('@cam-company-profile-tpl/pages/main-pages/media-pages/items-pages/manager.hbs');

const { file_types, file_types_name, mime_types, MAX_UPLOAD_SIZE_200MB } = require('./constants');

class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            modals: {},
            table: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            },
            table_loaded: false,
        });

        this.state.modals = {
            media_delete: new DeleteMediaModal(this)
        }
    };

    /**
     * Get display type icon and text for table row display
     *
     * @param {string} mime_type
     */
    getDisplayType(mime_type) {
        const this_type = mime_types[mime_type] || 10; // '10' is empty.

        const key = file_types[this_type];
        const name = file_types_name[this_type];

        return `<div class="c-cmlt-image-type"><span class="media-icon icon-file-${key}"></span> ${name}</div>`;
    };

    /**
     * Fetch public link for media
     *
     * @param {string} id - UUID
     * @returns {Promise<void>}
     */
    async fetchPublicLink(id) {
        try {
            let data = await Api.Resources.Media().method(Api.Request.Method.GET)
                .custom(`${id}/generate-public-url`);
            navigator.clipboard.writeText(data.media_url).then(() => {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Copied media Link'));
            });
        } catch (e) {
            let message = createErrorMessage('Unable to fetch media info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    /**
     * Create the media datatable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('media.items.update', {
                    media_id: data.id
                });
            }).on('scope_change', (scope) => {
                this.state.table_scope = scope;
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Media'
        });

        this.state.table.setFilterOptions({
            content_type: {
                label: 'Type',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Audio (m4a)',
                        value:  'audio/m4a'
                    },
                    2: {
                        label: 'Audio (mpeg3)',
                        value: 'audio/mpeg3'
                    },
                    3: {
                        label: 'CSV',
                        value: 'text/csv'
                    },
                    4: {
                        label: 'Image (gif)',
                        value: 'image/gif'
                    },
                    5: {
                        label: 'Image (jpg)',
                        value: 'image/jpeg'
                    },
                    6: {
                        label: 'Image (png)',
                        value: 'image/png'
                    },
                    7: {
                        label: 'MS Excel',
                        value: 'application/vnd.ms-excel'
                    },
                    8: {
                        label: 'MS Powerpoint',
                        value: 'application/vnd.ms-powerpoint'
                    },
                    9: {
                        label: 'MS Word',
                        value: 'application/msword'
                    },
                    10: {
                        label: 'PDF',
                        value: 'application/pdf'
                    },
                    11: {
                        label: 'Plain Text',
                        value: 'text/plain'
                    },
                    12: {
                        label: 'Video (m4v)',
                        value: 'video/m4v'
                    },
                    13: {
                        label: 'Video (mp4)',
                        value: 'video/mp4'
                    },
                    14: {
                        label: 'Video (mpeg)',
                        value: 'video/mpeg'
                    },
                    15: {
                        label: 'Video (webm)',
                        value: 'video/webm'
                    }
                }
            },
            is_bid_media: {
                label: 'Bid Media',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            }
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        let checked = '<svg class="t-icon-checked"><use xlink:href="#remix-icon--system--checkbox-circle-line"></use></svg>',
            unchecked = '';

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name'
            },
            size: {
                label: 'Size',
                value: (data) => {
                    return filesize(data.size);
                }
            },
            type: {
                label: 'File Type',
                value: (data) => {
                    return this.getDisplayType(data.file.content_type);
                },
            },
            bid_media: {
                label: 'Bid Media',
                orderable: false,
                value: (data) => {
                    return data.is_bid_media ? checked : unchecked;
                }
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            }
        });

        // set row action config
        this.state.table.setRowActions({
            preview_media: {
                label: 'Preview',
                link: {
                    href: data => data.file_media_urls.original,
                    target: '_blank'
                }
            },
            edit: {
                label: 'Edit',
                action: (data) => {
                    this.router.navigate('media.items.update', {
                        media_id: data.id
                    });
                }
            },
            link: {
                label: 'Copy Public Link',
                action: (data) => {
                    this.fetchPublicLink(data.id);
                },
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: (data) => {
                    this.openModal({
                        media_id: data.id,
                        filename: data.name
                    })
                    .then(() => {
                        this.state.table.draw() })
                    .finally(() => {
                        this.state.modals.media_delete.close();
                    });
                }
            }
        });

        this.configureUppy();

        // set buttons config
        this.state.table.setButtons({
            add_media: {
                label: 'New Media',
                action: () => {
                    this.state.uppy_dashboard.openModal();
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.Media, (request) => {
            request.accept('application/vnd.adg.fx.collection-v1+json');
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Configure 'Uppy' upload library
     *
     * @param {boolean} [show=true]
     */
    configureUppy() {
        let max_files = 25;
        this.state.uppy = Uppy({
            id: 'media',
            autoProceed: false,
            restrictions: {
                allowedFileTypes: Object.keys(mime_types),
                maxNumberOfFiles: max_files,
                maxFileSize: MAX_UPLOAD_SIZE_200MB
            },
            onBeforeUpload: (files) => {
                let uploaded_files = Object.assign({}, files)
                Object.keys(uploaded_files).forEach(file_id => {
                    uploaded_files[file_id].meta['is_bid_media'] = true;
                });
                return uploaded_files;
            }
        })
            .use(XHRUpload, {
                endpoint: Api.Resources.Media().buildUrl(),
                method: 'POST',
                headers: {
                    Accept: 'application/vnd.adg.fx.collection-v1+json'
                },
                fieldName: 'file',
                getResponseData: () => ({}),
                getResponseError: (responseText, response) => {
                    if (response.status === 401) {
                        window.location.href = window.fx_pages.AUTH_LOGIN;
                        return;
                    }
                    let error = JSON.parse(responseText);
                    if (response.status === 422) {
                        let text = [];
                        for (let key of Object.keys(error.errors)) {
                            text.push(error.errors[key]);
                        }
                        return new Error(text.join(', '));
                    }
                    return new Error('Upload failed for unknown reason, please contact support');
                }
            })
            .use(Dashboard, {
                metaFields: [
                    {id: 'name', name: 'Name', placeholder: 'File name'}
                ],
                inline: false,
                locale: {
                    strings: {
                        editing: 'Now Editing %{file}',
                        dashboardWindowTitle: 'Uppy Dashboard Window (Press escape to close)',
                        dashboardTitle: 'Uppy Dashboard',
                    },
                },
                closeModalOnClickOutside: false,
                hideProgressAfterFinish: true,
                proudlyDisplayPoweredByUppy: false,
                note: `1-${max_files} files allowed per upload, up to 200MB each`,
                onRequestCloseModal: () => {
                    this.state.uppy_dashboard.closeModal();
                    let files = Object.assign({}, this.state.uppy.getState().files);
                    if (Object.keys(files).length === 0) {
                        this.state.uppy.reset();
                    }
                }
            })
            .use(Webcam, {
                    target: Dashboard,
                    showVideoSourceDropdown: true,
                    showRecordingLength: true,
                });

        this.state.uppy_dashboard = this.state.uppy.getPlugin('Dashboard');

        this.state.uppy
            .on('upload-success', () => {
                this.state.table.draw();
            })
            .on('complete', () => {
                setTimeout(() => {
                    let close_modal = true;
                    let files = this.state.uppy.getState().files;
                    for (let id in files) {
                        let file = files[id];
                        let is_processing = file.progress.preprocess || file.progress.postprocess;
                        if (file.progress.uploadComplete && !is_processing && !file.error) {
                            this.state.uppy.removeFile(file.id);
                            continue;
                        }
                        close_modal = false;
                    }
                    if (this.state.uppy_dashboard.isModalOpen() && close_modal) {
                        this.state.uppy_dashboard.closeModal();
                    }
                }, 1000);
            });
    }


    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        this.state.table.draw();
    };

    /**
     * Open delete media modal with promise
     *
     * @param {string} media_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal({media_id, filename}) {
        return new Promise((resolve, reject) => {
            return this.state.modals.media_delete.open({
                media_id,
                filename,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // We have to force the filter to hide if it's still open when they unload
        this.state.table.hideFilterMenu();

        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return media_tpl();
    };
}

module.exports = Manager;