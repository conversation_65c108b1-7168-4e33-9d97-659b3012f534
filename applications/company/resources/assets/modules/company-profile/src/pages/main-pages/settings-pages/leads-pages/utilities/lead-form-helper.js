'use strict';

const Api = require('@ca-package/api');
const debounce = require('@cac-js/utils/debounce');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");
const {FIELD_NAMES, TYPES} = require("./constants");
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/checkbox'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const InputMask = require('inputmask');
const autosize = require("autosize");
class LeadFormHelper {
    constructor(root, refreshFormCallback) {
        this.root = root;
        this.refreshForm = refreshFormCallback;
        this.inputs = [];
    }

    setupAllListeners() {
        this.setupRadioFieldToggles();
        this.setupFieldsToggles();
        this.setupLabelOptions();
    }

    setupRadioFieldToggles() {
        const fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE];
        fields.forEach((field) => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const dropdown_radio = findChild(this.root, jsSelector(`${field}_dropdown`));
            const freeform_radio = findChild(this.root, jsSelector(`${field}_freeform`));

            const toggleRadios = () => {
                const is_available = visibility_elem.is(':checked');
                dropdown_radio.prop('disabled', !is_available);
                freeform_radio.prop('disabled', !is_available);
                this.refreshForm();
            };

            onEvent(visibility_elem, 'change', toggleRadios);
            onEvent(dropdown_radio, 'change', this.refreshForm);
            onEvent(freeform_radio, 'change', this.refreshForm);

            toggleRadios();
        });
    }

    setupFieldsToggles() {
        Object.values(FIELD_NAMES).forEach((field) => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const requirement_elem = findChild(this.root, jsSelector(`${field}_requirement`));
            const options_title = findChild(this.root, jsSelector(`${field}_options_title`));
            const options_container_elem = findChild(this.root, jsSelector(`${field}_options_container`));

            if (visibility_elem.length) {
                this.inputs[field] = FormInput.init(visibility_elem);
            }

            onEvent(requirement_elem, 'change', () => {
                if (!visibility_elem.is(':checked')) {
                    visibility_elem.prop('checked', true).trigger('change');
                }
                this.refreshForm();
            });

            onEvent(visibility_elem, 'change.fx', () => {
                if (!this.inputs[field].checked) {
                    requirement_elem.prop('checked', false);
                    options_container_elem.addClass('t-hidden')
                    options_title.addClass('inactive')
                }
                else {
                    options_container_elem.removeClass('t-hidden')
                    options_title.removeClass('inactive')
                }

                // hide everything below.
                this.refreshForm();
            });
        });
    }

    setupLabelOptions() {
        const fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE, FIELD_NAMES.CUSTOMER_NOTES];
        fields.forEach(field => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const label_elem = findChild(this.root, jsSelector(`${field}_label`));

            label_elem.prop('disabled', !visibility_elem.is(':checked'));

            onEvent(visibility_elem, 'change', () => {
                label_elem.prop('disabled', !visibility_elem.is(':checked'));
                this.refreshForm();
            });
        });

        const label_fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE, FIELD_NAMES.CUSTOMER_NOTES, 'title', 'save_button'];

        label_fields.forEach(field => {
            const label_elem = findChild(this.root, jsSelector(`${field}_label`));
            label_elem.fxEvent('keyup', debounce(this.refreshForm, 300));
        });
    }

    validateFields() {
        const fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE, FIELD_NAMES.CUSTOMER_NOTES];
        let has_error = false;

        fields.forEach((field) => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const requirement_elem = findChild(this.root, jsSelector(`${field}_requirement`));
            const label_elem = findChild(this.root, jsSelector(`${field}_label`));

            if (visibility_elem.is(':checked') && requirement_elem.is(':checked') && !label_elem.val()) {
                label_elem.addClass('error');
                has_error = true;
            } else {
                label_elem.removeClass('error');
            }
        });

        return !has_error;
    }

    buildEntity(leads_form_id, validator, lead_form_status, lead_form_settings) {
        if (!lead_form_status.is(':checked')) {
            return { is_active: false, fields: {} };
        }

        const entity = {
            id: leads_form_id,
            title: validator.getInputElem('title_label').val(),
            save_button_label: validator.getInputElem('save_button_label').val(),
            default_assigned_to_user_id: parseInt(validator.getInputElem('default_assigned_to').val()),
            is_active: lead_form_status.is(':checked'),
            fields: {},
        };

        if (lead_form_settings.is(':visible')) {
            Object.values(FIELD_NAMES).forEach((field) => {
                const visibility = findChild(this.root, jsSelector(`${field}_visibility`)).is(':checked');
                const requirement = findChild(this.root, jsSelector(`${field}_requirement`)).is(':checked');
                const label = findChild(this.root, jsSelector(`${field}_label`)).val();

                entity.fields[field] = {
                    is_enabled: visibility,
                    is_required: requirement,
                };

                if (label) {
                    entity.fields[field].label = label;
                }

                if (field === FIELD_NAMES.MARKETING_SOURCE || field === FIELD_NAMES.PROJECT_TYPE) {
                    const is_dropdown = findChild(this.root, jsSelector(`${field}_dropdown`)).is(':checked');
                    entity.fields[field].field_type = is_dropdown ? TYPES.DROPDOWN : TYPES.FREEFORM;
                }
            });
        }

        return entity;
    }

    async fetchLeadFormSettings() {
        return await Api.Resources.LeadFormsAction()
            .method(Api.Request.Method.GET)
            .accept('application/vnd.adg.fx.form-v1+json')
            .custom('current');

    }

    /**
     * Get users list
     *
     * @returns {Promise<array>}
     */
    async getUsers() {
        let {entities: users} = await Api.Resources.Users()
            .fields(['id', 'first_name', 'last_name'])
            .filter('is_active', true)
            .filter('is_user_invited', false)
            .all();
        return users.map(user => user.data);
    };

    setupPhoneMask() {
        const phone_input = findChild(this.root, jsSelector('phone'));
        if (phone_input) {
            InputMask({
                "mask": "(*************"
            }).mask(phone_input);
        }
    }

    setupTextareaResize() {
        const textarea = findChild(this.root, jsSelector('customer_notes'));

        if (textarea) {
            autosize(textarea);

            textarea.fxEvent('change', function () {
                autosize.update(textarea);
            });
        }
    }

    setupSelectInputs() {
        const selectInputs = [FIELD_NAMES.STATE, FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE];

        selectInputs.forEach(inputName => {
            const inputElement = findChild(this.root, jsSelector(inputName));

            if (inputElement) {
                initSelectPlaceholder(inputElement);
            }
        });
    }

    initializeFormComponents() {
        this.setupSelectInputs();
        this.setupPhoneMask();
        this.setupTextareaResize();
    }

    async setupAssignedToField(active_users) {
        const default_assigned_to_input = findChild(this.root, jsSelector('default_assigned_to'));
        default_assigned_to_input.prop('disabled', true);

        let default_assigned_to = FormInput.init(default_assigned_to_input, {
            data_provider: () => {
                let users = [];
                for (let user of active_users) {
                    users.push({
                        id: user.id,
                        text: `${user.first_name} ${user.last_name}`
                    });
                }
                return users;
            },
            placeholder: '-- Select One --',
            closeOnSelect: true,
        });
        await default_assigned_to.promise;
        default_assigned_to_input.prop('disabled', false);
    }

    setupSubmitButtonCompanyColors(company_colors) {
        const submit_button = findChild(this.root, jsSelector('save_button'));
        if (!submit_button || !company_colors || !company_colors.button_background_color ||
            !company_colors.button_text_color || !company_colors.hover_background_color || !company_colors.hover_text_color) {
            return;
        }

        submit_button.css({
            '--button-background-color': company_colors.button_background_color,
            '--button-border-color': company_colors.button_background_color,
            '--button-text-color': company_colors.button_text_color,

            '--button-hover-background-color': company_colors.hover_background_color,
            '--button-hover-border-color': company_colors.hover_background_color,
            '--button-hover-text-color': company_colors.hover_text_color
        });
    }
}

module.exports = LeadFormHelper;