'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Scale extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Scale.Type.SCALE);
        Object.assign(this.state, {
            label: 'Scale',
            icon: 'module--drawing--tools--scale',
            node_type: Node.Entity.Type.SCALE
        });
    };
}

module.exports = Scale;
