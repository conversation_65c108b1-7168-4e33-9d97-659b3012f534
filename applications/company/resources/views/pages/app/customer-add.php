<?php

use Core\StaticAccessors\Config;

$this->extend('layout.app');

$asset = $this->asset;

// select2
$asset->style('select2/select2');
$asset->script('select2/select2.full');
$asset->script('select2/select2.multi-checkboxes');

// moment
$asset->script('moment.min');

// fullcalendar
$asset->style('fullcalendar/fullcalendar.min');
$asset->style('fullcalendar/scheduler.min');
$asset->script('fullcalendar/fullcalendar.min');
$asset->script('fullcalendar/scheduler.min');

// wicked picker
$asset->style('wickedpicker');
$asset->script('wickedpicker');

// jquery ui
$asset->style('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css');
$asset->script('//ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js');

// jquery Touch Punch
$asset->script('jquery.ui.touch-punch.min');

// mask
$asset->script('jquery.mask.min');

// autosize
$asset->script('autosize.min');

// custom
$asset->style('custom');
$asset->style('resource-select');
$asset->style('customer-add');
$asset->style('custom-calendar');
$asset->script('cookie');
$asset->script('map');
$asset->script('calendar-filter');
$asset->scriptData('customer_add_info', [
    'repeat_business_marketing_type' => $repeatBusinessMarketingTypeID,
    'add_phone_row' => $addPhoneRow,
    'todays_date_default' => $todaysDateDefault,
    'priorities' => $priorities,
    'project_types' => $projectTypes,
    'project_types_legacy' => $projectTypesLegacy,
    'business_hours' => $formatted_business_hours
]);
$asset->scriptData('fx_pages', [
    'CUSTOMERS' => $this->uri->routeRaw('page.app.customers')->build(),
    'CUSTOMER_MANAGEMENT' => $this->uri->routeRaw('page.app.customer-management')->build() . '?cid={customer_id}',
    'LEAD' => $this->uri->routeRaw('page.app.leads', ['path' => '/details'])->build() . '/{lead_id}'
]);
$asset->script('customer-add');

// google maps
$google_api = 'https://maps.googleapis.com/maps/api/js?key='.Config::get('google.map.api_key');
$asset->script($google_api);

$icons = [
    'info' => $this->asset->uri('image', 'icons/info.png')
];

?>
<div id="companyLatitude" class="is-hidden"><?=$companyLatitude?></div>
<div id="companyLongitude" class="is-hidden"><?=$companyLongitude?></div>
<input id="fullUserColor" type="hidden" name="fullUserColor" value="0" />
<div id="loading-image" class="loadingImage" style="display: block">
    <img src="<?=$this->asset->uri('image', 'ajax-loader.gif')?>" />
</div>
<div class="tiny reveal" id="noResourcesModal" data-reveal data-close-on-esc="false" data-close-on-click="false">
    <div class="row">
        <h3>No Salesmen or Installers</h3>
    </div>
    <p>Add salesmen or installers to schedule an appointment.</p>
    <button class="button" id="closeNoResourcesModal">Ok</button>
</div>
<?=$buttonDisplay?>
<form class="reportForm" method="post" novalidate style="margin-bottom:3rem;" onsubmit="return false">
    <input type="hidden" id="latitude" name="latitude" value="<?=$latitude?>" />
    <input type="hidden" id="longitude" name="longitude" value="<?=$longitude?>" />
    <input type="hidden" id="customerID" name="customerID" value="<?=$customerID?>" />
    <input type="hidden" id="propertyID" name="propertyID" value="<?=$propertyID?>" />
    <input type="hidden" id="leadUUID" name="leadUUID" value="<?=$leadUUID?>" />
    <input type="hidden" id="leadID" name="leadID" value="<?=$leadID?>" />

    <input type="hidden" name="existingFirstName" value="<?=$firstName?>" />
    <input type="hidden" name="existingLastName" value="<?=$lastName?>" />
    <input type="hidden" name="existingEmail" value="<?=$email?>" />
    <input type="hidden" name="existingBusinessName" value="<?=$businessName?>" />

    <input type="hidden" name="existingAddress" value="<?=$address?>" />
    <input type="hidden" name="existingAddress2" value="<?=$address2?>" />
    <input type="hidden" name="existingCity" value="<?=$city?>" />
    <input type="hidden" name="existingState" value="<?=$state?>" />
    <input type="hidden" name="existingZip" value="<?=$zip?>" />
    <input type="hidden" id="marketingTypeID" name="marketingTypeID" value="<?=$marketing_type_id?>" />
    <input type="hidden" id="projectTypeID" name="projectTypeID" value="<?=$project_type_id?>" />
    <input type="hidden" id="priority" name="priority" value="<?=$priority?>" />
    <input type="hidden" id="assignedToUserID" name="assignedToUserID" value="<?=$assigned_to_user_id?>" />

    <div class="row newCustomerDisplay">
        <div class="medium-12 columns">
            <div class="row">
                <div class="medium-9 small-7 large-10 columns">
                    <h1 class="project-title" style="margin: 0;"><?=$pageTitle?></h1>
                </div>
                <div class="medium-3 small-5 large-2 columns" style="text-align: right;">
                    <button data-calendar-open class="button no-margin">Schedule</button>
                </div>
            </div>
        </div>
    </div>
    <div class="row newCustomerDisplay">
        <div class="medium-12 columns">
            <hr class="section-divider">
            <p class="section-title">Customer Information</p>

            <div class="row">
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label t-optional">
                            Business Name
                            <span class="f-fl-optional">(Optional)</span>
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProperty?><?=$disabledProject?> name="businessName" type="text" value="<?=$businessName?>"/>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Customer First Name
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProperty?><?=$disabledProject?> name="firstName" type="text" value="<?=$firstName?>" required  />
                    </div>
                </div>
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Customer Last Name
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProperty?><?=$disabledProject?> name="lastName" type="text" value="<?=$lastName?>" required  />
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-8 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project Address
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProject?> name="address" id="address" type="text" value="<?=$address?>" required  />
                    </div>
                </div>
                <div class="medium-4 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Address 2 <small>(Apt, Suite, Unit)</small>
                        </label>
                        <input class="f-f-input" <?=$disabledProject?> name="address2" id="address2" type="text" value="<?=$address2?>" />
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project City
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProject?> name="city" id="city" type="text" value="<?=$city?>" required  />
                    </div>
                </div>
                <div class="medium-3 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project State/Province
                        </label>
                        <select class="f-f-input duplicateSearch" <?=$disabledProject?> required name="state" id="state">
                            <option value="">--</option>
                            <optgroup label="States">
                                <?php foreach ($lists['states'] as $state_abbr => $state_label): ?>
                                    <option value="<?=$state_abbr?>"<?=($state_abbr === $state ? ' selected="selected"' : '')?>><?=$state_label?></option>
                                <?php endforeach; ?>
                            </optgroup>
                            <optgroup label="US Territories">
                                <?php foreach ($lists['us_territories'] as $territory_abbr => $territory_label): ?>
                                    <option value="<?=$territory_abbr?>"<?=($territory_abbr === $state ? ' selected="selected"' : '')?>><?=$territory_label?></option>
                                <?php endforeach; ?>
                            </optgroup>
                            <optgroup label="Provinces">
                                <?php foreach ($lists['provinces'] as $province_abbr => $province_label): ?>
                                    <option value="<?=$province_abbr?>"<?=($province_abbr === $state ? ' selected="selected"' : '')?>><?=$province_label?></option>
                                <?php endforeach; ?>
                            </optgroup>
                        </select>
                    </div>
                </div>
                <div class="medium-3 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project Postal Code
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProject?> name="zip" id="zip" type="text" value="<?=$zip?>" required />
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project County
                        </label>
                        <input class="f-f-input" <?=$disabledProject?> name="county" id="county" type="text" value="<?=$county?>" />
                    </div>
                </div>
                <div class="medium-6 small-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Project Township
                        </label>
                        <input class="f-f-input" <?=$disabledProject?> name="township" id="township" type="text" value="<?=$township?>" />
                    </div>
                </div>
            </div>

            <div class="row accordion">
                <div class="medium-12 columns">
                    <ul class="accordion" data-accordion data-allow-all-closed="true" style="margin-left: 0; margin-bottom: 1rem;">
                        <li class="accordion-item" data-accordion-item>
                            <a class="accordion-title">Is the billing address different? <small>Click to Add</small></a>
                            <div class="accordion-content" data-tab-content style="background:none;">
                                <div class="row">
                                    <div class="medium-8 small-12 columns">
                                        <div class="f-field">
                                            <label class="f-f-label">
                                                Billing Address
                                            </label>
                                            <input class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="ownerAddress" id="ownerAddress" type="text" value="<?=$ownerAddress?>" />
                                        </div>
                                    </div>
                                    <div class="medium-4 small-12 columns">
                                        <div class="f-field">
                                            <label class="f-f-label">
                                                Billing Address 2 <small>(Apt, Suite, Unit)</small>
                                            </label>
                                            <input class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="ownerAddress2" id="ownerAddress2" type="text" value="<?=$ownerAddress2?>" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="medium-6 small-12 columns">
                                        <div class="f-field">
                                            <label class="f-f-label">
                                                Billing City
                                            </label>
                                            <input class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="ownerCity" id="ownerCity" type="text" value="<?=$ownerCity?>" />
                                        </div>
                                    </div>
                                    <div class="medium-3 small-12 columns">
                                        <div class="f-field">
                                            <label class="f-f-label">
                                                Billing State/Province
                                            </label>
                                            <select class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="ownerState" id="ownerState" style="margin:0 0 0 0 !important;">
                                                <option value="">--</option>
                                                <optgroup label="States">
                                                    <?php foreach ($lists['states'] as $state_abbr => $state_label): ?>
                                                        <option value="<?=$state_abbr?>"<?=($state_abbr === $ownerState ? ' selected="selected"' : '')?>><?=$state_label?></option>
                                                    <?php endforeach; ?>
                                                </optgroup>
                                                <optgroup label="US Territories">
                                                    <?php foreach ($lists['us_territories'] as $territory_abbr => $territory_label): ?>
                                                        <option value="<?=$territory_abbr?>"<?=($territory_abbr === $ownerState ? ' selected="selected"' : '')?>><?=$territory_label?></option>
                                                    <?php endforeach; ?>
                                                </optgroup>
                                                <optgroup label="Provinces">
                                                    <?php foreach ($lists['provinces'] as $province_abbr => $province_label): ?>
                                                        <option value="<?=$province_abbr?>"<?=($province_abbr === $ownerState ? ' selected="selected"' : '')?>><?=$province_label?></option>
                                                    <?php endforeach; ?>
                                                </optgroup>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="medium-3 small-12 columns">
                                        <div class="f-field">
                                            <label class="f-f-label">
                                                Billing Postal Code
                                            </label>
                                            <input class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="ownerZip" id="ownerZip" type="text" value="<?=$ownerZip?>" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="row">
                <div class="medium-6 small-9 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Customer Email
                        </label>
                        <input class="f-f-input duplicateSearch" <?=$disabledProperty?><?=$disabledProject?> name="email" type="text" value="<?=$email?>" required  />
                        <small class="error-match form-error validEmail">
                            Email does not appear to be valid.
                            <a data-dismiss-email>[Dismiss]</a>
                        </small>
                    </div>
                </div>
                <div class="medium-2 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            No Email
                        </label>
                        <input class="f-f-input" <?=$disabledProperty?><?=$disabledProject?> name="noEmailRequired" type="checkbox" value="1" style="margin-left: 1.1rem; margin-top: 0.7rem">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-9 columns" id="phoneSection">
                    <table id="phoneTable" class="phoneTable" cellpadding="0" cellspacing="0" style="margin: 1rem 0;">
                        <thead style="background:none;">
                        <tr>
                            <th width="40%" style="text-align:left;color:#ffffff;line-height: .5rem;border-bottom:0;padding:0;">
                                <div class="f-field">
                                    <label class="f-f-label">Customer Phone</label>
                                </div>
                            </th>
                            <th width="40%" style="text-align:center;color:#ffffff;line-height:.5rem;border-bottom:0;padding:0;"></th>
                            <th width="10%" style="text-align:center;color:#000000;line-height:.5rem;border-bottom:0;padding:0;">
                                <div class="f-field">
                                    <label class="f-f-label">Primary</label>
                                </div>
                            </th>
                            <th width="10%" style="text-align:right;color:#000000;line-height:.25rem;border-bottom:0;padding:0;">
                                <a class="addPhone"><img src="<?=$this->asset->uri('image', 'icons/phone_add.png')?>" /></a>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr style="display:none;">
                            <td class="description" style="color:#36413e;text-align:center;">
                                <div class="f-field">
                                    <select class="f-f-input" name="description[]" >
                                        <option></option>
                                        <option value="Cell">Cell (Text Opt-in)</option>
                                        <option value="CellNoText">Cell (Text Opt-out)</option>
                                        <option value="Home">Home</option>
                                        <option value="Work">Work</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </td>
                            <td class="number" style="color:#36413e;">
                                <div class="f-field">
                                    <input class="f-f-input no-margin phone duplicateSearch" name="phone[]" type="text" value="" >
                                </div>
                            </td>
                            <td class="primary" style="color:#36413e;text-align:center;">
                                <div class="f-field">
                                    <input class="f-f-input isPrimary no-margin" name="isPrimary[]" type="checkbox" value="1">
                                </div>
                            </td>
                            <td class="delete" style="color:#36413e;text-align:right;">
                                <a class="deletePhone"><img src="<?=$this->asset->uri('image', 'icons/phone_delete.png')?>" /></a>
                            </td>
                        </tr>
                        <?=$phoneDisplay?>
                        </tbody>
                    </table>
                    <small style="margin-left: .5rem;" class="form-error onePhone">At least one phone is required.</small>
                    <small style="margin-left: .5rem;" class="error-match fullPhone">Description and complete phone number are required.</small>
                    <small style="margin-left: .5rem;" class="form-error primary">Please mark one phone number as primary.</small>
                </div>
                <div class="medium-3 columns hide-for-small-only"></div>
            </div>

            <div class="row duplicateResult">
                <div class="medium-12 columns">
                    <div class="callout alert">
                        <p><small>Some of the information you have entered matches a customer we have on file. <a class="viewDuplicateResult">See Result</a></small></p>
                    </div>
                </div>
            </div>
            <hr class="section-divider">
            <p class="section-title">Additional Contacts</p>
            <div class="row">
                <div class="medium-8 columns">
                    <div class="f-field">
                        <label class="f-f-label" style="margin-bottom: 0.2rem;">Additional Project Contacts <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="6" title="Additional project contacts will be CC'ed on all emails that are sent for this project."><img src="<?=$this->asset->uri('image', 'icons/info.png')?>" /></span> </label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns text-center">
                    <table id="emailTable" class="emailTable" cellpadding="0" cellspacing="0">
                        <thead style="background:none;">
                        <tr>
                            <th width="30%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0 0.4rem 0.4rem 0;font-weight: normal;font-size: 0.875rem;">
                                <div class="f-field">
                                    <label class="f-f-label">Name</label>
                                </div>
                            </th>
                            <th width="30%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0.4rem 0 0.4rem 0.7rem;font-weight: normal;font-size: 0.875rem;">
                                <div class="f-field">
                                    <label class="f-f-label">Phone</label>
                                </div>
                            </th>
                            <th width="30%" style="text-align:left;color:#000000;line-height: .5rem;border-bottom:0;padding:0.4rem 0 0.4rem 0.7rem;font-weight: normal;font-size: 0.875rem;">
                                <div class="f-field">
                                    <label class="f-f-label">Email</label>
                                </div>
                            </th>
                            <th width="10%" style="text-align:center;color:#000000;line-height: .5rem;border-bottom:0;padding:0;">
                                <a class="addEmail"><img src="<?=$this->asset->uri('image', 'icons/phone_add.png')?>" /></a>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr style="display:none;">
                            <td class="name" style="color:#36413e;border-bottom:0;">
                                <div class="f-field">
                                    <input name="name" class="f-f-input" type="text" value="" maxlength="70"/>
                                </div>
                            </td>
                            <td style="color:#36413e;text-align:center;border-bottom:0;">
                                <div class="f-field">
                                    <input name="contactPhone" class="f-f-input phone" type="text" value="" sort="" maxlength="100"/>
                                </div>
                            </td>
                            <td class="email" style="color:#36413e;text-align:center;border-bottom:0;">
                                <div class="f-field">
                                    <input name="contactEmail" class="f-f-input" type="text" value="" sort="" maxlength="100"/>
                                    <small class="error-match form-error validEmail">
                                        Email does not appear to be valid.
                                        <a data-dismiss-contact-email>[Dismiss]</a>
                                    </small>
                                </div>
                            </td>
                            <td class="delete" style="color:#36413e;text-align:center;border-bottom:0;">
                                <a class="deleteEmail"><img src="<?=$this->asset->uri('image', 'icons/phone_delete.png')?>" /></a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="medium-4 columns text-center">
                            <small style="text-align: left;padding-left: 0.7rem;" class="error-match contactName">A name is required.</small>
                        </div>
                        <div class="medium-8 columns text-center">
                            <small style="text-align: left;padding-left: 1.4rem;" class="error-match contactEmail">A phone or valid email is required.</small>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="section-divider">
            <p class="section-title">Project Information</p>

            <div class="row">
                <div class="medium-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Name
                        </label>
                        <?=$projectNameDisplay?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-6 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Salesperson
                        </label>
                        <select id="projectSalesperson" name="projectSalesperson" class="isFloating">
                            <option value="">Select One</option>
                        </select>
                        <small class="form-warning">Warning: Project salesperson and appointment salesperson do not match.</small>
                    </div>
                </div>
                <div class="medium-5 columns hide-for-small-only"></div>
            </div>

            <div class="row">
                <div class="medium-6 columns">
                    <div class="f-field">
                        <label class="f-f-label" for="referralDropdown">
                            How did you hear about us? (Primary Marketing Source) <span data-tooltip aria-haspopup="true" class="has-tip" data-disable-hover="false" tabindex="1" title="Primary users can edit Marketing Sources in Marketing via the top navigation bar"><img src="<?=$icons['info']?>" /></span>
                            <?php if (!$marketing_source_required): ?>
                                <span class="f-fl-optional">(Optional)</span>
                            <?php endif; ?>
                        </label>
                        <?php if ($marketing_source_required): ?>
                        <select class="f-f-input" id="referralDropdown" required name="referralDropdown" value="">
                            <?php else: ?>
                            <select class="f-f-input" id="referralDropdown" name="projectReferralDropdown" value="">
                                <?php endif; ?>
                            </select>
                    </div>
                </div>
                <div class="medium-6 columns">
                    <div class="f-field">
                        <label class="f-f-label" for="secondaryReferralDropdown">
                            Secondary Marketing Source
                            <span class="f-fl-optional">(Optional)</span>
                        </label>
                            <select class="f-f-input" id="secondaryReferralDropdown" name="projectSecondaryMarketingTypeID" value="">
                            </select>
                    </div>
                </div>

                <br>
            </div>

            <div class="row">
                <div class="medium-6 columns">
                    <div class="f-field">
                        <label class="f-f-label" for="typeDropdown">
                            Project Type
                            <?php if (!$project_type_required): ?>
                                <span class="f-fl-optional">(Optional)</span>
                            <?php endif; ?>
                        </label>
                        <?php if ($project_type_required): ?>
                        <select class="f-f-input" id="typeDropdown" required name="projectType" value="">
                            <?php else: ?>
                            <select class="f-f-input" id="typeDropdown" name="projectType" value="">
                                <?php endif; ?>
                            <option value="">Select One</option>
                        </select>
                    </div>
                </div>
                <div class="medium-6 columns">
                    <div class="f-field">
                        <label class="f-f-label" for="priorityDropdown">
                            Priority <span class="f-fl-optional">(Optional)</span>
                        </label>
                        <select class="f-f-input" id="priorityDropdown" name="projectPriority" value="">
                            <option value="">Select One</option>
                        </select>
                    </div>
                </div>
                <br>
            </div>

            <div class="row">
                <div class="medium-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Summary <span class="f-fl-optional">(Optional) </span> <span data-tooltip aria-haspopup="true" class="has-tip" data-disable-hover="false" tabindex="1" title="Add project details or add custom questions in Company Profile > Settings > Projects"><img src="<?=$icons['info']?>" /></span>
                        </label>
                        <textarea class="f-f-input summary" rows="3" name="projectSummary"><?=$summary?></textarea>
                    </div>
                </div>
            </div>



            <hr class="section-divider">
            <p class="section-title">Project Note <span>(Optional)</span></p>
            <div class="row">
                <div class="medium-12 columns">
                    <div class="f-field">
                        <label class="f-f-label">
                            Note <span class="f-fl-internal">(Internal)</span>
                        </label>
                        <textarea class="f-f-input projectNote" rows="3" name="projectNote"><?=$notes?><?=$working_notes?></textarea>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="medium-9 columns">
                    <div class="f-field">
                        <label class="f-f-label">Pin Note <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="6" title="Pin note to the top of the note list and to the project management, project tab."><img src="<?=$icons['info']?>" /></span></label>
                        <div class="switch tiny">
                            <input class="switch-input" checked id="isPinnedAdd" type="checkbox" name="isPinnedNote" value="1">
                            <label class="switch-paddle" for="isPinnedAdd"></label>
                        </div>
                    </div>

                </div>
            </div>

            <hr class="section-divider">
            <p class="section-title">Appointment</p>
            <div class="row">
                <div class="medium-12 columns">
                    <button data-calendar-open class="button">Schedule</button>
                </div>
                <div class="medium-3 columns appointment-summary">
                    <p class="no-margin">
                        <label>Appointment Date:</label> <span class="appointment-date"></span>
                    </p>
                </div>
                <div class="medium-3 columns appointment-summary">
                    <p class="no-margin">
                        <label>Appointment Time:</label> <span class="appointment-time"></span><br/>
                    </p>
                </div>
                <div class="medium-3 columns appointment-summary">
                    <p class="no-margin">
                        <label>Salesperson:</label> <span class="appointment-salesperson"></span><br/>
                    </p>
                </div>
            </div>
            <hr class="section-divider">
            <div class="row introEmail">
                <div class="medium-12 columns">
                    <label>
                        <input type="checkbox" class="bottom" name="sendIntroEmail" value="1">
                        Send Introduction Email <span data-tooltip aria-haspopup="true" class="has-tip" data-disable-hover="false" tabindex="1" title="Customer will receive the introduction email (See Company Profile > Email Section to customize email content)."><img src="<?=$icons['info']?>" /></span>
                    </label>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <?=$submitButton?>
                    <a id="cancelAddCustomer" class="button secondary">Cancel</a>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <div class="callout warning validating-email-message">
                        <p>Verifying email deliverability...<span><a class="abort-validation">skip</a></span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="calendarContainer" class="row expanded">
        <div id="calendarShow" class="medium-12 columns no-pad">
            <div class="medium-12 columns">
                <div class="row expanded">
                    <div class="medium-9 columns">
                    </div>
                    <div class="medium-3 columns">
                        <button data-calendar-close class="button">Back To Customer</button>
                    </div>
                </div>
            </div>
            <div id="scheduleCalendar" class="medium-12 columns no-pad">
                <div class="row expanded">
                    <div class="calendarWrapper">
                        <div id="filterGroup">
                            <div class="header">
                                <span class="title">Filter
                                    <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="8" title="Filter: Select the users that you want displayed on the calendar. Order: Drag the users to set their display order.">
                                        <img src="<?=$icons['info']?>" />
                                    </span>
                                </span>
                                <div class="group">
                                    <button id="filter-clear" class="btn icon-cross"></button>
                                    <button id="filter-apply" class="btn icon-checkmark"></button>
                                </div>
                            </div>
                            <div class="list"></div>
                        </div>
                        <div id="calendarGroup">
                            <div class="dashboard-filter-bar table">
                                <div>
                                    <div class="filter">
                                        <button id="filter" class="btn icon-filter"></button>
                                    </div>
                                    <div class="todayButton">
                                        <a id="today" class="button today" style="">Today</a>
                                    </div>
                                    <div class="daySelector">
                                        <div>
                                            <div id="previous" class="arrow-left"></div>
                                        </div>
                                        <div style="width: 60%">
                                            <span id="calendarTitle" class="calendarTitle"></span>
                                        </div>
                                        <div>
                                            <div id="next" class="arrow-right"></div>
                                        </div>
                                    </div>
                                    <div class="button-group viewSelector">
                                        <a id="dailyView" class="button view view-left">Daily</a>
                                        <a id="resourceWeekView" class="button view view-center">Weekly</a>
                                        <a id="monthlyView" class="button view view-right active">Monthly</a>
                                    </div>
                                </div>
                            </div>
                            <div id='calendar'></div>
                            <div id="addEvent" class="reveal small" data-reveal data-close-on-esc="false" data-close-on-click="false">
                                <h2 id="modalTitle"></h2>
                                <p class="lead name"></p>
                                <div class="row">
                                    <div class="medium-12 columns">
                                        <label class="scheduledTitle"></label>
                                    </div>
                                    <div class="medium-5 columns" id="scheduleModalDD">
                                        <select class="evaluationData" name="salesperson" id="scheduleSalesperson">
                                            <option value="">--</option>
                                        </select>
                                    </div>
                                    <div class="medium-7 columns"></div>
                                </div>
                                <div class="row">
                                    <div class="medium-12 columns">
                                        <label>Start Time</label>
                                    </div>
                                    <div class="medium-4 columns">
                                        <input id="scheduledStartDate" class="evaluationData datepickerFrom date" type="text" name="scheduledStartDate" />
                                        <small class="form-error" id="startDateErr">Please select a scheduled start date</small>
                                    </div>
                                    <div class="medium-4 columns">
                                        <input id="scheduledStartTime" class="evaluationData timepicker" type="text" name="scheduledStartTime" readonly="readonly" />
                                        <small class="form-error" id="startTimeErr">Please enter a scheduled start time</small>
                                    </div>
                                    <div class="medium-2 columns">&nbsp;</div>
                                </div>
                                <div class="row">
                                    <div class="medium-12 columns">
                                        <label>End Time</label>
                                    </div>
                                    <div class="medium-4 columns">
                                        <input id="scheduledEndDate" class="evaluationData datepickerTo date" type="text" name="scheduledEndDate" />
                                        <small style="margin-bottom: .3rem;"class="form-error" id="endDateErr">Please select an end date</small>
                                    </div>
                                    <div class="medium-4 columns">
                                        <input readonly="readonly" id="scheduledEndTime" class="evaluationData timepicker" type="text" name="scheduledEndTime" />
                                        <small style="margin-bottom: .3rem;" id="endTimeErr" class="form-error">Please enter an end time</small>
                                    </div>
                                    <div class="medium-2 columns">&nbsp;</div>
                                </div>
                                <div class="row">
                                    <div class="medium-12 columns">
                                        <label>Description <small><i>(Internal)</i></small></label>
                                    </div>
                                    <div class="medium-8 columns">
                                        <textarea class="internalNote" rows="3" name="scheduleDescription"></textarea>
                                    </div>
                                </div>
                                <div class="row notifyCustomer">
                                    <div class="medium-12 columns">
                                        <label>
                                            <input type="checkbox" name="notifyCustomerAppointment" value="1" />
                                            Notify Customer of Appointment <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="1" title="Customer will receive an appointment email (See Company Profile > Email Section to customize email content)."><img src="<?=$icons['info']?>" /></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="medium-12 columns">
                                        <input type="hidden" name="tempID" value=""/>
                                        <input type="hidden" name="projectScheduleID" value=""/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="medium-12 columns" id="okButtonDiv">
                                        <p><a id="confirmAppointmentYes" class="button top">Ok</a> <a id="confirmAppointmentNoSelect" class="button secondary select top">Cancel</a> <a id="confirmAppointmentNoDrag" class="button secondary drag top">Cancel</a></p>
                                    </div>
                                </div>
                            </div>
                            <div id="viewEvent" class="reveal small" data-reveal></div>
                            <div id="cancelEvent" class="reveal tiny" data-reveal data-close-on-esc="false" data-close-on-click="false">
                                <h2 id="modalTitle">Cancel</h2>
                                <p>Are you sure you want to cancel this event?</p>
                                <input type="hidden" name="tempID" value="" />
                                <p><a id="cancelConfirmYes" class="button tiny">Yes</a><a id="cancelConfirmNo" style="margin: 0 0 0 1rem;" class="button tiny success">No</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<div id="duplicateResultModal" class="reveal full" data-reveal data-close-on-click="false" data-close-on-esc="false">
    <h2 class="text-center" id="modalTitle">Duplicate Customers</h2>
    <table>
        <thead>
        <tr>
            <th>Type</th>
            <th>Name</th>
            <th>Business</th>
            <th>Address</th>
            <th>City</th>
            <th>State</th>
            <th>Zip</th>
            <th>Email</th>
            <th>Phone</th>
            <th></th>
        </tr>
        </thead>
        <tbody></tbody>
    </table>
    <p class="text-center"><a class="button" data-close>Close</a></p>
    <button class="close-button" data-close aria-label="Close modal" type="button">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div id="cancelCustomerModal" class="reveal tiny" data-reveal data-close-on-click="false" data-close-on-esc="false">
    <h2 class="text-center" id="modalTitle">Cancel</h2>
    <p class="lead text-center">Are you sure you want to cancel?  Your information will not be saved.</p>
    <p class="text-center"><a id="cancelCustomerYes" class="button">Yes</a> <a id="cancelCustomerNo" class="button secondary">No</a></p>
</div>
<div id="latLongModal" class="reveal small" data-reveal data-close-on-click="false" data-close-on-esc="false">
    <p class="lead text-center">Your Address Cannot Be Verified</p>
    <p id="addressPartial" class="is-invalid-input" data-invalid="" style="padding: 0 .5rem .5rem .5rem;">
        <br>You entered </br>
        <span id="addressPartialText"></span>
        <button id="usePartial" class="button right" style="margin-top: -1rem">Use</button>
    </p>
    <p id="addressFull" style="padding: 0 .5rem .5rem .5rem;">
        <br>We suggest </br>
        <span id="addressFullText"></span>
        <button id="useComplete" class="button right" style="margin-top: -1rem">Use</button>
    </p>
    <input type="hidden" id="partialMatchLatitude" name="partialMatchLatitude" value="" />
    <input type="hidden" id="partialMatchLongitude" name="partialMatchLongitude" value="" />
    <p class="text-center">
        <button id="cancelAddress" class="button secondary" style="margin-top: 1rem;margin-bottom: .5rem;" data-close>Cancel</button>
    </p>
</div>
<iframe name="hidden-iframe" style="display: none;"></iframe>
<div type="hidden" id="mapsAddress" style="display: none;"></div>
<div type="hidden" id="hiddenMap" style="display: none;"></div>
<div class="is-hidden" id="userID"><?=$userID?></div>
<div id="confirmSaveUnscheduled" class="reveal tiny" data-reveal data-close-on-esc="false" data-close-on-click="false">
    <h2 id="modalTitle">Save without Scheduling?</h2>
    <p>Are you sure you want to save this customer without scheduling an evaluation?</p>
    <p class="text-center"><button id="saveDontSchedule" class="button">Yes</button>
        <button id="dontSaveWithoutSchedule" class="button secondary" >No</button>
    </p>
</div>
<div id="confirmSaveDuplicates" class="reveal tiny" data-reveal data-close-on-esc="false" data-close-on-click="false">
    <h2 id="modalTitle">Similar Records Found</h2>
    <p>We have found similar records in the system for this customer. Are you sure you want to proceed?</p>
    <p class="text-center"><button id="saveWithDuplicates" class="button">Yes</button>
        <button id="dontSaveWithDuplicates" class="button secondary" >No</button>
    </p>
</div>
