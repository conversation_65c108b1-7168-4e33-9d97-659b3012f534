'use strict';

const moment = require('moment-timezone');

import Api from '@ca-package/api';
import Page from '@ca-package/router/src/page';
import {findChild, jsSelector, onClickDestroy, onClickWatcher} from '@ca-package/dom';

import FormInput from '@ca-submodule/form-input';
FormInput.use(require('@ca-submodule/form-input/src/switch'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
const Tooltip = require('@ca-submodule/tooltip');

import emails_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/emails.hbs';
import email_item_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/emails/item.hbs';
import preview_append_tpl from "@cam-setup-wizard-tpl/pages/main-pages/step-pages/emails/preview_append.hbs";
import preview_prepend_tpl from "@cam-setup-wizard-tpl/pages/main-pages/step-pages/emails/preview_prepend.hbs";

const EmailTypes = {
    NEW_CUSTOMER: Api.Constants.EmailTemplates.Type.NEW_CUSTOMER,
    SALES_APPOINTMENT: Api.Constants.EmailTemplates.Type.SALES_APPOINTMENT,
    SALES_APPOINTMENT_REMINDER: Api.Constants.EmailTemplates.Type.SALES_APPOINTMENT_REMINDER,
    CUSTOMER_BID: Api.Constants.EmailTemplates.Type.CUSTOMER_BID,
    BID_ACCEPTED: Api.Constants.EmailTemplates.Type.BID_ACCEPTED,
    BID_REJECTED: Api.Constants.EmailTemplates.Type.BID_REJECTED,
    INSTALLATION_APPOINTMENT: Api.Constants.EmailTemplates.Type.INSTALLATION_APPOINTMENT,
    INSTALLATION_APPOINTMENT_REMINDER: Api.Constants.EmailTemplates.Type.INSTALLATION_APPOINTMENT_REMINDER,
    WARRANTIES: Api.Constants.EmailTemplates.Type.WARRANTIES,
    INVOICE: Api.Constants.EmailTemplates.Type.INVOICE,
    BID_FOLLOW_UP: Api.Constants.EmailTemplates.Type.BID_FOLLOW_UP
};

const EmailTypeNames = {
    [EmailTypes.NEW_CUSTOMER]: 'New Customer',
    [EmailTypes.SALES_APPOINTMENT]: 'Sales Appointment',
    [EmailTypes.SALES_APPOINTMENT_REMINDER]: 'Sales Appointment Reminder',
    [EmailTypes.CUSTOMER_BID]: 'Customer Bid',
    [EmailTypes.BID_ACCEPTED]: 'Bid Accepted',
    [EmailTypes.BID_REJECTED]: 'Bid Rejected',
    [EmailTypes.INSTALLATION_APPOINTMENT]: 'Installation Appointment',
    [EmailTypes.INSTALLATION_APPOINTMENT_REMINDER]: 'Installation Appointment Reminder',
    [EmailTypes.WARRANTIES]: 'Warranties',
    [EmailTypes.INVOICE]: 'Invoice'
};

/**
 * @memberof module:SetupWizard/Pages/MainPages/StepPages
 */
export class Emails extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            previous: {
                route: 'step.bid_customization',
                step: parent.steps.BID_CUSTOMIZATION
            },
            next: {
                route: 'step.terms_conditions',
                step: parent.steps.TERMS_CONDITIONS
            },
            setup_emails: false,
            setup_emails_completed_at: null,
            emails_loaded: false,
            active_email: null,
            email_changed: false,
            emails: {
                [EmailTypes.NEW_CUSTOMER]: {
                    instructions: 'Sent when customer is added to the system and appointment is not scheduled ("Send Introduction Email" checkbox must be checked).'
                },
                [EmailTypes.SALES_APPOINTMENT]: {
                    instructions: 'Sent when a sales appointment is scheduled ("Notify Customer of Appointment" checkbox must be checked).',
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'date_time', 'date', 'time', 'end_time', 'address', 'wisetack_prequal_link'
                    ]
                },
                [EmailTypes.SALES_APPOINTMENT_REMINDER]: {
                    instructions: 'Sent to customer 24 hours before sales appointment ("Reminder 24 Hours Before Appointment" setting must be enabled).',
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'date_time', 'date', 'time', 'end_time', 'address', 'wisetack_prequal_link'
                    ]
                },
                [EmailTypes.CUSTOMER_BID]: {
                    instructions: 'Sent with the bid to the customer. The bid link will be appended after the email content.',
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'viewBidLink'
                    ]
                },
                [EmailTypes.BID_ACCEPTED]: {
                    instructions: 'Automatically sent when the customer accepts the bid.'
                },
                [EmailTypes.BID_REJECTED]: {
                    instructions: 'Automatically sent when the customer rejects the bid.',
                },
                [EmailTypes.INSTALLATION_APPOINTMENT]: {
                    instructions: 'Sent when an installation/job appointment is scheduled ("Notify Customer of Appointment" checkbox must be checked).',
                    tags: [
                        'installerFirstName', 'installerLastName', 'installerBio', 'installerEmail', 'installerPhone', 'installerPicture', 'date_time', 'date', 'time', 'end_time', 'address'
                    ]
                },
                [EmailTypes.INSTALLATION_APPOINTMENT_REMINDER]: {
                    instructions: 'Sent to customer 24 hours before installation/job appointment ("Reminder 24 Hours Before Appointment" setting must be enabled).',
                    tags: [
                        'installerFirstName', 'installerLastName', 'installerBio', 'installerEmail', 'installerPhone', 'installerPicture', 'date_time', 'date', 'time', 'end_time', 'address'
                    ]
                },
                [EmailTypes.WARRANTIES]: {
                    instructions: 'Sent with the warranties to the customer.'
                },
                [EmailTypes.INVOICE]: {
                    instructions: 'Sent with the invoice to the customer.'
                }
            },
            is_wisetack_enabled: setup_wizard_data.features.wisetack_api,
            is_merchant_approved: setup_wizard_data.wisetack.is_merchant_approved,
        });

    };

    /**
     * Check if Wisetack tags should be shown
     *
     * @returns {boolean}
     */
    isWisetackEnabledAndApproved() {
        const { is_wisetack_enabled, is_merchant_approved } = this.state
        return is_wisetack_enabled && is_merchant_approved;
    }

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.state.parent.getPageContainer().scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Fetch email data for company
     * @returns {Promise}
     */
    async fetchEmails() {
        try {
            let {entities: email_templates} = await Api.Resources.EmailTemplates()
                .fields(['id', 'name', 'type', 'source', 'content', 'subject', 'is_send_from_salesperson',])
                .filter('status', Api.Constants.EmailTemplates.Status.ACTIVE)
                .sort('type', 'asc')
                .all();
            this.state.email_templates = email_templates.map(entity => entity.data);
            for (let email of this.state.email_templates) {
                Object.assign(this.state.emails[email.type], email);
            }

        } catch (e) {}
    };

    /**
     * Load emails into page
     *
     * @returns {Promise}
     */
    async loadEmails() {
        let company_address_2 = '';
        if (this.state.company_data.address_2 != null) {
            company_address_2 = ', ' + this.state.company_data.address_2;
        }
        let company_address = `${this.state.company_data.address}${company_address_2}, ${this.state.company_data.city}, ${this.state.company_data.state} ${this.state.company_data.zip}`;

        let company_phones = '';
        for (let phone in this.state.company_data.phones) {
            let this_phone = this.state.company_data.phones[phone];
            company_phones += this_phone.description + ': ' + this_phone.number + '<br/>';
        }

        this.state.tag_defaults = {
            company_name: {
                label: 'Company Name',
                default: this.state.company_data.name
            },
            evaluatorFirstName: {
                label: 'Salesperson First Name',
                default: this.state.company_data.user.first_name
            },
            evaluatorLastName: {
                label: 'Salesperson Last Name',
                default: this.state.company_data.user.last_name
            },
            evaluatorBio: {
                label: 'Salesperson Bio',
                default: this.state.company_data.user.bio !== null ? this.state.company_data.user.bio : ''
            },
            evaluatorEmail: {
                label: 'Salesperson Email',
                default: this.state.company_data.user.email
            },
            evaluatorPhone: {
                label: 'Salesperson Phone',
                default: this.state.company_data.user.phone_number
            },
            evaluatorPicture: {
                label: 'Salesperson Picture',
                default: `<img style="max-height:180px;" src="${window.fx_url.assets.IMAGE+'no-image-user-profile.jpg'}" />`
            },
            date_time: {
                label: 'Appointment Date/Time',
                default: 'Monday, August 8, 2023 at 9:00 am'
            },
            date: {
                label: 'Appointment Date',
                default: 'Monday, August 8, 2023'
            },
            time: {
                label: 'Appointment Start Time',
                default: '9:00 am'
            },
            end_time: {
                label: 'Appointment End Time',
                default: '10:00 am'
            },
            address: {
                label: 'Appointment Address, City, State, Zip',
                default: '1234 Street Address, Cityville, MO 12345'
            },
            viewBidLink: {
                label: 'View Bid Link',
                default: `<a href="#" style="color: #${this.state.company_data.color};text-decoration: underline;">View Bid</a>`
            },
            installerFirstName: {
                label: 'Installer First Name',
                default: this.state.company_data.user.first_name
            },
            installerLastName: {
                label: 'Installer Last Name',
                default: this.state.company_data.user.last_name
            },
            installerBio: {
                label: 'Installer Bio',
                default: this.state.company_data.user.bio !== null ? this.state.company_data.user.bio : ''
            },
            installerEmail: {
                label: 'Installer Email',
                default: this.state.company_data.user.email
            },
            installerPhone: {
                label: 'Installer Phone',
                default: this.state.company_data.user.phone_number
            },
            installerPicture: {
                label: 'Installer Picture',
                default: `<img style="max-height:180px;" src="${window.fx_url.assets.IMAGE+'no-image-user-profile.jpg'}" />`
            },
            wisetack_prequal_link: {
                label: 'Wisetack Prequal Link',
                default: ""
            }
        };

        for (let email in this.state.emails) {
            let this_email = this.state.emails[email],
                send_sales_option = this_email.is_send_from_salesperson !== null;

            let elem = $(email_item_tpl({
                type: this_email.type,
                id: this_email.id,
                label: EmailTypeNames[this_email.type],
                email_content: this_email.content,
                instructions: this_email.instructions,
                send_sales_switch: send_sales_option
            }));
            this.elem.accordion_container.append(elem);
            this_email.elem = elem;
            this_email.content = findChild(elem, jsSelector('content'));
            this_email.warning = findChild(elem, jsSelector('warning'));
            this_email.error = findChild(elem, jsSelector('error'));
            this_email.save = findChild(elem, jsSelector('save'));

            this_email.save.prop('disabled', true);

            let prepend_text = preview_prepend_tpl({
                    email_from: this.state.company_data.email_from,
                    todays_date: moment().format("MMMM D, YYYY"),
                    subject: this_email.subject
                }),
                append_text = preview_append_tpl({
                    company_logo: this.state.company_data.logo_media_urls.email_thumbnail,
                    company_name: this.state.company_data.name,
                    company_address: company_address,
                    company_phones: company_phones,
                    website: this.state.company_data.website,
                    add_bid_link: this_email.type === EmailTypes.CUSTOMER_BID,
                    company_color: `#${this.state.company_data.color}`
                });

            let textarea = findChild(elem, jsSelector('textarea')),
                textarea_config = {
                preset: 'simple',
                remove_empty_paragraphs: true,
                alignment: true,
                font_size: true,
                preview: true,
                preview_config:{
                    title: `Email Preview: ${this_email.name}`,
                    prepend: prepend_text,
                    append: append_text
                }
            };
            if (this_email.tags !== undefined) {
                let tags = [],
                    replacements = [];
                for (let tag of this_email.tags) {
                    tags.push({
                        label: this.state.tag_defaults[tag].label,
                        content: tag
                    });
                    replacements.push({
                        content: tag,
                        replacement: this.state.tag_defaults[tag].default
                    });
                }

                // Set preview wisetack value
                if (this.isWisetackEnabledAndApproved()) {
                    replacements = replacements.map(item => {
                        if (item.content === 'wisetack_prequal_link') {
                            item.replacement = `<p><a href="#">Click here</a> to prequalify for financing through Wisetack.</p>`;
                        }
                        return item;
                    });
                }

                textarea_config.tags = tags;
                textarea_config.preview_config['tag_replacements'] = replacements;
            }
            this_email.textarea = FormInput.init(textarea, textarea_config)
                .on('editor_changed', () => {
                    this_email.save.prop('disabled', false);
                    this.state.email_changed = true;
                });
            this_email.editor = await this_email.textarea.promise;

            if (send_sales_option) {
                this_email.check = findChild(elem, jsSelector('send_from_salesperson')).prop('checked', this_email.is_send_from_salesperson).trigger('change');
                this_email.switch = FormInput.init(this_email.check);
                this_email.check.on('change', () => {
                    this.state.email_changed = true;
                    this_email.save.prop('disabled', false);
                });
            }
        }
        this.state.emails_loaded = true;
    };

    /**
     * Toggle email open and closed
     *
     * @param {number} current_email
     */
    toggleEmail(current_email) {
        if (this.state.active_email !== null) {
            this.state.emails[this.state.active_email].elem.removeClass('t-active');
            this.state.emails[this.state.active_email].content.stop().slideUp(300);
        }
        if (current_email === this.state.active_email) {
            this.state.active_email = null;
            return;
        }
        this.state.active_email = current_email;
        this.state.emails[current_email].elem.addClass('t-active');
        this.state.emails[current_email].content.stop().slideDown(300);
    };

    /**
     * Save email content
     *
     * @param {string} id
     */
    saveEmail(id) {
        this.clearError();
        let active_email = this.state.emails[this.state.active_email];
        let email_content = active_email.editor.getContent();

        if (email_content === '') {
            active_email.error.addClass('t-show');
            return;
        }
        this.state.parent.startWorking();
        active_email.error.removeClass('t-show');

        let data = {
            content: email_content
        };
        if (active_email.is_send_from_salesperson !== null) {
            data['is_send_from_salesperson'] = active_email.check.is(':checked') ? true : false;
        }

        try {
            Api.Resources.EmailTemplates().partialUpdate(id, data).then((entity, response) => {
                this.state.email_changed = false;
                active_email.warning.removeClass('t-show');
                if (!this.state.setup_emails) {
                    this.changeSkipToNext();
                    this.state.setup_emails = true;
                }
                this.toggleEmail(this.state.active_email);
                this.state.parent.resetWorking();
            }, (error) => {
                this.setError('Unable to save email, please contact support');
                this.state.parent.resetWorking();
            });
        } catch (e) {
            //call error on page
        }
    };

    /**
     * Save current step for company
     *
     * @returns {Promise}
     */
    async saveCompanyStep(type) {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: type.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(type.route);
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Save step when user clicks navigation button
     *
     * @param {string} endpoint
     * @param {boolean} unload
     * @returns {Promise}
     */
    async saveStep(endpoint, unload = false) {
        this.state.parent.startWorking();

        try {
            let response = await $.ajax({
                url: window.fx_url.API + `setup-wizard/${endpoint}`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: this.state.next.step
                })
            });
            if (endpoint === 'next') {
                this.state.setup_emails_completed_at = response.emailsCompletedAt;
            }
            this.state.parent.resetWorking();
            if (!unload) {
                this.router.navigate(this.state.next.route);
            }
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Determine if step needs to be saved or skipped
     */
    handleNextClick() {
        let endpoint = this.state.setup_emails ? 'next' : 'skip';

        if (endpoint === 'next' && this.state.setup_emails_completed_at !== null) {
            this.saveCompanyStep(this.state.next);
            return;
        }
        this.saveStep(endpoint);
    };

    /**
     * Change skip to next
     */
    changeSkipToNext() {
        this.state.parent.setSkipRoute(null);
        this.state.parent.setNextRoute(this.state.next.route);
        this.state.parent.changeNavState();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.parent.scrollTopParent();
        this.state.parent.setBackRoute(this.state.previous.route);
        this.state.parent.setSkipRoute(this.state.next.route);
        this.state.parent.setNextRoute(null);
        await super.load(request, next);
        this.state.company_data = request.data.company;

        if (!this.state.emails_loaded) {
            this.state.parent.startWorking();
            await this.fetchEmails();
            await this.loadEmails();
            this.state.parent.resetWorking();
        }
        Tooltip.initAll(this.elem.accordion_container);
        // toggle the first email open
        this.toggleEmail(EmailTypes.NEW_CUSTOMER);


        if (request.data.emails) {
            this.state.setup_emails = true;
            this.changeSkipToNext();
        }
        if (request.data.emails_completed_at !== null) {
            this.state.setup_emails_completed_at = request.data.emails_completed_at;
        }

        let that = this;
        onClickWatcher(this.state.parent.getMainHeader(), jsSelector('button'), function () {
            let $this = $(this);
            if ($this.data('value') === 'next' || $this.data('value') === 'skip') {
                that.handleNextClick();
                return false;
            }
            if ($this.data('value') === 'back') {
                that.saveCompanyStep(that.state.previous);
                return false;
            }
        }, false);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.toggleEmail(this.state.active_email);
        this.state.parent.setSkipRoute(null);
        onClickDestroy(this.state.parent.getMainHeader());

        if (this.state.setup_emails && this.state.setup_emails_completed_at === null) {
            this.saveStep('next', true);
        }

        request.data.emails = this.state.setup_emails;
        request.data.emails_completed_at = this.state.setup_emails_completed_at;

        this.state.parent.updateSetupData(request.data);
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.accordion_container = findChild(root, jsSelector('accordion'));

        let that = this;
        onClickWatcher(this.elem.accordion_container, jsSelector('item'), function () {
            if (that.state.email_changed) {
                let active_email = that.state.emails[that.state.active_email],
                    position = active_email.elem.position();
                active_email.warning.addClass('t-show');
                that.state.parent.elem.pages.scrollTop(position.top);
                return;
            }
            that.toggleEmail($(this).data('type'));
        }, true);

        onClickWatcher(this.elem.accordion_container, jsSelector('save'), function () {
            that.saveEmail($(this).data('id'));
        }, true);
    };

    /**
     * Render page
     */
    render() {
        return emails_tpl({
            brand_name: window.setup_wizard_data.brand_name
        });
    };
}
