<?php

declare(strict_types=1);

namespace Common\Seeders;

use App\Classes\Acl;
use App\Resources\SubscriptionResource;
use Common\Classes\Seeder;
use Common\DataSources\SubscriptionSource;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use Core\StaticAccessors\App;

/**
 * Class SubscriptionSeeder
 *
 * @package Common\Seeders
 */
class SubscriptionSeeder extends Seeder
{
    /**
     * @var int|null
     */
    protected ?int $reseller_id = null;

    /**
     * @var string|null
     */
    protected ?string $template_alias = null;

    /**
     * @var bool|null
     */
    protected ?bool $is_default = false;

    /**
     * Set reseller id
     *
     * @param int $id
     * @return $this
     */
    public function resellerID(int $id): self
    {
        $this->reseller_id = $id;
        return $this;
    }

    /**
     * Get reseller id
     *
     * @return int
     * @throws AppException
     */
    public function getResellerID(): int
    {
        if ($this->reseller_id === null) {
            throw new AppException('No reseller id defined');
        }
        return $this->reseller_id;
    }

    /**
     * Set template alias to use for subscription based on subscription sources
     *
     * @param string $alias
     * @return $this
     */
    public function templateAlias(string $alias): self
    {
        $this->template_alias = $alias;
        return $this;
    }

    /**
     * Get template alias
     *
     * @return string
     * @throws AppException
     */
    public function getTemplateAlias(): string
    {
        if ($this->template_alias === null) {
            throw new AppException('No template alias defined');
        }
        return $this->template_alias;
    }

    /**
     * Set if subscription is default for reseller
     *
     * @param bool $bool
     * @return $this
     */
    public function isDefault(bool $bool): self
    {
        $this->is_default = $bool;
        return $this;
    }

    /**
     * Get if is default
     *
     * @return bool
     * @throws AppException
     */
    public function getIsDefault(): bool
    {
        if ($this->is_default === null) {
            throw new AppException('Default status defined');
        }
        return $this->is_default;
    }

    /**
     * Run seeder
     *
     * @throws AppException
     */
    public function run(): void
    {
        /** @var SubscriptionSource $subscription_source */
        $subscription_source = App::get(SubscriptionSource::class);

        $subscription = $subscription_source->get($this->getTemplateAlias());

        foreach ($subscription['options'] as $i => &$option) {
            $option['is_preferred'] = false;
            unset($option);
        }
        $subscription['reseller_id'] = $this->getResellerID();
        $subscription['status'] = SubscriptionResource::STATUS_ACTIVE;
        $subscription['is_default'] = $this->getIsDefault();
        /** @var int $subscription_id */
        $subscription_id = SubscriptionResource::make(Acl::make())
            ->create(Entity::make($subscription))
            ->store('remove_default', false)
            ->run();

        $this->primaryKey($subscription_id);
    }
}
