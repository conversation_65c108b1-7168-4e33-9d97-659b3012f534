<?php

namespace Common\Extensions;

use Common\Classes\DB\ModelActions;
use Common\Models;
use Common\Models\History;
use Core\Components\DB\Classes\PointInTimeHistory;
use Core\Interfaces\KernelInterface;
use Illuminate\Database\Eloquent\Relations\Relation;

class ModelExtension
{
    public function load(KernelInterface $kernel)
    {
        $kernel->singleton(ModelActions::class, function () {
            return new ModelActions;
        });

        Relation::morphMap([
            // 1
            Models\CustomBidLineItem::TYPE_GENERAL => Models\CustomBidGeneralLineItem::class,

            // 2
            Models\CustomBidLineItem::TYPE_PRODUCT => Models\CustomBidGeneralLineItem::class,

            // 3
            Models\CustomBidLineItem::TYPE_SERVICE => Models\CustomBidServiceLineItem::class,

            // system
            4 => null,

            // 5
            5 => Models\Company::class,

            // 6
            Models\ProjectCost::TYPE_GENERAL => Models\ProjectGeneralCost::class,

            // 7
            Models\ProjectCost::TYPE_COMMISSION => Models\ProjectCommissionCost::class,

            // 8
            Models\UserApiToken::TYPE_WEB => Models\UserWebApiToken::class,

            // 9 - available

            // 10
            Models\FormItemGroupLayoutItem::TYPE_GROUP => Models\FormItemGroup::class,

            // 11
            11 => Models\FormItemGroupField::class,

            // 12
            Models\FormItemGroupFieldProduct::TYPE_CATEGORY => Models\ProductCategory::class,

            // 13
            13 => Models\ProductItem::class,

            // 14
            Models\CompanyFormItem::TYPE_BID => Models\CompanyFormBidItem::class,

            // 15
            Models\BidItemLineItem::TYPE_GENERAL => null,

            // 16
            Models\BidItemLineItem::TYPE_PRODUCT => Models\BidItemProductLineItem::class,

            // 17
            Models\BidItemLineItem::TYPE_DISCOUNT => Models\BidItemPriceAdjustmentLineItem::class,

            // 18
            Models\BidItemLineItem::TYPE_FEE => Models\BidItemPriceAdjustmentLineItem::class,

            // 19
            Models\FormItemGroupLayoutItem::TYPE_TEMPLATE => Models\FormItemGroupTemplate::class,

            // 20
            Models\CompanyPaymentMethod::TYPE_ACH => Models\CompanyAchPaymentMethod::class,

            // 21
            Models\CompanyPaymentMethod::TYPE_CREDIT_CARD => Models\CompanyCreditCardPaymentMethod::class,

            // 22
            Models\BidItemMedia::TYPE_LIBRARY => Models\Media::class,

            // 23
            Models\BidItemPaymentTerm::TYPE_ONE_TIME => Models\BidItemOneTimePaymentTerm::class,

            // 24
            Models\BidItemPaymentTerm::TYPE_INSTALLMENT => Models\BidItemInstallmentPaymentTerm::class,

            // 25
            Models\NotificationDistribution::TYPE_EMAIL => Models\EmailMessage::class,

            // 26
            Models\EmailMessageAddress::TYPE_COMPANY_FROM => Models\Company::class,

            // 27
            Models\EmailMessageAddress::TYPE_COMPANY_REPLY => Models\Company::class,

            // 28
            28 => Models\User::class,

            // 29
            29 => Models\Customer::class,

            // 30
            Models\EmailMessageAddress::TYPE_PROJECT_CONTACT => Models\ProjectEmail::class,

            // 31
            Models\EmailMessageAddress::TYPE_BRAND_FROM => Models\Brand::class,

            // 32
            Models\EmailMessageAddress::TYPE_BRAND_REPLY => Models\Brand::class,

            // 33
            Models\EmailMessageAddress::TYPE_REGISTRATION => Models\Registration::class,

            // 34
            Models\NotificationDistribution::TYPE_TEXT => Models\TextMessage::class,

            // 35
            Models\TextMessageNumber::TYPE_CUSTOMER => Models\CustomerPhone::class,

            // 36
            Models\TextMessageNumber::TYPE_FROM_MESSAGE_SERVICE => Models\TextMessageService::class,

            // 37
            Models\CalendarFeed::OWNER_TYPE_USER => Models\User::class,

            // 38
            Models\SystemFormItem::TYPE_BID => Models\SystemFormBidItem::class,

            // 39
            39 => Models\CompanyFormItemField::class,

            // 40
            Models\FormItemEntryGroupFieldOption::FIELD_OPTION_SOURCE_STRUCTURE => Models\FormItemGroupFieldOption::class,

            // 41
            Models\FormItemEntryGroupFieldOption::FIELD_OPTION_SOURCE_COMPANY => Models\CompanyFormItemFieldOption::class,

            // 42
            42 => Models\CompanyFormItem::class,

            // 43
            43 => Models\SystemFormItem::class,

            // 44
            44 => Models\ProjectSchedule::class,

            // 45
            45 => Models\UserEvent::class,

            // 46
            46 => Models\CompanyEvent::class,

            // 47
            47 => Models\Property::class,

            // 48
            48 => Models\Project::class,

            // 49
            49 => Models\Lead::class,

            // 50
            50 => Models\Task::class,

            // 51
            51 => Models\BidItem::class
        ]);

        PointInTimeHistory::modelMap([
            Models\BidContent::class => History\BidContentHistory::class,
            Models\BidContentProductItem::class => History\BidContentProductItemHistory::class,
            Models\BidItem::class => History\BidItemHistory::class,
            Models\BidItemContent::class => History\BidItemContentHistory::class,
            Models\BidItemCustomDrawing::class => History\BidItemCustomDrawingHistory::class,
            Models\BidItemDrawing::class => History\BidItemDrawingHistory::class,
            Models\BidItemInstallmentPaymentTerm::class => History\BidItemInstallmentPaymentTermHistory::class,
            Models\BidItemInstallmentPaymentTermInstallment::class => History\BidItemInstallmentPaymentTermInstallmentHistory::class,
            Models\BidItemLineItem::class => History\BidItemLineItemHistory::class,
            Models\BidItemMedia::class => History\BidItemMediaHistory::class,
            Models\BidItemOneTimePaymentTerm::class => History\BidItemOneTimePaymentTermHistory::class,
            Models\BidItemPaymentTerm::class => History\BidItemPaymentTermHistory::class,
            Models\BidItemPriceAdjustmentLineItem::class => History\BidItemPriceAdjustmentLineItemHistory::class,
            Models\BidItemProductLineItem::class => History\BidItemProductLineItemHistory::class,
            Models\BidItemSection::class => History\BidItemSectionHistory::class,
            Models\BidItemSectionForm::class => History\BidItemSectionFormHistory::class,

            Models\Company::class => History\CompanyHistory::class,
            Models\CompanyAchPaymentMethod::class => History\CompanyAchPaymentMethodHistory::class,
            Models\CompanyCreditCardPaymentMethod::class => History\CompanyCreditCardPaymentMethodHistory::class,
            Models\CompanyInvoice::class => History\CompanyInvoiceHistory::class,
            Models\CompanyInvoiceCredit::class => History\CompanyInvoiceCreditHistory::class,
            Models\CompanyInvoiceLineItem::class => History\CompanyInvoiceLineItemHistory::class,
            Models\CompanyPaymentMethod::class => History\CompanyPaymentMethodHistory::class,
            Models\CompanyPhone::class => History\CompanyPhoneHistory::class,
            Models\CompanySetting::class => History\CompanySettingHistory::class,
            Models\CompanySubscription::class => History\CompanySubscriptionHistory::class,
            Models\CompanySubscriptionPriceAdjustment::class => History\CompanySubscriptionPriceAdjustmentHistory::class,

            Models\ContentPartial::class => History\ContentPartialHistory::class,
            Models\ContentTemplate::class => History\ContentTemplateHistory::class,

            Models\Customer::class => History\CustomerHistory::class,
            Models\CustomerPhone::class => History\CustomerPhoneHistory::class,

            Models\Drawing::class => History\DrawingHistory::class,
            Models\DrawingNode::class => History\DrawingNodeHistory::class,

            Models\EmailValidation::class => History\EmailValidationHistory::class,

            Models\File::class => History\FileHistory::class,
            Models\FileVariant::class => History\FileVariantHistory::class,

            Models\FormItemEntry::class => History\FormItemEntryHistory::class,
            Models\FormItemEntryGroup::class => History\FormItemEntryGroupHistory::class,
            Models\FormItemEntryGroupFieldValue::class => History\FormItemEntryGroupFieldValueHistory::class,
            Models\FormItemEntryGroupFieldFile::class => History\FormItemEntryGroupFieldFileHistory::class,
            Models\FormItemEntryGroupFieldOption::class => History\FormItemEntryGroupFieldOptionHistory::class,
            Models\FormItemEntryGroupFieldProduct::class => History\FormItemEntryGroupFieldProductHistory::class,

            Models\CompanyFormCategory::class => History\CompanyFormCategoryHistory::class,
            Models\CompanyFormCategoryItem::class => History\CompanyFormCategoryItemHistory::class,
            Models\Pivots\CompanyFormCategoryItem::class => History\Pivots\CompanyFormCategoryItemHistory::class,

            Models\CompanyFormItem::class => History\CompanyFormItemHistory::class,
            Models\CompanyFormBidItem::class => History\CompanyFormBidItemHistory::class,

            Models\SystemFormCategory::class => History\SystemFormCategoryHistory::class,
            Models\SystemFormCategoryItem::class => History\SystemFormCategoryItemHistory::class,
            Models\Pivots\SystemFormCategoryItem::class => History\Pivots\SystemFormCategoryItemHistory::class,

            Models\GoogleStaticImage::class => History\GoogleStaticImageHistory::class,

            Models\Media::class => History\MediaHistory::class,

            Models\ProductCategory::class => History\ProductCategoryHistory::class,
            Models\ProductCategoryItem::class => History\ProductCategoryItemHistory::class,
            Models\Pivots\ProductCategoryItem::class => History\Pivots\ProductCategoryItemHistory::class,
            Models\ProductItem::class => History\ProductItemHistory::class,
            Models\ProductItemMeta::class => History\ProductItemMetaHistory::class,
            Models\ProductItemPrice::class => History\ProductItemPriceHistory::class,

            Models\Project::class => History\ProjectHistory::class,
            Models\ProjectEmail::class => History\ProjectEmailHistory::class,
            Models\ProjectFile::class => History\ProjectFileHistory::class,
            Models\ProjectNote::class => History\ProjectNoteHistory::class,
            Models\ProjectSchedule::class => History\ProjectScheduleHistory::class,

            Models\Property::class => History\PropertyHistory::class,

            Models\SystemFormItem::class => History\SystemFormItemHistory::class,
            Models\SystemFormBidItem::class => History\SystemFormBidItemHistory::class,

            Models\Unit::class => History\UnitHistory::class,

            Models\Material::class => History\MaterialHistory::class,
            Models\AdditionalCost::class => History\AdditionalCostHistory::class,

            Models\ProductItemMaterial::class => History\ProductItemMaterialHistory::class,
            Models\ProductItemAdditionalCost::class => History\ProductItemAdditionalCostHistory::class,

            Models\BidItemProductMaterialLineItem::class => History\BidItemProductMaterialLineItemHistory::class,
            Models\BidItemProductAdditionalCostLineItem::class => History\BidItemProductAdditionalCostLineItemHistory::class,

            Models\EmailTemplate::class => History\EmailTemplateHistory::class,

            Models\Task::class => History\TaskHistory::class,
            Models\Lead::class => History\LeadHistory::class,
            Models\ProjectType::class => History\ProjectTypeHistory::class,
            Models\ResultType::class => History\ResultTypeHistory::class,

            Models\UserSetting::class => History\UserSettingHistory::class
        ]);
    }
}
