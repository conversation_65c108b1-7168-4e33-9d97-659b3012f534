<div class="section m-company-settings-section" data-id="settings">
    <div class="section-loading" data-js="loader"></div>
    <div class="m-page-menu" data-js="tab-menu">
        {{#each tab_items}}
            {{#if is_enabled}}
            <div class="c-pm-tab{{#if @first}} t-active{{/if}}" data-js="tab-item" data-id="{{@key}}">
                <svg class="c-pmt-icon"><use xlink:href="#remix-icon--{{icon}}"></use></svg>
                <a class="c-pmt-title">{{title}}</a>
            </div>
            {{/if}}
        {{/each}}
    </div>
    <div class="c-s--pages" data-js="page-container"></div>
</div>