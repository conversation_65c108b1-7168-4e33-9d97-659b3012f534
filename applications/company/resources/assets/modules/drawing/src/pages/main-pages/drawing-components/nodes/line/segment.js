/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/Line/Segment
 */

'use strict';

const Line = require('../line');
const LineUtil = require('../../utils/line');
const Point = require('../../utils/point');
const ConnectorNode = require('../connector');
const LineOpeningNode = require('../line_opening');

const EntityTypes = Line.Entity.Type;

const SnapTypeConfig = new Map([
    [EntityTypes.FOUNDATION, [EntityTypes.FOUNDATION, EntityTypes.INTERIOR_WALL, EntityTypes.BEAM, EntityTypes.FLOOR_JOIST, EntityTypes.BEAM_REPLACEMENT]],
    [EntityTypes.INTERIOR_WALL, [EntityTypes.INTERIOR_WALL, EntityTypes.EXTERIOR_WALL, EntityTypes.FOUNDATION]],
    [EntityTypes.EXTERIOR_WALL, [EntityTypes.EXTERIOR_WALL, EntityTypes.INTERIOR_WALL]],
    [EntityTypes.BEAM, [EntityTypes.BEAM, EntityTypes.FOUNDATION, EntityTypes.FLOOR_JOIST]],
    [EntityTypes.INTERIOR_DRAIN, [EntityTypes.INTERIOR_DRAIN]],
    [EntityTypes.INTERIOR_DRAIN_TILE, [EntityTypes.INTERIOR_DRAIN_TILE]],
    [EntityTypes.EXTERIOR_DRAIN, [EntityTypes.EXTERIOR_DRAIN]],
    [EntityTypes.LINTEL_REPAIR, []],
    [EntityTypes.FENCE, [EntityTypes.FENCE, EntityTypes.EXISTING_FENCE]],
    [EntityTypes.EXISTING_FENCE, [EntityTypes.EXISTING_FENCE, EntityTypes.FENCE]],
    [EntityTypes.FLOOR_JOIST, [EntityTypes.FLOOR_JOIST, EntityTypes.BEAM, EntityTypes.FOUNDATION, EntityTypes.EXTERIOR_WALL, EntityTypes.BEAM_REPLACEMENT]],
    [EntityTypes.LINE_DIMENSION, [EntityTypes.LINE_DIMENSION]],
    [EntityTypes.SPRINKLER_LINE, [EntityTypes.SPRINKLER_LINE]],
    [EntityTypes.RETAINING_WALL, [EntityTypes.RETAINING_WALL]],
    [EntityTypes.UTIILTY_LINE, [EntityTypes.UTIILTY_LINE]],
    [EntityTypes.FRENCH_DRAIN, [EntityTypes.FRENCH_DRAIN]],
    [EntityTypes.VAPOR_BARRIER, [EntityTypes.VAPOR_BARRIER]],
    [EntityTypes.ROOT_BARRIER, [EntityTypes.ROOT_BARRIER]],
    [EntityTypes.JOINT_SEALANT, [EntityTypes.JOINT_SEALANT]],
    [EntityTypes.BEAM_REPLACEMENT, [EntityTypes.BEAM_REPLACEMENT, EntityTypes.FOUNDATION, EntityTypes.FLOOR_JOIST]]
]);

/**
 * @typedef {object} OpeningConfig
 * @property {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.LineOpening} attachment
 * @property {number} distance - distance to from point of line
 * @property {number} half_width
 * @property {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} from
 * @property {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} to
 * @property {number|undefined} prev_part - part index of part left of opening, if undefined no part exists
 * @property {number|undefined} next_part - part index of part right of opening, if undefined no part exists
 * @property {boolean|undefined} invalid - determines if opening is too close to a segment endpoint
 */

/**
 * @typedef {object} PartConfig
 * @property {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} from
 * @property {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} to
 * @property {boolean} first - determines if connected to 'from' point
 * @property {boolean} last - determines if connected to 'to' point
 */

/**
 * Base class for all lines which use mitering
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line
 * @abstract
 */
class Segment extends Line {
    /**
     * Get default state for segment node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            parts: null
        });
        return state;
    };

    /**
     * Get snap config
     *
     * @readonly
     *
     * @returns {Map<number, number[]>}
     */
    static get SnapTypeConfig() {
        return SnapTypeConfig;
    };

    /**
     * Get snap types for segment based on type
     *
     * @returns {number[]}
     */
    get snap_types() {
        return SnapTypeConfig.get(this.type);
    };

    /**
     * Get connector for endpoint
     *
     * @param {string} endpoint
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector}
     */
    getConnector(endpoint) {
        return this.getNodeAttachmentByTag(`connector_${endpoint}`);
    };

    /**
     * Attach connector to specified endpoint
     *
     * @param {string} endpoint
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector} connector
     * @param {boolean} [update=false] - determines if we update the segment endpoint to the connectors position
     * @param {boolean} [notify=true]
     * @returns {boolean}
     */
    attachConnector(endpoint, connector, {update = false, notify = true}) {
        let data;
        if (update) {
            data = {
                [endpoint]: connector.position
            };
            this.update(data, false, false);
            // update opposite connector so it has updated line positioning
            let miter = this.getConnector(this.oppositeEndpoint(endpoint)).miter;
            if (miter.updateLine(this.id, data)) {
                miter.commit();
            }
        }
        this.attachNode(connector, {
            tags: ['connector', `connector_${endpoint}`],
            persist: false
        });
        connector.attachNode(this, {
            data: {endpoint},
            tags: ['miter', 'segment'],
            persist: false
        });
        if (notify) {
            this.emitNodeEvent('update', {data});
            this.emit('update', {data});
        }
        return true;
    };

    /**
     * Detach connector
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector} connector
     * @param {object} [config={}]
     * @returns {{actions: boolean, deleted: boolean, join: ({line: module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment, connector: module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector}|false)}}
     */
    detachConnector(connector, config = {}) {
        let result = connector.detachSegment(this, config);
        this.detachNode(connector);
        return result;
    };

    /**
     * Set connector of endpoint
     *
     * Will detach existing connector and attach new one if there is a change.
     *
     * @param {string} endpoint
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Connector} connector
     * @param {object} [config={}]
     * @returns {{detach: ({join_line: (module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment|null), deleted: boolean}|null), attach: (boolean|null)}}
     */
    setConnector(endpoint, connector, config = {}) {
        let attachment = this.getConnector(endpoint),
            result = {detach: null, attach: null};
        if (attachment !== connector) {
            let delay_render = attachment !== null && connector !== null;
            if (attachment !== null) {
                let detach = Object.assign(config.detach || {}, {
                    render: !delay_render
                });
                result.detach = this.detachConnector(attachment, detach);
            }
            if (connector !== null) {
                result.attach = this.attachConnector(endpoint, connector, config.attach || {});
            }
            if (delay_render && result.detach.actions) {
                if (result.detach.join !== false) {
                    result.detach.join.connector.renderAttachments(true);
                } else if (!result.detach.deleted) {
                    attachment.renderAttachments();
                }
            }
        }
        return result;
    };

    /**
     * Adjust point to sit on line if not at a 90 degree angle
     *
     * Since all points are rounded, they will not work with non 90 degree lines so we adjust them so they will pass
     * various tests.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    getAdjustedPoint(point) {
        let angle = Math.abs(this.vector.angle);
        if (angle === 0 || angle === 90 || angle === 180) {
            return point;
        }
        return Point.of(LineUtil.getClosestPointOnLineSegment(this, point));
    };

    /**
     * Get nearest endpoint based on point
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @param {number} [threshold=0] - determines how close point has to be for us to return endpoint
     * @returns {{endpoint: string, distance: number}}
     */
    getNearestEndpoint(point, threshold = 0) {
        if (threshold === 0) {
            return {
                endpoint: point.equals(this.from) ? 'from' : (point.equals(this.to) ? 'to' : null),
                distance: 0
            };
        }
        let full_distance = this.vector.length,
            half_distance = full_distance / 2,
            point_distance = point.subtract(this.from).length,
            distance,
            endpoint;
        if (point_distance <= half_distance) {
            endpoint = 'from';
            distance = point_distance;
        } else {
            endpoint = 'to';
            distance = full_distance - point_distance;
        }
        if (distance > threshold) {
            endpoint = null;
        }
        return {endpoint, distance};
    };

    /**
     * Split line at a specific point
     *
     * Creates a duplicate line and new connector. Connects this segment and new one to connector and moves attachments
     * over which fall on new line.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     */
    split(point) {
        // create new connector at point
        let connector = this.paper.createNode(EntityTypes.CONNECTOR, {
                position: point
            }, false),
            to_connector = this.getConnector('to');
        to_connector.run_detach_actions = false;

        // connect current to endpoint to new connector
        this.setConnector('to', connector, {
            attach: {
                update: true,
                notify: false
            },
            detach: {
                run_actions: false
            }
        });
        // create a new duplicate line without attachments
        let line = this.duplicate(true, false, false),
            update = {
                from: connector.position,
                to: to_connector.position
            };

        // update line position before setting connectors so they have the proper data to miter
        line.update(update, false, false);
        // connect from endpoint to new connector
        line.setConnector('from', connector);
        // connect to endpoint of new line to current lines to connector
        line.setConnector('to', to_connector);
        to_connector.run_detach_actions = true;

        // move stamps from one line to the other if the now reside on new line
        let attachments = this.getNodeAttachmentsByTag(['stamp', 'opening']);
        if (attachments.length > 0) {
            let distance = point.subtract(this.from).length;
            for (let attachment of attachments) {
                if (attachment.position.subtract(this.from).length <= distance) {
                    continue;
                }
                attachment.setLine(line, false);
            }
        }
        // emit node event after everything is setup so any attachments can updated accordingly
        line.render(true);
        line.emitNodeEvent('update', {data: update});
        line.commit();

        this.render(true);
        this.emitNodeEvent('update');
        this.emit('update');
        this.commit();
        return {
            connector, line
        };
    };

    /**
     * Initialize node
     *
     * Create base connectors
     */
    init() {
        super.init();
        this.attachConnector('from', ConnectorNode.getByPoint(this.paper, this.from, this, true), {
            notify: false
        });
        this.attachConnector('to', ConnectorNode.getByPoint(this.paper, this.to, this, true), {
            notify: false
        });
    };

    /**
     * Perform hit test of node only to determine if point exists within
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {(null|object)}
     */
    hitTest(point) {
        let tolerance = this.paper.hit_test_tolerance,
            result = this.getPaperItem('main').hitTest(point, {
                tolerance,
                fill: true
            });
        if (result === null) {
            return null;
        }
        switch (result.type) {
            case 'fill':
                point = Point.of(LineUtil.getClosestPointOnLine(this, point, true));
                let {endpoint} = this.getNearestEndpoint(point, tolerance);
                if (endpoint !== null) {
                    return {
                        type: 'endpoint',
                        endpoint
                    };
                }
                return {
                    type: 'line'
                };
            case 'segment':
                return {
                    type: 'endpoint',
                    endpoint: result.segment.index < 3 ? 'from' : 'to'
                };
        }
    };

    /**
     * Handle hit test from interaction library
     *
     * We use this to select connectors through a line, due to the fact connectors don't show on the paper.
     *
     * @param {object} result
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    handleDownHitTest(result, event) {
        if (result.type === 'segment' || result.type === 'fill') {
            let endpoint = null;
            if (result.type === 'segment') {
                endpoint = result.segment.index < 3 ? 'from' : 'to';
            } else if (result.type === 'fill') {
                let point = Point.of(LineUtil.getClosestPointOnLine(this, event.point, true)),
                    {endpoint: nearest_endpoint} = this.getNearestEndpoint(point, this.paper.hit_test_tolerance);
                if (nearest_endpoint !== null) {
                    endpoint = nearest_endpoint;
                }
            }
            if (endpoint !== null) {
                let connector = this.getConnector(endpoint);
                if (connector.getNodeAttachmentCount() > 1) {
                    return connector;
                }
            }
        }
        return this;
    };

    /**
     * Attach opening to segment
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.LineOpening} node
     * @param {boolean} [render=true]
     */
    attachOpening(node, render = true) {
        this.attachNode(node, {
            tags: ['opening']
        });
        this.clearPartsCache();
        if (render) {
            this.render(true);
        }
    };

    /**
     * Detach opening to segment
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.LineOpening} node
     * @param {boolean} [render=true]
     */
    detachOpening(node, render = true) {
        this.detachNode(node);
        this.clearPartsCache();
        if (render) {
            this.render(true);
        }
    };

    /**
     * Determine if point is contained within line
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {boolean}
     */
    contains(point) {
        return this.getPaperItem('main').contains(point);
    };

    /**
     * Build opening and part list for line based on attached line opening nodes
     *
     * @param {array|null} [ignore_openings=null] - list of openings to ignore
     * @returns {{parts: PartConfig[], openings: OpeningConfig[], opening_idx: number[]}}
     */
    buildParts(ignore_openings = null) {
        let attachments = this.getNodeAttachmentsByTag('opening'),
            openings = [],
            vector = this.vector,
            size = vector.length;
        if (attachments.length > 0) {
            let from = this.from;
            for (let attachment of attachments) {
                if (ignore_openings !== null && ignore_openings.indexOf(attachment.id) !== -1) {
                    continue;
                }
                let distance = attachment.position.subtract(from).length,
                    half_width = attachment.width / 2;
                openings.push({
                    attachment,
                    distance,
                    half_width,
                    from: from.add(vector.normalize(distance - half_width)),
                    to: from.add(vector.normalize(distance + half_width))
                });
            }
        }
        if (openings.length === 0) {
            return {
                parts: [{
                    from: this.from,
                    to: this.to,
                    vector: this.vector,
                    size: this.vector.length,
                    first: true,
                    last: true
                }],
                openings: [],
                opening_idx: {}
            };
        }

        openings.sort((a, b) => a.distance - b.distance);

        let parts = [],
            opening_idx = {},
            padding = this.half_width;
        for (let i = 0; i < openings.length; i++) {
            let prev = openings[i - 1],
                curr = openings[i],
                next = openings[i + 1];
            opening_idx[curr.attachment.id] = i;
            // if no opening exists before this one (mean it's the first) and the opening leaves space a beginning of
            // segment, then we add it
            if (prev === undefined && curr.distance > curr.half_width) {
                parts.push({
                    from: this.from,
                    to: curr.from,
                    first: true,
                    last: false
                });
                curr.prev_part = parts.length - 1;
            }

            let last_opening_has_space = next === undefined && curr.distance < size - curr.half_width,
                next_opening_has_space = next !== undefined && (next.distance - curr.distance) > (curr.half_width + next.half_width);
            // add part only if next opening doesn't exist and there is room for a part or next opening does exist and
            // it isn't right next to the current
            if (last_opening_has_space || next_opening_has_space) {
                let part = {
                    from: curr.to,
                    to: next === undefined ? this.to : next.from,
                    first: false,
                    last: next === undefined
                };
                parts.push(part);
                curr.next_part = parts.length - 1;
                if (next !== undefined) {
                    next.prev_part = curr.next_part;
                }
            }

            // determine if opening is invalid by being too close to an endpoint, needed for detaching openings if
            // line endpoints are moved
            if (
                curr.distance < (curr.half_width + padding) ||
                curr.distance > (size - (curr.half_width + padding))
            ) {
                curr.invalid = true;
            }
        }

        return {
            parts: parts.map(part => {
                part.vector = part.to.subtract(part.from);
                part.size = part.vector.length;
                return part;
            }),
            openings,
            opening_idx
        };
    };

    /**
     * Clear internal parts cache
     */
    clearPartsCache() {
        this.state.parts = null;
    };

    /**
     * Get and cache parts for line
     *
     * @returns {{parts: PartConfig[], openings: OpeningConfig[], opening_idx: number[]}}
     */
    getParts() {
        if (this.state.parts === null) {
            this.state.parts = this.buildParts();
        }
        return this.state.parts;
    };

    /**
     * Find line part which point lies on
     *
     * @param {PartConfig[]} parts
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {(null|PartConfig)}
     */
    getLinePartForPoint(parts, point) {
        let part = null;
        for (let i = 0; i < parts.length; i++) {
            if (!LineUtil.isPointOnLineSegment(parts[i], point)) {
                continue;
            }
            part = parts[i];
            break;
        }
        return part;
    };

    /**
     * Draw node and register
     */
    draw() {
        let {parts} = this.getParts(),
            part_paths = [],
            half_width = this.half_width,
            vector = this.vector,
            vector_left = vector.clone().rotate(-90).normalize(half_width),
            vector_right = vector.clone().rotate(90).normalize(half_width),
            from_miter = this.getConnector('from').miter.getLinePoints(this.id),
            to_miter = this.getConnector('to').miter.getLinePoints(this.id),
            layer = this.getLayer(this.selected ? 'selected' : 'default');
        for (let part of parts) {
            let from_sides = part.first ? from_miter : {
                    left: part.from.add(vector_left),
                    right: part.from.add(vector_right)
                },
                to_sides = part.last ? to_miter : {
                    left: part.to.add(vector_left),
                    right: part.to.add(vector_right)
                };
            let path = new this.paper.ps.Path({
                segments: [from_sides.right, part.from, from_sides.left, to_sides.left, part.to, to_sides.right],
                closed: true,
                fillColor: this.selected ? this.properties.selected_color : this.state.color,
                parent: layer,
                guide: true // disables hit testing
            });
            part_paths.push(path);
        }
        // main path used for hit testing, is invisible using slight transparency to get around paper.js limitation
        // only hit testing things with some type of non transparent fill
        let item = new this.paper.ps.Path({
            segments: [from_miter.right, this.from, from_miter.left, to_miter.left, this.to, to_miter.right],
            fillColor: 'rgba(0, 0, 0, 0.001)',
            strokeWidth: 0,
            parent: layer
        });
        this.setPaperItem('main', item, true);
        this.setPaperItem('parts', part_paths);

        super.draw();
    };

    /**
     * Normalize data after update
     *
     * @param {object} data
     */
    postUpdate(data) {
        super.postUpdate(data);
        this.clearPartsCache();
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        return {
            from: Point.toArray(this.from),
            to: Point.toArray(this.to),
            width: this.width,
            color: this.color,
            style: this.style
        };
    };

    /**
     * Handle event from attached node
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base} node
     * @param {string} event - event name
     * @param {object} data - event data
     */
    onAttachedNodeEvent(node, event, data) {
        switch (event) {
            case 'update':
                if (node instanceof LineOpeningNode) {
                    this.clearPartsCache();
                    this.render(true);
                }
                break;
            case 'render':
                this.render(true);
                break;
            case 'commit':
                this.commit(false);
                break;
            case 'delete':
                this.detachNode(node);
                this.commit();
                if (node instanceof LineOpeningNode) {
                    this.clearPartsCache();
                    this.render(true);
                }
                break;
        }
    };
}

module.exports = Segment;
