<?php

$this->extend('layout.app');

$asset = $this->asset;
$manifest = $asset->manifest('module:company-profile');

// jquery ui
$asset->style('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css');
$asset->script('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js');
$asset->script('autosize.min');

$manifest->style("css/{$this->domain->brand()->slug}");

$asset->scriptData('fx_pages', [
    'ACCOUNT' => $this->uri->routeRaw('page.app.company.account', ['path' => '/subscription'])->build(),
    'QUICKBOOKS_CONNECT' => $this->uri->routeRaw('quickbooks.oauth.connect')->build(),
    'QUICKBOOKS_DISCONNECT' => $this->uri->routeRaw('quickbooks.oauth.disconnect')->build(),
    'FORM_MARKETPLACE' => $this->uri->routeRaw('page.app.marketplace', ['path' => '/forms'])->build(),
    'FORM_PREVIEW_RENDER_URL' => $this->uri->routeRaw('api.v1.company.form.items.render-preview')->build(),
    'MARKETING' => $this->uri->routeRaw('page.app.marketing')->build(),
    'SETUP_WIZARD' => $this->uri->routeRaw('page.app.setup-wizard', ['path' => ''])->build(),
    'USER_PROFILE' => $this->uri->routeRaw('page.app.user.profile', ['path' => ''])->build()
]);

$asset->scriptData('profile_data', [
    'is_setup_wizard' => $is_setup_wizard,
    'brand_name' => $brand_name,
    'company_name' => $company_name,
    'available_users' => $available_users,
    'today_date_short_email_preview' => $todayDateShortEmailPreview,
    'primary_phone_display' => $primaryPhoneDescription.': '.$primaryPhoneNumber,
    'address' => $address,
    'website' => $website,
    'features' => [
        'media_library' => $featureMediaLibrary,
        'text_messaging' => $featureTextMessaging,
        'marketplace' => $featureMarketplace,
        'product_attributes' => $featureProductAttributes,
        'product_components' => $featureProductComponents,
        'bid_follow_ups' => $featureBidFollowUps,
        'leads_website_form' => $featureLeadsWebsiteForm,
        'wisetack_api' => $feature_wisetack_api,
    ],
    'leads_website_form' => [
        'status' => $lead_website_form_status,
        'uuid' => $lead_website_form_uuid,
        'form_url' => $lead_website_form_url,
        'token' => $lead_website_form_token,
    ],
    'wisetack' => [
        'merchant' => $wisetack_merchant,
        'is_merchant_approved' => $wisetack_merchant ? $wisetack_merchant->isApproved() : false,
    ],
    'user' => [
        'id' => $userID,
        'first_name' => $userFirstName,
        'last_name' => $userLastName,
        'bio' => $userBio,
        'email' => $userEmail,
        'phone_number' => $userPhoneNumber,
        'phone_description' => $userPhoneDescription
    ],
    'google' => [
        'is_enabled' => $google_is_enabled,
        'status' => $google_status
    ],
    'filter_options' => [
        'units' => $filter_options_units,
        'product_categories' => $filter_options_product_categories
    ]
]);

$manifest->script('vendor');
$manifest->script('module');
$manifest->svg('sprite');
