/**
 * @module Tooltip
 */

'use strict';

const tippy = require('tippy.js').default;
const uuid4 = require('uuid/v4');

const objFilter = require('@cac-js/utils/object_filter');


require('remixicon/icons/System/information-line.svg');
require('remixicon/icons/System/error-warning-line.svg');
require('@cac-icon/materials.svg');

const info_tpl = require('@cas-tooltip-tpl/info.hbs');
const warning_tpl = require('@cas-tooltip-tpl/warning.hbs');
const materials_tpl = require('@cas-tooltip-tpl/materials.hbs');

/**
 * @memberof module:Tooltip
 */
class Controller {
    constructor() {
        this.state = {
            content: new Map,
            instances: []
        };
    };

    /**
     * Set content for use by a tooltip
     *
     * @param {string} id - id used in data-tooltip-content-id attribute
     * @param {string} content
     */
    setContent(id, content) {
        this.state.content.set(id, content);
    };

    /**
     * Get configuration from element data attributes
     *
     * Filters and renames data attributes into standard config object.
     *
     * @param {jQuery} element
     * @returns {{}}
     */
    getElementConfig(element) {
        return objFilter(element.data(), {
            type: true,
            trigger: true,
            hideOnClick: 'hide_on_click',
            contentId: 'content_id',
            interactive: true,
            maxWidth: 'max_width'
        });
    };

    /**
     * Get Tippy ready config from internal one
     *
     * @param {object} config
     * @returns {object}
     */
    getTippyConfig(config) {
        return objFilter(config, {
            trigger: true,
            hide_on_click: 'hideOnClick',
            interactive: true,
            max_width: 'maxWidth'
        });
    };

    /**
     * Find and initialize all elements with tooltips (marked with data-tooltip) in container element
     *
     * @param {HTMLElement|jQuery|string} container
     * @param {object} config
     */
    initAll(container = 'document', config = {}) {
        $(container).find('[data-tooltip]').each((i, element) => {
            this.init(element, config);
        });
    };

    /**
     * Initialize tooltips for single element
     *
     * @param {HTMLElement|jQuery|string} element
     * @param {object} [config={}]
     * @param {string|undefined} config.type
     * @param {string|undefined} config.content
     * @param {string|undefined} config.content_id
     * @returns {number}
     */
    init(element, config = {}) {
        let idx = this.state.instances.length,
            $element = $(element),
            content = '';
        config = Object.assign({}, config, this.getElementConfig($element));
        if (typeof config.content !== 'string') {
            if (config.content_id !== undefined) {
                content = this.state.content.get(config.content_id);
            } else {
                content = $element.html();
            }
        } else {
            content = config.content;
        }
        let $new_element;
        switch (config.type) {
            case 'warning':
                $new_element = $(warning_tpl({
                    size: $element.data('size')
                }));
                break;
            case 'materials':
                $new_element = $(materials_tpl({
                    size: $element.data('size')
                }));
                break;
            case 'info':
            default:
                $new_element = $(info_tpl({
                    size: $element.data('size')
                }));
                break;
        }
        $element.replaceWith($new_element);
        $new_element.data('tooltip_idx', idx);
        this.state.instances[idx] = tippy($new_element[0], Object.assign(this.getTippyConfig(config), {
            content
        }));
        return idx;
    };

    /**
     * Create info tooltip element for use in template
     *
     * Will store content and make it available for when the tooltip is eventually initialized by the user.
     *
     * @param {string} content
     * @param {object} config
     * @param {boolean|undefined} config.size - size of icon (must match an available CSS selector for size)
     * @returns {string} - HTML of info icon
     */
    info(content, config = {}) {
        let id = uuid4();
        this.setContent(id, content);
        return info_tpl({
            content_id: id,
            size: config.size
        });
    };
}

module.exports = new Controller;
