<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Acl;
use App\Resources\System\Form\{CategoryItemResource, CategoryResource, ItemResource};
use Core\Components\Console\Commands\BaseCommand;
use Core\Components\Resource\Classes\{Entity, Scope};
use Core\Components\Resource\Interfaces\AclInterface;
use Throwable;

/**
 * Class SystemFormCategoryCommand
 *
 * @package App\Console\Commands
 */
class SystemFormCategoryCommand extends BaseCommand
{
    /**
     * Get category type
     *
     * @return int|null
     */
    protected function getType(): ?int
    {
        $category_type_aliases = [
            'bid' => CategoryResource::TYPE_BID
        ];
        $category_types = [
            CategoryResource::TYPE_BID => 'Bid'
        ];

        $type = $this->args->get('type');
        if ($type !== null) {
            if (isset($category_type_aliases[$type])) {
                $type = $category_type_aliases[$type];
            }
        } elseif ($type === null) {
            $this->console->line('Choose a category type:');
            $this->console->line();
            $type = $this->console->menu($category_types, [
                'cancel' => true,
                'cancel_title' => '[cancel]'
            ]);
            if ($type === false) {
                $this->console->error('Add form category aborted');
                return null;
            }
        }
        return $type;
    }

    /**
     * Get list of existing categories for a type
     *
     * @param CategoryResource $resource
     * @param int $type
     * @return array
     */
    protected function getExistingCategories(CategoryResource $resource, int $type): array
    {
        $categories = [];
        $category_scope = Scope::make()
            ->filter('type', 'eq', $type)
            ->filter('status', 'eq', CategoryResource::STATUS_ACTIVE)
            ->sort('name', 'asc');
        $resource->collection()
            ->scope($category_scope)
            ->run()
            ->each(function ($category) use (&$categories) {
                $categories[$category['id']] = $category['name'];
            });
        return $categories;
    }

    /**
     * Output menu of existing categories to user to select from
     *
     * @param CategoryResource $resource
     * @param int $type
     * @return string|null
     */
    protected function listCategories(CategoryResource $resource, int $type): ?string
    {
        $existing_categories = $this->getExistingCategories($resource, $type);
        if (count($existing_categories) === 0) {
            $this->console->error('No categories exist');
            return null;
        }
        $this->console->line('Choose a form category');
        $this->console->line();
        $category_id = $this->console->menu($existing_categories, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($category_id === false) {
            $this->console->error('Aborted');
            return null;
        }
        if (!$this->console->confirm("You chose '{$existing_categories[$category_id]}', is this correct?")) {
            $this->console->error('Aborted');
            return null;
        }
        return $category_id;
    }

    /**
     * Get form type from category type
     *
     * @param $category_type
     * @return int|null
     */
    protected function getFormType($category_type)
    {
        $form_type = null;
        switch($category_type) {
            case CategoryResource::TYPE_BID:
                $form_type = ItemResource::TYPE_BID;
                break;
        }
        return $form_type;
    }

    /**
     * Output menu of existing form items to user to select from
     *
     * @param ItemResource $resource
     * @param int $type
     * @return string|null
     */
    protected function listForms(ItemResource $resource, int $type): ?string
    {
        $existing_forms = [];
        $form_scope = Scope::make()
            ->filter('type', 'eq', $type)
            ->filter('status', 'eq', $this->getFormType($type))
            ->sort('name', 'asc');
        $resource->collection()
            ->scope($form_scope)
            ->run()
            ->each(function ($form) use (&$existing_forms) {
                $existing_forms[$form['id']] = $form['display_name'] ?? $form['name'];
            });

        if (count($existing_forms) === 0) {
            $this->console->error('No forms exist');
            return null;
        }
        $this->console->line('Choose a form');
        $this->console->line();
        $form_id = $this->console->menu($existing_forms, [
            'cancel' => true,
            'cancel_title' => '[cancel]'
        ]);
        if ($form_id === false) {
            $this->console->error('Aborted');
            return null;
        }
        if (!$this->console->confirm("You chose '{$existing_forms[$form_id]}', is this correct?")) {
            $this->console->error('Aborted');
            return null;
        }
        return $form_id;
    }

    /**
     * Display list of all categories for a type
     */
    public function list(): void
    {
        $type = $this->getType();
        if ($type === null) {
            $this->console->error('Form category type not selected');
            return;
        }

        $this->console->line();
        $this->console->line('All categories:');
        $resource = CategoryResource::make(Acl::make());
        foreach ($this->getExistingCategories($resource, $type) as $name) {
            $this->console->line($name);
        }
    }

    /**
     * Add new category
     *
     * @throws Throwable
     */
    public function add(): void
    {
        $category_type = $this->getType();
        if ($category_type === null) {
            $this->console->error('Form category type not selected');
            return;
        }

        while(true) {
            $category_name = $this->console->get('category-name', 'Category Name');

            $category_resource = CategoryResource::make(Acl::make());
            $parent_category_id = null;
            if ($this->console->confirm('Do you want to add a parent category?')) {
                $existing_categories = $this->getExistingCategories($category_resource, $category_type);
                if (count($existing_categories) === 0) {
                    $this->console->info('No parent categories available');
                    if (!$this->console->confirm('Do you want to continue?')) {
                        $this->console->error('Add form category aborted');
                        return;
                    }
                } else {
                    $this->console->line('Choose a parent category:');
                    $this->console->line();
                    $parent_category_id = $this->console->menu($existing_categories, [
                        'cancel' => true,
                        'cancel_title' => '[cancel]'
                    ]);
                    if ($parent_category_id === false) {
                        $this->console->error('Add form category aborted');
                        return;
                    }
                    if (!$this->console->confirm("You chose '{$existing_categories[$parent_category_id]}' parent category, is this correct?")) {
                        $this->console->error('Add form category aborted');
                        return;
                    }
                }
            }

            try {
                $category_resource->create(Entity::make([
                    'name' => $category_name,
                    'parent_id' => $parent_category_id,
                    'status' => CategoryResource::STATUS_ACTIVE,
                    'type' => $category_type
                ]))->run();
                $this->console->info('Form category successfully added');

                if (!$this->console->confirm("Do you want to add another category?")) {
                    break;
                }
            } catch (Throwable $e) {
                $this->console->error('Unable to add form category');
                throw $e;
            }
        }
        $this->console->info('Done');
    }

    /**
     * Archive category
     *
     * @throws Throwable
     */
    public function archive(): void
    {
        $category_type = $this->getType();
        if ($category_type === null) {
            $this->console->error('Form category type not selected');
            return;
        }

        $category_resource = CategoryResource::make(Acl::make());
        $category_id = $this->listCategories($category_resource, $category_type);

        try {
            $category_resource->partialUpdate(Entity::make([
                'id' => $category_id,
                'status' => CategoryResource::STATUS_ARCHIVED
            ]))->run();
            $this->console->info('Form category successfully archived');
        } catch (Throwable $e) {
            $this->console->error('Unable to archive form category');
            throw $e;
        }
    }

    /**
     * Get category id and form item id from user input
     *
     * @param AclInterface $acl
     * @return array|null
     */
    protected function getCategoryAndFormIds(AclInterface $acl): ?array
    {
        $category_resource = CategoryResource::make($acl);
        $form_resource = ItemResource::make($acl);

        $type = $this->getType();
        if ($type === null) {
            $this->console->error('Form category type not selected');
            return null;
        }
        $category_id = $this->listCategories($category_resource, $type);
        if ($category_id === null) {
            $this->console->error('Form category not selected');
            return null;
        }
        $form_id = $this->listForms($form_resource, $type);
        if ($form_id === null) {
            $this->console->error('Form not selected');
            return null;
        }
        return [$category_id, $form_id];
    }

    /**
     * Attach category to form
     *
     * @throws Throwable
     */
    public function attach(): void
    {
        $acl = Acl::make();
        try {
            if (($ids = $this->getCategoryAndFormIds($acl)) === null) {
                return;
            }
            [$category_id, $form_id] = $ids;
            $entity = Entity::make([
                'category_id' => $category_id,
                'item_id' => $form_id
            ]);
            CategoryItemResource::make($acl)->create($entity)->run();
            $this->console->info('Form successfully attached to category');
        } catch (Throwable $e) {
            $this->console->error('Unable to attach form to category');
            throw $e;
        }
    }

    /**
     * Detach category from form
     *
     * @throws Throwable
     */
    public function detach(): void
    {
        $acl = Acl::make();
        try {
            if (($ids = $this->getCategoryAndFormIds($acl)) === null) {
                return;
            }
            [$category_id, $form_id] = $ids;
            $resource = CategoryItemResource::make($acl);
            $entity_id = $resource->entity()
                ->lookup([
                    'category_id' => $category_id,
                    'item_id' => $form_id
                ])
                ->scope(Scope::make()->field('id'))
                ->run();
            $resource->delete($entity_id)->run();

            $this->console->info('Form successfully detached to category');
        } catch (Throwable $e) {
            $this->console->error('Unable to detach form to category');
            throw $e;
        }
    }
}
