<?php

declare(strict_types=1);

namespace App\Services\Text\Numbers;

use App\Services\Text\Classes\Number;
use Common\Models\TextMessageService;

/**
 * Class FromMessageServiceNumber
 *
 * @package App\Services\Text\Numbers
 */
class FromMessageServiceNumber extends Number
{
    /**
     * Fill in instance from text message service model
     *
     * @param TextMessageService $service
     * @return $this
     */
    public function fromModel(TextMessageService $service): self
    {
        $this->type(static::TYPE_FROM_MESSAGE_SERVICE);
        $this->itemID($service->getUuidKey());

        return $this;
    }
}
