<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API\Integrations;

use App\Services\CompanySettingService;
use App\Services\Quickbooks\Classes\Query;
use Core\Exceptions\AppException;
use App\Services\Quickbooks\Resources\{CustomerResource, InvoiceResource, ItemResource};
use App\Services\QuickbooksService;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use QuickBooksOnline\API\Data\IPPPhysicalAddress;
use Throwable;

/**
 * Class QuickbooksController
 *
 * @package App\Http\Controllers\App\API\Integrations
 */
class QuickbooksController
{
    /**
     * Get list of all items in Quickbooks account
     *
     * Port of legacy ajax file getQuickbooksSettings.php
     *
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function settings(): \Core\Components\Http\Responses\JSONResponse
    {
        $data = ['status' => 1];
        try {
            $user = Auth::user();
            if (!$user->primary) {
                throw new AppException('Only primary users can access settings');
            }
            $service = new QuickbooksService($user->companyID);
            $item_resource = new ItemResource($service);
            $items = $item_resource->cursor(function (Query $query) {
                return $query->orderBy('Name', 'asc');
            });
            $list = [];
            foreach ($items as $item) {
                $list[] = [
                    'id' => $item->Id,
                    'name' => $item->Name,
                    'parent_item_id' => $item->ParentRef,
                    'type' => $item->Type
                ];
            }

            $setting_service = new CompanySettingService($user->companyID);
            $data['result'] = [
                'items' => $list,
                'quickbooks_default_service' => $setting_service->get('quickbooks_default_service', null),
                'quickbooks_use_invoice' => $setting_service->get('quickbooks_use_invoice', true),
                'quickbooks_allow_online_credit_card_payment' => $setting_service->get('quickbooks_allow_online_credit_card_payment', false),
                'quickbooks_allow_online_ach_payment' => $setting_service->get('quickbooks_allow_online_ach_payment', false)
            ];
        } catch (Throwable $e) {
            $data['status'] = 2;
        }
        return Response::json($data);
    }

    /**
     * Get list of QB customers for DataTables to display and paginate
     *
     * Port of legacy ajax file getQuickbooksCustomers.php
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function customers(RequestInterface $request): \Core\Components\Http\Responses\JSONResponse
    {
        $start = (int) $request->get('start', 1);
        if ($start < 1) {
            $start = 1;
        }
        $max_results = (int) $request->get('length', 10);
        if ($max_results < 0) {
            $max_results = 10;
        }

        try {
            $user = Auth::user();
            if (!$user->primary) {
                throw new AppException('Only primary users can access customers');
            }
            $service = new QuickbooksService($user->companyID);
            $customer_resource = new CustomerResource($service);
            $count = $customer_resource->count();

            $customers = $customer_resource->query()->paginate($start, $max_results)->run();
        } catch (\Throwable) {
            return Response::json([
                'error' => 'Unable to fetch customers'
            ], 400);
        }

        $data = [];
        foreach ($customers as $customer) {
            $datum = [
                'id' => $customer->Id,
                'name' => $customer->FullyQualifiedName,
                'address' => ''
            ];
            /** @var IPPPhysicalAddress $address */
            $address = $customer->BillAddr;
            if ($address !== null) {
                $datum['address'] = $address->Line1;
                if ($address->Line2 !== null) {
                    $datum['address'] .= " {$address->Line2}";
                }
                $datum['address'] .= " {$address->City}, {$address->CountrySubDivisionCode} {$address->PostalCode}";
            }
            $data[] = $datum;
        }

        return Response::json([
            'data' => $data,
            'recordsTotal' => $count,
            'recordsFiltered' => $count
        ]);
    }

    /**
     * Get customer invoices by ids
     *
     * This is a port of legacy getQuickbooksInvoicesById.php page. Needs to be reworked since this isn't a
     * good approach to the problem.
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws AppException
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\QueryException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function invoices(RequestInterface $request): \Core\Components\Http\Responses\JSONResponse
    {
        $user = Auth::user();
        $ids = $request->get('quickbooksIDs', []);

        $invoices = [];
        $service = new QuickbooksService($user->companyID);
        if ($service->isConnected() && is_array($ids) && count($ids) > 0) {
            $invoice_resource = new InvoiceResource($service);
            $invoices = $invoice_resource->legacyFindByIds($ids);
        }
        return Response::json([
            'invoices' => $invoices
        ]);
    }
}
