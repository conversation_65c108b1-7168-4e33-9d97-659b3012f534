<div class="f-field{{classAttr classes append="y"}}" data-js="field" data-path="{{path}}">
    <label class="f-f-label" for="fx-form-field-{{path}}">{{label}}{{#if internal}} <span class="f-fl-internal">(Internal)</span>{{/if}}<span class="f-fl-required" data-js="required">*</span>{{#if tooltip}}<span data-tooltip data-type="info">{{tooltip}}</span>{{/if}}</label>
    <textarea class="f-f-input" data-js="textarea" id="fx-form-field-{{path}}"></textarea>
    <div class="f-f-errors" data-js="error-container"></div>
</div>
