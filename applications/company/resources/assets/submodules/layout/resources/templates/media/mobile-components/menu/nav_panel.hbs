<div class="c-lmmi-panel m-nav-panel">
    <div class="c-n-wrapper">
    <div class="c-n-wrapper-flex">
        <!--        User info-->
        <div class="c-nw-user">
            <div class="c-nwu-image">
                <svg class="c-nwui-icon"><use xlink:href="#remix-icon--user & faces--user-line"></use></svg>
            </div>
            <div class="c-nwu-info">
                <div class="c-nwui-name">{{user_name}}</div>
                <div class="c-nwui-company">{{company_name}}</div>
            </div>
        </div>
        <!--        Actions-->
            <div class="c-nw-actions" data-js="nav-actions">
            <div class="c-nwa-button-grow">
                <a class="c-nwa-button" data-js="search">
                    <svg class="c-nwas-icon"><use xlink:href="#remix-icon--system--search-line"></use></svg>
                </a>
            </div>

            <div class="c-nwa-button-grow">
                <a class="c-nwa-button" href="/">
                    <svg class="c-nwas-icon"><use xlink:href="#remix-icon--business--calendar-line"></use></svg>
                </a>
            </div>

            <div class="c-nwa-button-grow">
                <a class="c-nwa-button" id="mobile-notification-center-button" data-js="notification-center-button">
                    <svg class="c-nwas-icon"><use xlink:href="#remix-icon--media--notification-2-line"></use></svg>
                      <span class="notification-badge" id="mobile-notification-count"></span>
                </a>
            </div>
        </div>
    </div>

        <!--        Training & setup buttons, Page links, and Quick links container -->
        <div class="c-n-navigation-container">
            <!--        Training & setup buttons-->
            {{#if buttons_nav}}
                <div class="c-nw-buttons" data-js="nav-buttons">
                    {{#each buttons}}
                        <a class="c-nwb-button t-{{@key}}" href="{{url}}">{{title}}</a>
                    {{/each}}
                </div>
            {{/if}}
            <!--        Page links-->
            <div class="c-nw-nav" data-js="nav">
                {{#each nav}}
                    <div class="c-nwn-container{{#if class}} {{class}}{{/if}}">
                        <a class="c-nwnc-item{{#if active}} t-active{{/if}}"{{#if menu}} data-js="menu-trigger"{{else}} href="{{url}}"{{/if}}>
                            <div class="c-nwnci-text">{{title}}</div>
                            {{#if menu}}
                                <svg class="c-nwnci-arrow"><use xlink:href="#remix-icon--arrows--arrow-down-s-line"></use></svg>
                            {{/if}}
                        </a>
                        {{#if menu}}
                            <div class="c-nwn-menu" data-js="menu">
                                {{#each menu}}
                                    <a class="c-nwnm-item{{#if active}} t-active{{/if}}" href="{{url}}"><div class="c-nwnmi-text">{{title}}</div></a>
                                {{/each}}
                            </div>
                        {{/if}}
                    </div>
                {{/each}}
            </div>
            <!--        Quick links-->
            {{#if quick_links}}
                <div class="c-nw-quick-links">
                    {{#each quick_links}}
                        <a class="c-nwq-link"{{#if url}} href="{{url}}"{{/if}}{{buildAttrs attrs}}>
                            <svg class="c-nwql-icon"><use xlink:href="#{{icon}}"></use></svg>
                            <div class="c-nwql-title">{{title}}</div>
                        </a>
                    {{/each}}
                    {{#if menu_1}}
                        {{#each menu_1}}
                            <a class="c-nwq-link" href="{{url}}">
                                <svg class="c-nwql-icon"><use xlink:href="#{{icon}}"></use></svg>
                                <div class="c-nwql-title">{{title}}</div>
                            </a>
                        {{/each}}
                    {{/if}}
                </div>
            {{/if}}
        </div>

<!--    Logout-->
    {{#if menu_2}}
        <div class="c-n-logout">
            {{#each menu_2}}
                <a class="c-nl-link" href="{{url}}">
                    <svg class="c-nll-icon"><use xlink:href="#{{icon}}"></use></svg>
                    <div class="c-nll-title">{{title}}</div>
                </a>
            {{/each}}
        </div>
    {{/if}}

<!--    New button-->
    {{#if new_button_menu}}
        <a class="c-n-new-button" data-js="new-button">
            <svg class="c-nb-icon"><use xlink:href="#remix-icon--system--add-line"></use></svg>
        </a>
        <div class="c-n-new-button-menu" data-js="new-button-menu">
            <a class="c-nm-close" data-js="close"></a>
            <div class="c-nm-container">
                {{#each new_button_menu}}
                    <a class="c-nmc-item" href="{{url}}">
                        <svg class="c-nmci-icon"><use xlink:href="#{{icon}}"></use></svg>
                        <div class="c-nmci-text">{{title}}</div>
                    </a>
                {{/each}}
            </div>
        </div>
    {{/if}}
</div>