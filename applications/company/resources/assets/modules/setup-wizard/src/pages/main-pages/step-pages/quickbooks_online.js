'use strict';

import Page from '@ca-package/router/src/page';
import {find<PERSON>hild, jsSelector, on<PERSON><PERSON><PERSON><PERSON><PERSON>, onClickWatcher} from "@ca-package/dom";

import FormInput from '@ca-submodule/form-input';
FormInput.use(require('@ca-submodule/form-input/src/switch'));

import FormValidator from "@cas-validator-js";

import quickbooks_online_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/quickbooks_online.hbs';
import status_connected_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/quickbooks-online/status_connected.hbs';
import status_disconnected_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/quickbooks-online/status_disconnected.hbs';

/**
 * @memberof module:SetupWizard/Pages/MainPages/StepPages
 */
export class QuickbooksOnline extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            previous: {
                route: 'step.warranty_packet',
                step: parent.steps.WARRANTY_PACKET
            },
            next: {
                route: 'step.google_calendar',
                step: parent.steps.GOOGLE_CALENDAR
            },
            setup_quickbooks_online: false,
            setup_quickbooks_online_completed_at: null,
            qb_loaded: false,
            service_options: [],
            unload: false
        });
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.state.parent.getPageContainer().scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Change skip to next
     */
    changeSkipToNext() {
        this.state.parent.setSkipRoute(null);
        this.state.parent.setNextRoute(this.state.next.route);
        this.state.parent.changeNavState();
    };

    /**
     * Save current step for company
     *
     * @returns {Promise}
     */
    async saveCompanyStep(type) {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: type.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(type.route);
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Skip current step for company
     *
     * @returns {Promise}
     */
    async skipStep() {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/skip`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: this.state.next.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(this.state.next.route);
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Save data for step
     *
     * @returns {Promise}
     */
    async save() {
        this.state.parent.startWorking();

        let data = {
            quickbooks_default_service: parseInt(this.elem.default_service.val()),
            quickbooks_use_invoice: this.elem.use_quickbooks_invoice.is(':checked') ?? false,
            quickbooks_allow_online_credit_card_payment: this.elem.allow_credit_card.is(':checked') ?? false,
            quickbooks_allow_online_ach_payment: this.elem.allow_ach.is(':checked') ?? false,
        };

        try {
            let response = await $.ajax({
                url: window.fx_url.API + `setup-wizard/quickbooks-online`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(data)
            });
            this.state.setup_quickbooks_online_completed_at = response.quickbooksOnlineCompletedAt;
            this.state.parent.resetWorking();
            if (!this.state.unload) {
                this.router.navigate(this.state.next.route);
            }
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save data, please try again');
        }
    };

    /**
     * Determine if step needs to be saved or skipped
     */
    handleNextClick() {
        if (this.state.setup_quickbooks_online) {
            this.elem.form.trigger('submit');
        } else {
            this.skipStep();
        }
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populateServices(data) {
        // append Service options to the select dropdown
        for (let item of data.items) {
            let service_option = $('<option/>')
                .attr('value', item.id)
                .text(item.name);
            this.state.service_options.push(service_option);
        }
        this.elem.default_service.append(this.state.service_options);

        // fill inputs with QuickBooks settings
        if (data.quickbooks_default_service !== null) {
            this.elem.default_service.val(data.quickbooks_default_service);
        }
        this.elem.use_quickbooks_invoice.attr('checked', data.quickbooks_use_invoice);
        this.elem.allow_credit_card.attr('checked', data.quickbooks_allow_online_credit_card_payment);
        this.elem.allow_ach.attr('checked', data.quickbooks_allow_online_ach_payment);
    };

    /**
     * Fetch available QuickBooks services list
     *
     * @returns {Promise}
     */
    async fetchServices() {
        return new Promise((resolve) => {
            $.getJSON(`${window.fx_url.API}integration/quickbooks/settings`)
                .done(resolve);
        });
    };

    /**
     * Load available QuickBooks services list
     *
     * @returns {Promise}
     */
    async loadServices() {
        try {
            let data = await this.fetchServices();
            if (data.status === 2) {
                this.setError('Unable to load QuickBooks data, please contact support');
                return;
            }
            this.populateServices(data.result);
        } catch(e) {
            this.setError('Unable to load QuickBooks Services list');
        }
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    async populateQuickbooks(data) {
        switch(data.qb_connection_status) {
            case true:
                // company is connected to QB
                this.state.setup_quickbooks_online = true;
                this.changeSkipToNext();
                this.elem.instructions.html(`${window.setup_wizard_data.brand_name} is now able to talk with Quickbooks Online. Please review and adjust the settings below to tell the software how to work with Quickbooks, then click next. <a href="https://cxlratr.to/hc-quickbooks" target="_blank">Learn More</a>`);
                this.elem.status.addClass('t-show');
                let elem = $(status_connected_tpl({
                    link: window.fx_pages.QUICKBOOKS_DISCONNECT,
                }));
                this.elem.form = findChild(elem, jsSelector('form'));
                this.elem.default_service = findChild(elem, jsSelector('default_service'));
                this.elem.use_quickbooks_invoice = findChild(elem, jsSelector('use_quickbooks_invoice'));
                this.elem.allow_credit_card = findChild(elem, jsSelector('allow_credit_card'));
                this.elem.allow_ach = findChild(elem, jsSelector('allow_ach'));
                this.elem.content.append(elem);

                this.state.use_quickbooks_invoice = FormInput.init(this.elem.use_quickbooks_invoice);
                this.state.allow_credit_card = FormInput.init(this.elem.allow_credit_card);
                this.state.allow_ach = FormInput.init(this.elem.allow_ach);

                await this.loadServices();

                this.state.validator = FormValidator.init(this.elem.form)
                    .on('form:submit', () => {
                        this.save();
                        return false;
                    })
                    .on('form:validate', () => {
                        this.clearError();
                        return false;
                    })
                    .on('form:error', () => {
                        this.setError('Please review form errors below');
                        return false;
                    });
                break;
            case false:
                // company isn't connected to QB
                this.elem.content.append(status_disconnected_tpl({
                    link: window.fx_pages.QUICKBOOKS_CONNECT
                }));
                break;
        }
    };

    /**
     * Fetch quickbooks data from server
     */
    async fetchQuickbooksData() {
        try {
            let response = await $.ajax({
                url: window.fx_url.API + 'company/profile/integrations',
                dataType: "json",
                type: "GET",
                contentType: "application/x-www-form-urlencoded"
            });
            return response.result;
        } catch (e) {
            console.log(e);
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.parent.scrollTopParent();
        this.state.parent.setBackRoute(this.state.previous.route);
        this.state.parent.setSkipRoute(this.state.next.route);
        this.state.parent.setNextRoute(null);

        if (!this.state.qb_loaded) {
            this.state.parent.startWorking();
            let data = await this.fetchQuickbooksData();
            await this.populateQuickbooks(data);
            this.state.qb_loaded = true;
            this.state.parent.resetWorking();
        }
        await super.load(request, next);
        if (request.data.quickbooks_online) {
            this.state.setup_quickbooks_online = true;
            this.changeSkipToNext();
        }
        if (request.data.quickbooks_online_completed_at !== null) {
            this.state.setup_quickbooks_online_completed_at = request.data.quickbooks_online_completed_at;
        }

        let that = this;
        onClickWatcher(this.state.parent.getMainHeader(), jsSelector('button'), function () {
            let $this = $(this);
            if ($this.data('value') === 'next' || $this.data('value') === 'skip') {
                that.handleNextClick();
                return false;
            }
            if ($this.data('value') === 'back') {
                that.saveCompanyStep(that.state.previous);
                return false;
            }
        }, false);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.parent.setSkipRoute(null);
        onClickDestroy(this.state.parent.getMainHeader());

        if (this.state.setup_quickbooks_online && this.state.setup_quickbooks_online_completed_at === null) {
            this.state.unload = true;
            this.elem.form.trigger('submit');
        }
        this.state.unload = false;
        request.data.quickbooks_online = this.state.setup_quickbooks_online;
        request.data.quickbooks_online_completed_at = this.state.setup_quickbooks_online_completed_at;

        this.state.parent.updateSetupData(request.data);
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.content = findChild(root, jsSelector('content'));
        this.elem.status = findChild(root, jsSelector('status'));
        this.elem.instructions = findChild(root, jsSelector('instructions'));
    };

    /**
     * Render page
     */
    render() {
        return quickbooks_online_tpl({
            brand_name: window.setup_wizard_data.brand_name
        });
    };
}
