<?php

namespace Common\Models\Common;

use App\Classes\Email;
use Common\Models\Company;
use Common\Models\CompanyIntake;
use Common\Models\CompanyIntakeFeature;
use Common\Models\CompanyInvoiceCredit;
use Common\Models\CompanyPaymentMethod;
use Common\Models\CompanyPhone;
use Common\Models\CompanySetup;
use Common\Models\CompanySubscription;
use Common\Models\Customer;
use Common\Models\EmailTemplate;
use Common\Models\Feature;
use Common\Models\File;
use Common\Models\Lead;
use Common\Models\ProjectType;
use Common\Models\MarketingType;
use Common\Models\Pivots\CompanyFeature;
use Common\Models\Pivots\CompanyTrainingSectionModule;
use Common\Models\ProductCategory;
use Common\Models\ProductItem;
use Common\Models\ProjectCostCategory;
use Common\Models\ProjectCostType;
use Common\Models\ProjectTypeLegacy;
use Common\Models\Registration;
use Common\Models\Reseller;
use Common\Models\ResultType;
use Common\Models\SmtpCredential;
use Common\Models\Subscription;
use Common\Models\SuccessManager;
use Common\Models\Timezone;
use Common\Models\TrainingSectionModule;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;

trait CompanyCommon
{
    use ScopeSearchTrait;

    protected $castMap = [
        'companyID' => 'int',
        'resellerID' => 'int',
        'successManagerID' => 'int',
        'subscriptionID' => 'int',
        'isSubscriptionLocked' => 'bool',
        'status' => 'int',
        'signupStatus' => 'int',
        'isSetupWizard' => 'bool',
        'setupWizardStep' => 'int',
        'daylightSavings' => 'bool',
        'scheduleEmailSendSales' => 'bool',
        'emailSalesAppointmentReminderSendFromSales' => 'bool',
        'bidAcceptEmailSendSales' => 'bool',
        'bidRejectEmailSendSales' => 'bool',
        'bidEmailSendSales' => 'bool',
        'timezoneID' => 'int',
        'isActive' => 'bool',
        'smtpCredentialID' => 'int',
        'zendeskOrganizationID' => 'int'
    ];
    protected $dateMap = [
        'trialAt', 'firstActiveAt', 'activeAt', 'suspendedAt', 'dormantAt', 'trialExpiresAt', 'setupWizardCompletedAt'
    ];
    protected $searchColumns = [
        'name', 'address', 'address2', 'city', 'state', 'zip', 'billingAddress', 'billingAddress2', 'billingCity',
        'billingState', 'billingZip', 'website'
    ];

    public function customers()
    {
        return $this->hasMany(Customer::class, 'companyID');
    }

    public function currentSubscription()
    {
        return $this->hasOne(CompanySubscription::class, 'companyID')->where('isCurrent', 1);
    }

    public function defaultPaymentMethod()
    {
        return $this->hasOne(CompanyPaymentMethod::class, 'companyID')
            ->where('isDefault', 1);
    }

    public function emailTemplates()
    {
        return $this->morphMany(EmailTemplate::class, 'emailTemplates', 'ownerType', 'ownerID');
    }

    public function features()
    {
        return $this->belongsToMany(Feature::class, 'companiesFeatures', 'companyID', 'featureID')
            ->using(CompanyFeature::class)
            ->withPivot('status')
            ->withTimestamps();
    }

    public function intake()
    {
        return $this->hasOne(CompanyIntake::class, 'companyID');
    }

    public function intakeFeatures()
    {
        return $this->hasMany(CompanyIntakeFeature::class, 'companyID');
    }

    public function invoiceCredits()
    {
        return $this->hasMany(CompanyInvoiceCredit::class, 'companyID');
    }

    public function leads()
    {
        return $this->hasMany(Lead::class, 'companyID');
    }

    public function projectTypes()
    {
        return $this->hasMany(ProjectType::class, 'companyID');
    }

    public function resultTypes()
    {
        return $this->hasMany(ResultType::class, 'companyID');
    }

    public function logo()
    {
        return $this->belongsTo(File::class, 'logoFileID', 'fileID');
    }

    public function marketingTypes()
    {
        return $this->hasMany(MarketingType::class, 'companyID');
    }

    public function paymentMethods()
    {
        return $this->hasMany(CompanyPaymentMethod::class, 'companyID');
    }

    public function phones()
    {
        return $this->hasMany(CompanyPhone::class, 'companyID');
    }

    public function primaryPhone()
    {
        return $this->hasOne(CompanyPhone::class, 'companyID')->where('isPrimary', true)->limit(1);
    }

    public function products()
    {
        return $this->morphMany(ProductItem::class, 'products', 'ownerType', 'ownerID');
    }

    public function productCategories()
    {
        return $this->morphMany(ProductCategory::class, 'productCategories', 'ownerType', 'ownerID');
    }

    public function projectCostCategories()
    {
        return $this->hasMany(projectCostCategory::class, 'companyID');
    }

    public function projectCostTypes()
    {
        return $this->hasMany(ProjectCostType::class, 'companyID');
    }

    public function registration()
    {
        return $this->belongsTo(Registration::class, 'registrationID', 'registrationID');
    }

    public function reseller()
    {
        return $this->belongsTo(Reseller::class, 'resellerID', 'resellerID');
    }

    public function setup()
    {
        return $this->hasOne(CompanySetup::class, 'companyID');
    }

    public function smtpCredential()
    {
        return $this->belongsTo(SmtpCredential::class, 'smtpCredentialID', 'smtpCredentialID');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscriptionID', 'subscriptionID');
    }

    public function subscriptions()
    {
        return $this->hasMany(CompanySubscription::class, 'companyID');
    }

    public function successManager()
    {
        return $this->belongsTo(SuccessManager::class, 'successManagerID', 'successManagerID');
    }

    public function trainingSectionModules()
    {
        return $this->belongsToMany(TrainingSectionModule::class, 'companiesTrainingSectionModules', 'companyID', 'trainingSectionModuleID')
            ->using(CompanyTrainingSectionModule::class)
            ->withPivot('isCompleted')
            ->withTimestamps();
    }

    public function users()
    {
        return $this->hasMany(User::class, 'companyID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     * @todo rename to timezone when timezone column is removed from company table
     */
    public function _timezone()
    {
        return $this->belongsTo(Timezone::class, 'timezoneID', 'timezoneID');
    }

    public function scopeWithPrimaryDomain($query)
    {
        $query->addSelect('domain_d.domain as primary_domain');
        $query->join('resellersDomains as reseller_domain_d', function ($query) {
            $query->on('reseller_domain_d.resellerID', '=', "companies.resellerID")
                ->where('reseller_domain_d.isResellerPrimaryDomain', 1);
        });
        $query->join('domains as domain_d', 'domain_d.domainID', '=', 'reseller_domain_d.domainID');
        return $query;
    }

    public function scopeWithTimezone($query)
    {
        return $query->join('timezones', 'timezones.timezoneID', '=', 'companies.timezoneID')
            ->addSelect('timezones.timezone');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where('companies.companyID', $company);
    }

    /**
     * Get the company's button colors
     *
     * @return array
     */
    public function getButtonColors(): array
    {
        $default_color = '#005ad0';
        $default_hover_color = '#006EFF';
        $hex_to_rgb_format = "#%02x%02x%02x";
        $company_color_button_color = '#FFFFFF';
        $company_color_hover_button_color = '#FFFFFF';

        $company_color = $this->color ?? $default_color;
        $company_hover_color = $this->colorHover ?? $default_hover_color;

       try {
           // Convert hex to RGB and calculate luminance for companyColor
           list($r1, $g1, $b1) = sscanf($company_color, $hex_to_rgb_format);

           if (($r1 * 0.299 + $g1 * 0.587 + $b1 * 0.114) > 186) {
               $company_color_button_color = '#000000'; // Set to black if luminance is high
           }

           // Convert hex to RGB and calculate luminance for companyColorHover
           list($r2, $g2, $b2) = sscanf($company_hover_color, "#%02x%02x%02x");
           if (($r2 * 0.299 + $g2 * 0.587 + $b2 * 0.114) > 186) {
               $company_color_hover_button_color = '#000000'; // Set to black if luminance is high
           }
       } catch (\Exception $e) {}

        return [
            'button_background_color' => $company_color,
            'button_text_color' => $company_color_button_color,
            'hover_background_color' => $company_hover_color,
            'hover_text_color' => $company_color_hover_button_color,
        ];
    }
}
