<?php

declare(strict_types=1);

namespace App\Exceptions;

use Core\Exceptions\AppException;

/**
 * Class MailingListException
 *
 * @package App\Exceptions
 */
class MailingListException extends AppException
{
    /**
     * @var null|array
     */
    protected $request = null;

    /**
     * @var null|array
     */
    protected $response = null;

    /**
     * Set request
     *
     * @param array $request
     * @return MailingListException
     */
    public function setRequest(array $request): self
    {
        $this->request = $request;
        return $this;
    }

    /**
     * Get request
     *
     * @return array|null
     */
    public function getRequest(): ?array
    {
        return $this->request;
    }

    /**
     * Set response
     *
     * @param array $response
     * @return MailingListException
     */
    public function setResponse(array $response): self
    {
        $this->response = $response;
        return $this;
    }

    /**
     * Get response
     *
     * @return array|null
     */
    public function getResponse(): ?array
    {
        return $this->response;
    }

    /**
     * Get context for exception logging
     *
     * @return array
     */
    public function getContext()
    {
        return [
            'request' => $this->getRequest(),
            'response' => $this->getResponse()
        ];
    }
}
