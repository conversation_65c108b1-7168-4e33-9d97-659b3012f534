<?php

namespace App\Classes\FX\API;

use App\Classes\Acl;
use App\Resources\Bid\ItemResource;
use App\Resources\Project\FileResource;
use Common\Models\EvaluationBid;
use Common\Models\EvaluationPhoto;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Scope;
use Core\StaticAccessors\Config;
use PDO;
use SplFileInfo;

class GetProjectData extends Base
{
    private $projectID;
    private $companyID;
    private $firstName;
    private $lastName;
    private $bidFirstSent;
    private $evaluationID;
    private $results;

    public function setProjectID($projectID)
    {
        $this->projectID = $projectID;
    }

    public function clean($string)
    {
        $string = str_replace(' ', '', $string); // Replaces all spaces
        $string = preg_replace('/[^A-Za-z\-]/', '', $string); // Removes special chars and numbers

        return preg_replace('/-+/', '', $string); // Replaces multiple hyphens with single one.
    }

    public function getProjectData()
    {
        if (!$this->validateToken()) {
            $this->results = ['message' => 'No results'];
            return;
        }
        $st = $this->db->prepare("SELECT customer.companyID, customer.customerID, customer.firstName, customer.lastName, project.projectID, project.projectDescription, CONCAT_WS(' ', customer.firstName, customer.lastName) AS customerName, property.address, 
          property.address2, property.city, property.state, property.zip, property.latitude, property.longitude, projectSchedule.scheduledUserID, projectSchedule.scheduledStart, projectSchedule.scheduledEnd, DATE(projectSchedule.completedAt) AS installationComplete
        
              FROM project 
        
              JOIN property ON property.propertyID = project.propertyID
              JOIN customer ON customer.customerID = property.customerID
              JOIN projectSchedule ON projectSchedule.projectID = project.projectID

              WHERE project.projectID = :projectID AND project.deletedAt IS NULL AND projectSchedule.scheduleType = 'installation' AND projectSchedule.deletedAt IS NULL AND project.projectCancelled IS NULL AND customer.companyID = :companyID LIMIT 1");
        //write parameter query to avoid sql injections
        $st->bindParam(":projectID", $this->projectID);
        $st->bindValue(":companyID", $this->tokenUser->companyID);

        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {

                $this->companyID = $row['companyID'];

                $customerID = $row['customerID'];
                $this->firstName = $this->clean($row['firstName']);
                $this->lastName = $this->clean($row['lastName']);
                $phoneNumbers = $this->getPhoneNumbers($customerID);
                $projectID = $row['projectID'];
                $projectDescription = html_entity_decode($row['projectDescription'], ENT_QUOTES);
                $customerName = html_entity_decode($row['customerName'], ENT_QUOTES);
                $address = html_entity_decode($row['address'], ENT_QUOTES);
                $address2 = html_entity_decode($row['address2'], ENT_QUOTES);
                $city = html_entity_decode($row['city'], ENT_QUOTES);
                $state = $row['state'];
                $zip = $row['zip'];
                $latitude = $row['latitude'];
                $longitude = $row['longitude'];
                $scheduledUserID = $row['scheduledUserID'];
                $scheduledStart = $row['scheduledStart'];
                $scheduledEnd = $row['scheduledEnd'];
                $installationComplete = $row['installationComplete'];

                $salesman = $this->getSalesman();
                $salesmanName = html_entity_decode($salesman['salesmanName'] ?? '', ENT_QUOTES);
                $salesmanPhone = $salesman['salesmanPhone'] ?? '';

                $returnProject = array('message' => 'success', 'projectID' => $projectID, 'projectDescription' => $projectDescription, 'customerName' => $customerName, 'address' => $address, 'address2' => $address2, 'city' => $city, 'state' => $state, 'zip' => $zip, 'latitude' => $latitude, 'longitude' => $longitude,
                    'salesmanName' => $salesmanName, 'salesmanPhone' => $salesmanPhone, 'scheduledUserID' => $scheduledUserID, 'scheduledStart' => $scheduledStart,
                    'scheduledEnd' => $scheduledEnd, 'installationComplete' => $installationComplete);

                $returnProject['assignedCrewmen'] = $this->getAssignedCrewmen();
                $returnProject['phoneNumbers'] = $phoneNumbers;
                $returnProject['timecards'] = $this->getTimecards();
                $documents = $this->getDocuments();

                $returnProject['documents'] = $documents;

            }
            $this->results = $returnProject;
        } else {
            $this->results = array('message' => 'No results',);
        }
    }

    public function getSalesmanPhone($salesmanID)
    {
        $results = null;
        $st = $this->db->prepare("SELECT phoneNumber FROM userPhone WHERE userID = :userID AND deletedAt IS NULL");
        $st->bindParam(":userID", $salesmanID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $salesmanPhone = $row['phoneNumber'];
                $results = $salesmanPhone;
            }
            return $results;
        }
    }

    public function getSalesman()
    {
        $results = null;
        $st = $this->db->prepare("SELECT salesmanName, scheduledUserID, bidFirstSent, bidFirstSentByID, evaluationID
          FROM(
            (SELECT projectSchedule.scheduledUserID, CONCAT( user.userFirstName,' ', user.userLastName) AS salesmanName, project.projectID, evaluationBid.bidFirstSent, evaluationBid.bidFirstSentByID, evaluation.evaluationID

            FROM evaluationBid

            JOIN evaluation ON evaluation.evaluationID = evaluationBid.evaluationID AND evaluation.deletedAt IS NULL
            JOIN project ON project.projectID = evaluation.projectID
            JOIN projectSchedule ON projectSchedule.projectID = project.projectID
            JOIN user ON user.userID = projectSchedule.scheduledUserID
            JOIN property ON property.propertyID = project.propertyID
            JOIN customer ON customer.customerID = property.customerID

            WHERE 

            customer.companyID = :companyID AND
            projectSchedule.scheduleType = 'Evaluation' AND
            evaluationBid.bidFirstSent IS NOT NULL AND
            project.projectID = :projectID AND
            project.deletedAt IS NULL)

            UNION ALL
            
            (SELECT projectSchedule.scheduledUserID, CONCAT( user.userFirstName,' ', user.userLastName) AS salesmanName, project.projectID, customBid.bidFirstSent, customBid.bidFirstSentByID, evaluation.evaluationID

            FROM customBid

            JOIN evaluation ON evaluation.evaluationID = customBid.evaluationID AND evaluation.deletedAt IS NULL
            JOIN project ON project.projectID = evaluation.projectID
            JOIN projectSchedule ON projectSchedule.projectID = project.projectID
            JOIN user ON user.userID = projectSchedule.scheduledUserID
            JOIN property ON property.propertyID = project.propertyID
            JOIN customer ON customer.customerID = property.customerID

            WHERE 

            customer.companyID = :companyID AND
            projectSchedule.scheduleType = 'Evaluation' AND
            customBid.bidFirstSent IS NOT NULL AND
            project.projectID = :projectID AND
            project.deletedAt IS NULL)

            ) AS eval");
        $st->bindParam(":companyID", $this->companyID);
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        $data = null;
        $salesmanName = null;
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $salesmanName = $row['salesmanName'];
                $salesmanID = $row['scheduledUserID'];

                $salesmanPhone = $this->getSalesmanPhone($salesmanID);
                $data = array('salesmanName' => $salesmanName, 'salesmanID' => $salesmanID, 'salesmanPhone' => $salesmanPhone);
            }
        }

        if ($salesmanName == NULL) {
            $data = $this->getSalesmanNoEvaluation();
        }

        $results = $data;

        return $results;
    }

    public function getSalesmanNoEvaluation()
    {
        $results = null;
        $st = $this->db->prepare("SELECT salesmanName, bidFirstSent, bidFirstSentByID, evaluationID
          FROM(
            (SELECT CONCAT( user.userFirstName,' ', user.userLastName) AS salesmanName, project.projectID, evaluationBid.bidFirstSent, evaluationBid.bidFirstSentByID, evaluation.evaluationID

            FROM evaluationBid

            JOIN evaluation ON evaluation.evaluationID = evaluationBid.evaluationID AND evaluation.deletedAt IS NULL
            JOIN project ON project.projectID = evaluation.projectID
            JOIN user ON user.userID = evaluationBid.bidFirstSentByID
            JOIN property ON property.propertyID = project.propertyID
            JOIN customer ON customer.customerID = property.customerID

            WHERE 

            customer.companyID = :companyID AND
            evaluationBid.bidFirstSent IS NOT NULL AND
            project.projectID = :projectID AND
            project.deletedAt IS NULL)

            UNION ALL
            
            (SELECT CONCAT( user.userFirstName,' ', user.userLastName) AS salesmanName, project.projectID, customBid.bidFirstSent, customBid.bidFirstSentByID, evaluation.evaluationID

            FROM customBid

            JOIN evaluation ON evaluation.evaluationID = customBid.evaluationID AND evaluation.deletedAt IS NULL
            JOIN project ON project.projectID = evaluation.projectID
            JOIN user ON user.userID = customBid.bidFirstSentByID
            JOIN property ON property.propertyID = project.propertyID
            JOIN customer ON customer.customerID = property.customerID

            WHERE 

            customer.companyID = :companyID AND
            customBid.bidFirstSent IS NOT NULL AND
            project.projectID = :projectID AND
            project.deletedAt IS NULL)

            ) AS eval");
        $st->bindParam(":companyID", $this->companyID);
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        $data = null;
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $salesmanName = $row['salesmanName'];
                $salesmanID = $row['bidFirstSentByID'];

                $salesmanPhone = $this->getSalesmanPhone($salesmanID);
                $data = array('salesmanName' => $salesmanName, 'salesmanID' => $salesmanID, 'salesmanPhone' => $salesmanPhone);
            }
        }

        $results = $data;

        return $results;
    }

    public function getPhoneNumbers($customerID)
    {
        $results = null;
        $st = $this->db->prepare("SELECT phoneDescription, phoneNumber, isPrimary FROM `customerPhone` WHERE customerID = :customerID AND deletedAt IS NULL");
        $st->bindParam(":customerID", $customerID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $phoneNumbers[] = $row;
                $results = $phoneNumbers;
            }
            return $results;
        }
    }

    public function getTimecards()
    {
        $results = null;
        $st = $this->db->prepare("SELECT DISTINCT timecard.timecardDate FROM punchTime
      JOIN timecard ON timecard.timecardDate = punchTime.timecardDate
      WHERE projectID = :projectID");
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $dates[] = $row;
            }

            foreach ($dates as $date) {
                $date = $date['timecardDate'];
                $crewmen = $this->getCrewmen($date);
                $data = array();
                $data['timecardDate'] = $date;
                $data['crewmen'] = $crewmen;
                $results[] = $data;
            }
            return $results;
        }
    }

    public function getAssignedCrewmen()
    {
        $results = null;
        $st = $this->db->prepare("SELECT crewmanID, CONCAT(firstName,' ', lastName) AS crewmanName FROM crewman
      WHERE currentProjectID = :projectID");
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $crewmen[] = $row;
            }

            foreach ($crewmen as $crewman) {
                $crewmanID = $crewman['crewmanID'];
                $crewmanName = html_entity_decode($crewman['crewmanName'], ENT_QUOTES);
                $data[] = array('crewmanID' => $crewmanID, 'crewmanName' => $crewmanName);
            }

            $results = $data;

            return $results;
        }
    }

    public function getCrewmen($timecardDate)
    {
        $results = null;
        $st = $this->db->prepare("SELECT DISTINCT timecard.crewmanID, CONCAT(crewman.firstName,' ', crewman.lastName) AS crewmanName FROM punchTime
        JOIN timecard ON timecard.timecardDate = punchTime.timecardDate
        JOIN crewman ON crewman.crewmanID = timecard.crewmanID

      WHERE timecard.timecardDate = :timecardDate AND punchTime.projectID = :projectID AND timecard.crewmanID = punchTime.crewmanID");
        $st->bindParam(":timecardDate", $timecardDate);
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $crewmen[] = $row;
            }

            foreach ($crewmen as $crewman) {
                $crewmanID = $crewman['crewmanID'];
                $crewmanName = html_entity_decode($crewman['crewmanName'], ENT_QUOTES);
                $punchTimes = $this->getPunchTimes($timecardDate, $crewmanID);
                $data[] = array('crewmanID' => $crewmanID, 'crewmanName' => $crewmanName, 'punchTimes' => $punchTimes);
            }

            $results = $data;

            return $results;
        }
    }

    public function getPunchTimes($timecardDate, $crewmanID)
    {
        $results = null;
        $st = $this->db->prepare("SELECT punchTime.punchTimeID, punchTime.inTime, punchTime.outTime, punchTime.inTimeRecordedDT, 
        punchTime.outTimeRecordedDT, timecard.timecardDate, timecard.crewmanID FROM punchTime
        JOIN timecard ON timecard.timecardDate = punchTime.timecardDate
      WHERE punchTime.timecardDate = :timecardDate AND punchTime.projectID = :projectID AND punchTime.crewmanID = :crewmanID AND timecard.crewmanID = :crewmanID");
        $st->bindParam(":timecardDate", $timecardDate);
        $st->bindParam(":projectID", $this->projectID);
        $st->bindParam("crewmanID", $crewmanID);
        $st->execute();
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $punchTimes[] = $row;
                $punchTimeID = $row['punchTimeID'];
                $inTime = $row['inTime'];
                $outTime = $row['outTime'];
                $inTimeRecordedDT = $row['inTimeRecordedDT'];
                $outTimeRecordedDT = $row['outTimeRecordedDT'];

                $data[] = array('punchTimeID' => $punchTimeID, 'inTime' => $inTime, 'outTime' => $outTime, 'inTimeRecordedDT' => $inTimeRecordedDT, 'outTimeRecordedDT' => $outTimeRecordedDT);
            }

            $results = $data;

            return $results;
        }
    }

    public function getDocuments()
    {
        $st = $this->db->prepare("
                                SELECT 'evaldrawing' as type, evaluation.evaluationID, 'Evaluation Drawing' as description, evaluationDrawing.evaluationDrawing as name, evaluationDrawingDate as lastEdited FROM evaluationDrawing
                                JOIN evaluation ON evaluation.evaluationID = evaluationDrawing.evaluationID
                                JOIN project ON project.projectID = evaluation.projectID
                                WHERE project.projectID = :projectID AND project.deletedAt IS NULL AND evaluation.evaluationCancelled IS NULL");
        $st->bindParam(":projectID", $this->projectID);
        $st->execute();
        $data = [];
        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $description = html_entity_decode($row['description'], ENT_QUOTES);
                $name = $row['name'];
                $lastEdited = $row['lastEdited'];
                $type = $row['type'];
                $evaluationID = $row['evaluationID'];
                $documentPath = $this->buildDocumentLink($type, $this->projectID, $evaluationID, $name);
                $splFileInfo = new SplFileInfo($name);
                $fileType = $splFileInfo->getExtension();
                $data[] = array('documentDescription' => $description, 'documentName' => $name, 'documentPath' => $documentPath, 'fileType' => $fileType, 'lastEdited' => $lastEdited);
            }
        }

        // add all accepted evaluations which aren't related to a new bid
        $bid_photos = EvaluationPhoto::query()
            ->select('evaluation.evaluationID')
            ->selectRaw('COUNT(*) as count')
            ->join('evaluation', 'evaluation.evaluationID', '=', 'evaluationPhoto.evaluationID')
            ->where('evaluation.projectID', $this->projectID)
            ->whereNull('evaluation.evaluationCancelled')
            ->groupBy('evaluation.evaluationID');
        $bids = EvaluationBid::query()
            ->join('evaluation', 'evaluation.evaluationID', '=', 'evaluationBid.evaluationID')
            ->leftJoin(DB::raw("({$bid_photos->toSql()}) as photos"), 'photos.evaluationID', '=', 'evaluation.evaluationID')
            ->mergeBindings($bid_photos->getQuery())
            ->where('evaluation.projectID', $this->projectID)
            ->whereNotNull('evaluationBid.bidAccepted')
            ->whereNull('evaluation.evaluationCancelled')
            ->get([
                'evaluation.evaluationID', 'evaluationBid.bidFirstSent', DB::raw('IFNULL(photos.count, 0) as photoCount')
            ]);
        if (count($bids) > 0) {
            $base_url = Config::get('app.base_url') . '/';
            foreach ($bids as $bid) {
                $data[] = [
                    'documentDescription' => 'Evaluation Report',
                    'documentName' => "Evaluation-Report-{$bid->evaluationID}.pdf",
                    'documentPath' => "{$base_url}api/evaluationReport.php?token={$this->token}&eid={$bid->evaluationID}",
                    'fileType' => 'pdf',
                    'lastEdited' => $bid->bidFirstSent
                ];
                if ($bid->photoCount > 0) {
                    $data[] = [
                        'documentDescription' => 'Evaluation Photos',
                        'documentName' => "Evaluation-Photos-{$bid->evaluationID}.pdf",
                        'documentPath' => "{$base_url}api/evaluationPhotos.php?token={$this->token}&eid={$bid->evaluationID}",
                        'fileType' => 'pdf',
                        'lastEdited' => $bid->bidFirstSent
                    ];
                }
            }
        }

        $acl = Acl::make($this->tokenUser);

        // add scope of work for any accepted bid for this project
        $bid_item_resource = ItemResource::make($acl);
        $bid_item_scope = Scope::make()
            ->fields(['id', 'updated_at'])
            ->filter('project_id', 'eq', $this->projectID)
            ->filter('status', 'eq', ItemResource::STATUS_ACCEPTED);
        $bid_items = $bid_item_resource->collection()->scope($bid_item_scope)->run();
        if (count($bid_items) > 0) {
            $scope_of_work = $bid_item_resource->getMedia()->get('scope_of_work')->getOriginal();
            foreach ($bid_items as $bid_item) {
                $data[] = [
                    'documentDescription' => 'Scope of Work',
                    'documentName' => "scope-of-work-{$bid_item->id}.pdf",
                    'documentPath' => $scope_of_work->getUrl($bid_item->id)->csm()->build(),
                    'fileType' => 'pdf', // hard code type since the file entry doesn't exist yet
                    'lastEdited' => $bid_item->getDate('updated_at')->toDateTimeString()
                ];
            }
        }

        $project_file_resource = FileResource::make($acl);
        $project_file_scope = Scope::make()
            ->fields(['id', 'name', 'updated_at'])
            ->with([
                'file' => [
                    'fields' => ['extension']
                ]
            ])
            ->filter('project_id', 'eq', $this->projectID);
        $project_files = $project_file_resource->collection()->scope($project_file_scope)->run();
        if (count($project_files) > 0) {
            $profile_file_media = $project_file_resource->getMedia()->get('file')->getOriginal();
            foreach ($project_files as $project_file) {
                $data[] = [
                    'documentDescription' => $project_file->name,
                    'documentName' => "project-file-{$project_file->id}.{$project_file->file->extension}",
                    'documentPath' => $profile_file_media->getUrl($project_file->id)->csm()->build(),
                    'fileType' => $project_file->file->extension,
                    'lastEdited' => $project_file->getDate('updated_at')->toDateTimeString()
                ];
            }
        }

        $results = count($data) > 0 ? $data : null;

        return $results;
    }

    /*
    document location using getDocument.php:
    Project Documents - getDocument.php?type=projectdoc&pid={pid}&name={name}
    Evaluation Photos - getDocument.php?type=evalphoto&pid={pid}&eid={eid}&name={name}
    Evaluation Drawings - getDocument.php?type=evaldrawing&pid={pid}&eid={eid}&name={name}
    Evaluation Documents -  getDocument.php?type=evaldoc&pid={pid}&eid={eid}&name={name}
    */
    public function buildDocumentLink($type, $projectID, $evaluationID, $name)
    {
        $link = '';
        $baseURL = Config::get('app.base_url') . '/' . 'api/getDocument.php?';

        $name = rawurlencode($name);

        switch ($type) {
            case 'projectdoc':
                $link = $baseURL . 'type=' . $type . '&pid=' . $projectID . '&name=' . $name . '&token=' . $this->token;
                break;
            case 'evaldrawing':
                $link = $baseURL . 'type=' . $type . '&pid=' . $projectID . '&eid=' . $evaluationID . '&name=' . $name . '&token=' . $this->token;
                break;
            default:
                break;
        }

        return $link;
    }

    public function getResults()
    {
        return $this->results;
    }
}
