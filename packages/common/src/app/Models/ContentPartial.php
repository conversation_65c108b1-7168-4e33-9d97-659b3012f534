<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class ContentPartial extends HistoryEntityModel implements Interfaces\ContentPartialInterface
{
    use Common\ContentPartialCommon;

    protected $table = 'contentPartials';
    protected $primaryKey = 'contentPartialID';
    protected $fillable = [
        'contentPartialID', 'companyID', 'name', 'alias', 'content', 'createdByUser<PERSON>', 'updatedByUser<PERSON>',
        'deletedAt', 'deletedByUser<PERSON>'
    ];
}
