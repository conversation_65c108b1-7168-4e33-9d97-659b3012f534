'use strict';

const Page = require('@ca-package/router/src/page');

const LegacyBilling = require('./legacy/billing');

const billing_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/billing.hbs');

class Billing extends Page {
    /**
     * Populate template with data
     */
    populate() {
        let link = window.fx_url.BASE + `statement.php?cid=${this.state.customer_id}&pid=${this.state.id}`;
        this.elem.statement.prop('href', link);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.id = request.params.project_id;

        this.elem.loader.show();

        await super.load(request, next);
        this.state.customer_id = request.data.customer_id;
        this.populate();

        LegacyBilling.projectBilling({
            id: this.state.id
        });
        this.elem.loader.hide();
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        LegacyBilling.setup({
            loader: this.parent.elem.loader
        });
        this.elem.statement = this.elem.root.fxFind('statement');
    }

    /**
     * Render page
     */
    render() {
        return billing_tpl();
    };
}

module.exports = Billing;
