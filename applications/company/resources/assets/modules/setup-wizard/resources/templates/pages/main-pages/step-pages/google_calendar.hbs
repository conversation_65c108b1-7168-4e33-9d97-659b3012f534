<div class="c-sbcs-step">
    <div class="c-sbcss-header t-integrations">
        <div class="c-sbcssh-error" data-js="error"></div>
        <h4 class="c-sbcssh-title">Google Calendar</h4>
        <div class="c-sbcssh-status" data-js="status">
            <svg data-icon><use xlink:href="#remix-icon--system--checkbox-circle-fill"></use></svg>
        </div>
        <div class="c-sbcssh-status t-negate" data-js="negate-status"></div>
        <p class="c-sbcssh-instruction" data-js="description"></p>
    </div>
    <div data-js="content"></div>
</div>
