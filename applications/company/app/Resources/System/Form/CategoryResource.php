<?php

declare(strict_types=1);

namespace App\Resources\System\Form;

use App\ResourceDelegates\System\Form\CategoryDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\SystemFormCategory;
use Core\Components\Resource\Classes\{Collection, Entity, Resource};

/**
 * Class CategoryResource
 *
 * @package App\Resources\System\Form
 */
class CategoryResource extends Resource
{
    use UserActionTrackingTrait;

    public const TYPE_BID = 1;

    public const STATUS_ACTIVE = 1;
    public const STATUS_ARCHIVED = 2;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    /**
     * @var string Table name
     */
    protected $table = 'systemFormCategories';

    /**
     * @var string Model associated with resource
     */
    protected $model = SystemFormCategory::class;

    /**
     * @var bool Determines if id is automatically generated
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Get available types
     *
     * @return int[]
     */
    public static function getTypes()
    {
        return [
            static::TYPE_BID
        ];
    }

    /**
     * Get mapping of model type to resource type
     *
     * @return int[]
     */
    public static function getTypeMap()
    {
        return [
            SystemFormCategory::TYPE_BID => self::TYPE_BID
        ];
    }

    /**
     * Get available statuses
     *
     * @return int[]
     */
    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    /**
     * Boot resource
     */
    protected static function boot()
    {
        static::delegate(CategoryDelegate::class);
    }

    /**
     * Get list containing all categories and their children's id's along with the top-level (root) categories needs to
     * recursively build a tree
     *
     * @param Collection $collection
     * @return Collection
     */
    public function getList(Collection $collection)
    {
        $categories = [];
        $roots = [];
        foreach ($collection as $category) {
            if (!isset($categories[$category['id']])) {
                $categories[$category['id']] = [
                    'children' => []
                ];
            }
            $categories[$category['id']]['entity'] = $category;
            if ($category['parent_id'] === null) {
                $roots[] = $category['id'];
            } else {
                if (!isset($categories[$category['parent_id']])) {
                    $categories[$category['parent_id']] = [
                        'children' => []
                    ];
                }
                $categories[$category['parent_id']]['children'][] = $category['id'];
            }
        }

        return new Collection([
            'all' => $categories,
            'root' => $roots
        ]);
    }

    /**
     * Get nested list of categories using getList() method output
     *
     * Note: by default this will pass a generic collection of categories to the getList() method automatically. this can
     * be stopped by setting the $get_list param to false
     *
     * @param Collection $collection
     * @param bool $get_list
     * @return Collection
     */
    public function getNestedList(Collection $collection, $get_list = true)
    {
        if ($get_list) {
            $collection = $this->getList($collection);
        }

        $build_list = function ($category) use (&$build_list, $collection) {
            $entity = $category['entity']->toArray();
            $entity['categories'] = [];

            if (count($category['children']) > 0) {
                foreach ($category['children'] as $child) {
                    $entity['categories'][] = $build_list($collection['all'][$child]);
                }
            }

            return $entity;
        };

        $new_collection = [];
        foreach ($collection['root'] as $root_id) {
            $new_collection[] = $build_list($collection['all'][$root_id]);
        }

        return new Collection($new_collection);
    }

    /**
     * Archive all nested children by id
     *
     * @param string $id UUID
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     */
    public function archiveChildrenByID(string $id): void
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('systemFormCategories.parentSystemFormCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->partialUpdate(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category),
                    'status' => static::STATUS_ARCHIVED
                ]))->run();
            });
    }

    /**
     * Delete all children by id
     *
     * @param string $id UUID
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     */
    public function deleteChildrenByID(string $id): void
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('systemFormCategories.parentSystemFormCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category)
                ]))->run();
            });
    }
}
