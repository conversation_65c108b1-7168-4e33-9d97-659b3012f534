<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemMeta extends Model
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'companyFormItemMeta';
    protected $primaryKey = 'companyFormItemMetaID';
    protected $fillable = [
        'companyFormItemMetaID', 'companyFormItemID', 'formItemMetaID', 'valueType', 'value', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->belongsTo(CompanyFormItem::class, 'companyFormItemID', 'companyFormItemID');
    }

    public function formItemMeta()
    {
        return $this->belongsTo(FormItemMeta::class, 'formItemMetaID', 'formItemMetaID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItems', 'companyFormItems.companyFormItemID', "{$this->table}.companyFormItemID");
        return $query->where('companyFormItems.companyID', $company);
    }
}
