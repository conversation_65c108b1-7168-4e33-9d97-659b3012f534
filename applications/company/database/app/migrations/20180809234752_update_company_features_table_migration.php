<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class UpdateCompanyFeaturesTableMigration extends Migration
{
    public function up()
    {
        $this->schema->table('companiesFeatures', function (Blueprint $table) {
            $table->unsignedTinyInteger('status')->default(1)->after('featureID');
        });
    }

    public function down()
    {
        $this->schema->table('companiesFeatures', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}
