'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class KitchenSink extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, KitchenSink.Type.KITCHEN_SINK);
        Object.assign(this.state, {
            label: 'Kitchen Sink',
            icon: 'module--drawing--tools--kitchen-sink',
            node_type: Node.Entity.Type.KITCHEN_SINK
        });
    };
}

module.exports = KitchenSink;
