<?php

declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Carbon\Carbon;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

final class CreateCompanySetupProductItemsMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('companySetup')
            ->columns(
                function (Blueprint $table) {
                    $table->unsignedTinyInteger('industryProductItemsImport')->nullable()->after('companyID');
                    $table->dateTime('industryProductItemsImportCompletedAt')->nullable()->after('industryProductItemsImport');
                }
            )
            ->useHistory(false)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('companySetup')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['industryProductItemsImport', 'industryProductItemsImportCompletedAt']);
                }
            )
            ->useHistory(false)
            ->alter();
    }
}

