'use strict';

const Base = require('./base');
const Paper = require('../paper');
const Tool = require('../tools/base');
const Point = require('../utils/point');

const Styles = {
    SOLID: 1,
    DASHED: 2
};

/**
 * Freeform line class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes
 * @mixes SelectionOverlay
 * @abstract
 */
class FreeformLine extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            layer: Paper.Layer.FREEFORM_LINE,
            type: Base.Entity.Type.FREEFORM_LINE,
            tool_type: Tool.Type.FREEFORM_LINE,
            transfer_data: true
        });
    };

    /**
     * Get default state for line node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        Object.assign(state, {
            position: null,
            paths: [],
            path_idx: -1,
            width: 2,
            rotation: 0,
            color: '#000000',
            style: Styles.SOLID,
            finalized: false
        });
        return state;
    };

    /**
     * Load data into state
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        state.position = Point.of(state.position);
        state.paths = state.paths.map(segments => {
            return segments.map(segment => new this.paper.ps.Segment(...segment));
        });
        state.finalized = state.paths.length > 0;
        return state;
    };

    /**
     * Get available styles
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Style() {
        return Styles;
    };

    /**
     * Get position
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get position() {
        return this.state.position;
    };

    /**
     * Get width
     *
     * @readonly
     *
     * @returns {number}
     */
    get width() {
        return this.state.width;
    };

    /**
     * Get rotation
     *
     * @readonly
     *
     * @returns {number}
     */
    get rotation() {
        return this.state.rotation;
    };

    /**
     * Get color
     *
     * @readonly
     *
     * @returns {string}
     */
    get color() {
        return this.state.color;
    };

    /**
     * Get style
     *
     * @readonly
     *
     * @returns {number}
     */
    get style() {
        return this.state.style;
    };

    /**
     * Handle a zoom change by rendering node again
     */
    onZoomChanged() {
        this.drawSelected(this.getPaperItem('main'));
    };

    /**
     * Select node
     *
     * Subscribe to zoom change event so our selection overlay can be resized as needed.
     */
    select() {
        this.paperSubscribe('zoom-changed', 'onZoomChanged');
        super.select();
    };

    /**
     * Deselect node
     *
     * Unsubscribe from zoom change event.
     */
    deselect() {
        if (!this.selected || this.deleted) {
            return;
        }
        this.paperUnsubscribe('zoom-changed');
        super.deselect();
    };

    /**
     * Start new path
     *
     * @returns {number}
     */
    newPath() {
        this.state.path_idx++;
        this.state.paths[this.state.path_idx] = [];
        return this.state.path_idx;
    };

    /**
     * Remove last path and reset index
     */
    removeLastPath() {
        this.state.paths.pop();
        this.state.path_idx--;
    };

    /**
     * Add point to current path and optionally render
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @param {boolean} [render=true]
     */
    addPoint(point, render = true) {
        this.state.paths[this.state.path_idx].push(point);
        if (render) {
            this.render(true);
        }
    };

    /**
     * Finalize node before first commit
     *
     * We smooth and simplify all paths to reduce the amount of data needed to be stored. The current position is grabbed
     * and recorded before the entire group of paths is sent to [0, 0] so we can make the paths relative to it's
     * containing group instead of the project. All updated segments are stored back to paths array in state for final render.
     *
     * @param {boolean} [render=true]
     */
    finalize(render = true) {
        let group = this.build();
        group.children.forEach(path => {
            path.smooth({
                type: 'continuous'
            });
            path.simplify();
        });
        this.state.position = group.position.round();
        group.position = Point.of([group.bounds.width / 2, group.bounds.height / 2]);
        this.state.paths = group.children.map(path => {
            return Array.from(path.segments);
        });
        group.remove();
        this.state.finalized = true;
        if (render) {
            this.render(true);
        }
    };

    /**
     * Validate node
     *
     * @returns {boolean}
     */
    validate() {
        return this.state.paths.length > 0;
    };

    /**
     * Commit node
     *
     * If node is new (never had paths assigned to it), we finalize. This will only be done once in the nodes lifetime
     * so far.
     *
     * @param {boolean} [notify=true] - determines if events are fired
     */
    commit(notify = true) {
        if (!this.state.finalized) {
            this.finalize();
        }
        return super.commit(notify);
    };

    /**
     * Perform hit test of node only to determine if point exists within
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {(null|object)}
     */
    hitTest(point) {
        return this.selectionOverlayHitTest(point, {
            tolerance: this.paper.hit_test_tolerance
        });
    };

    /**
     * Build wrapper group containing all paths
     *
     * @returns {paper.Group}
     */
    build() {
        let children = this.state.paths.map(segments => {
            let config = {
                segments: segments.map(segment => segment.clone()),
                strokeColor: this.state.color,
                strokeWidth: this.state.width
            };
            if (this.state.style === Styles.DASHED) {
                config.dashArray = [8, 2];
            }
            return new this.paper.ps.Path(config);
        });
        return new this.paper.ps.Group({
            children,
            applyMatrix: !this.state.finalized,
            insert: false
        });
    };

    /**
     * Draw selected state
     *
     * @param {paper.Item} item
     */
    drawSelected(item) {
        let size = item.firstChild.strokeBounds.size,
            spacing = 0.05;
        this.drawSelectionOverlay(size, item.localToParent(item.firstChild.strokeBounds.center), {
            handles: {
                rotate: true
            },
            spacing: Math.min(size.width * spacing, size.height * spacing)
        });
    };

    /**
     * Draw and register node
     */
    draw() {
        let item = new this.paper.ps.Group({
            children: [this.build()],
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            applyMatrix: false
        });
        if (this.state.finalized) {
            item.position = this.state.position;
            item.rotation = this.state.rotation;
        }
        if (this.selected) {
            this.drawSelected(item);
        }
        this.setPaperItem('main', item, true);
    };

    /**
     * Normalize properties of node after update
     */
    postUpdate(data) {
        this.state.position = this.state.position.round();
        this.state.rotation = Math.round(this.state.rotation);
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        return {
            position: this.position,
            rotation: this.rotation,
            paths: this.state.paths.map(segments => {
                return segments.map(segment => {
                    let data = [Point.toArray(segment.point, true)];
                    if (segment.hasHandles()) {
                        data.push(Point.toArray(segment.handleIn, true), Point.toArray(segment.handleOut, true));
                    }
                    return data;
                });
            }),
            width: this.width,
            color: this.color,
            style: this.style
        };
    };
}

require('../node-mixins/selection_overlay')(FreeformLine);

module.exports = FreeformLine;
