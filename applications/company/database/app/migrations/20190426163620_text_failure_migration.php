<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class TextFailureMigration extends Migration
{
    public function up()
    {
        $this->newTable('textNumberDeliveryFailures')
            ->primaryKey('textNumberDeliveryFailureID', false)
            ->columns(function (Blueprint $table) {
                $table->string('number', 16);
                $table->string('code', 10)->nullable();
                $table->string('reason', 1000)->nullable();
            })
            ->column('textMessageRecipientID', Table::COLUMN_TYPE_UUID, [
                'after' => 'number',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('number', 'number_index');
                $table->index('textMessageRecipientID', 'text_message_recipient_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['textNumberDeliveryFailures']);
    }
}
