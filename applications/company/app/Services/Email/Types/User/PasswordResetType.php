<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\UserType;
use Common\Models\UserPasswordReset;

/**
 * Class PasswordResetType
 *
 * @package App\Services\Email\Types\User
 */
class PasswordResetType extends UserType
{
    use NotificationTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message|\App\Services\Email\Interfaces\MessageInterface
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        if (($password_reset = UserPasswordReset::withTrashed()->whereKey($this->getNotificationItemID())->first()) === null) {
            throw new TypeException('Unable to find password reset');
        }

        $this->setup($password_reset->user->company, [
            $password_reset->user
        ]);

        $message = $this->getMessage();
        $message->itemID($password_reset->getUuidKey());

        $domain = $this->getDomain();
        $message->subject("{$domain['brand']['name']} Password Reset");

        $template_vars = [
            'first_name' => $password_reset->user->userFirstName,
            'brand_name' => $domain['brand']['name'],
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilderFromRoute('page.auth.reset-password', ['id' => $password_reset->getUuidKey()->toString()])->csm()->build()
        ];
        $this->template->content = Template::fetch('emails.html.user.password-reset', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.password-reset', $template_vars));

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
