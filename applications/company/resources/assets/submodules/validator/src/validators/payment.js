'use strict';

const Parsley = require('parsleyjs');
const valid = require('card-validator');

const validators = {
    credit: {
        requirementType: 'string',
        validateString: function (value) {
            value = value.replace(/\D/g, '');
            return valid.number(value).isValid;
        },
        messages: {
            en: 'Credit card number is not valid.'
        }
    },
    expiration: {
        requirementType: 'boolean',
        validateString: function (value) {
            let [month, year] = value.split('/'),
                currentDate = new Date();
            if (month > 12) {
                return false;
            }
            let expiration = new Date(year, month - 1);
            return expiration > currentDate;
        },
        messages: {
            en: 'Credit card expiration must be in the future.'
        }
    },
    routing: {
        requirementType: 'boolean',
        validateString: function(routing) {
            let sum = 0,
                mod = 0,
                len = routing.length;
            if (len === 9) {
                sum = (
                    3 * (parseInt(routing.charAt(0)) + parseInt(routing.charAt(3)) + parseInt(routing.charAt(6))) +
                    7 * (parseInt(routing.charAt(1)) + parseInt(routing.charAt(4)) + parseInt(routing.charAt(7))) +
                    (parseInt(routing.charAt(2)) + parseInt(routing.charAt(5)) + parseInt(routing.charAt(8)))
                );
                mod = sum % 10;
            }
            return mod === 0 && len === 9;
        },
        messages: {
            en: 'Routing number is not correct.'
        }
    }
};

Object.keys(validators).forEach(validator => Parsley.addValidator(validator, validators[validator]));
