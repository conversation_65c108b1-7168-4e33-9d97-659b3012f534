<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Classes\Log;
use Core\Classes\Path;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Validation\Classes\{FieldConfig, Validation};
use Core\Exceptions\AppException;
use Exception;
use Ramsey\Uuid\Uuid;

/**
 * Class ErrorController
 *
 * @package App\Http\Controllers\App\API
 */
class ErrorController
{
    /**
     * @var \Monolog\Logger[]
     */
    protected $loggers = [];

    /**
     * Get logger instance for channel
     *
     * @param string $channel
     * @return \Monolog\Logger
     */
    protected function getLogger(string $channel): \Monolog\Logger
    {
        if (!isset($this->loggers[$channel])) {
            $this->loggers[$channel] = Log::create($channel, [
                'email' => [
                    'subject' => "JS Error [{$channel}]"
                ],
                'slack' => [
                    'username' => 'js-error'
                ],
                'file' => 'js_error.log',
                'app_processor' => false
            ]);
        }
        return $this->loggers[$channel];
    }

    /**
     * Ingest error records from frontend sources
     *
     * @param RequestInterface $request
     * @param Validation $validation
     * @param Path $path
     * @return \Core\Components\Http\Classes\Response
     * @throws AppException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function ingest(RequestInterface $request, Validation $validation, Path $path)
    {
        $config = FieldConfig::fromArray([
            'message' => [
                'label' => 'Message',
                'rules' => 'trim|required'
            ],
            'context' => [
                'label' => 'Context',
                'rules' => 'required|type[array]'
            ],
            'level' => [
                'label' => 'Level',
                'rules' => 'required|type[int]|in_array[levels]'
            ],
            'level_name' => [
                'label' => 'Level Name',
                'rules' => 'required|in_array[level_names]'
            ],
            'channel' => [
                'label' => 'Channel',
                'rules' => 'trim|required'
            ],
            'datetime' => [
                'label' => 'Datetime',
                'rules' => 'trim|required'
            ],
            'extra' => [
                'label' => 'Extra',
                'rules' => 'required|type[array]'
            ]
        ]);
        $config->store('levels', [Log::DEBUG, Log::INFO, Log::NOTICE, Log::WARNING, Log::ERROR]);
        $config->store('level_names', ['DEBUG', 'INFO', 'NOTICE', 'WARNING', 'ERROR']);
        $validation->config($config);

        $log_path = $path->logs('js-error-data/');
        if (!is_dir($log_path) && !mkdir($log_path, 0755)) {
            throw new Exception('Unable to create directory: %s', $log_path);
        }

        $user_id = Auth::user()->getKey();

        $records = $request->get('records', []);
        if (count($records) > 0) {
            foreach ($records as $record) {
                $validator = $validation->run($record);
                if (!$validator->valid()) {
                    continue;
                }
                $record['extra']['auth'] = [
                    'user_id' => $user_id
                ];
                $log_file = Uuid::uuid4()->toString() . '.json';
                $data = [
                    'context' => $record['context'],
                    'extra' => $record['extra']
                ];
                $file_path = $log_path . $log_file;
                if (file_put_contents($file_path, json_encode($data, JSON_PRETTY_PRINT)) === false) {
                    throw new AppException('Unable to write data to log file: %s', $file_path);
                }
                $this->getLogger($record['channel'])->log($record['level'], $record['message'], [
                    'datetime' => $record['datetime'],
                    'file' => $log_file
                ]);
            }
        }

        return Response::create('', 204);
    }
}
