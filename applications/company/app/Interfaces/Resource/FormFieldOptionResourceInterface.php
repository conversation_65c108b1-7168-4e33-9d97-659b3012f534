<?php

namespace App\Interfaces\Resource;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\UpdateOrCreateRequest;

/**
 * Interface FormFieldOptionResourceInterface
 *
 * @package App\Interfaces\Resource
 */
interface FormFieldOptionResourceInterface
{
    /**
     * Update or create entity
     *
     * @param Entity $entity
     * @return UpdateOrCreateRequest
     */
    public function updateOrCreate(Entity $entity): UpdateOrCreateRequest;

    /**
     * Delete missing options by field id
     *
     * @param string $field_id
     * @param array $option_ids
     */
    public function deleteMissingOptionsByFieldID(string $field_id, array $option_ids): void;
}
