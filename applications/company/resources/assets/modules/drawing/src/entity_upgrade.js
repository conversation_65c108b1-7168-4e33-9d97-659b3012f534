'use strict';

/**
 * Class which handles upgrading entities to latest version
 *
 * @memberof module:Drawing
 */
class EntityUpgrade {
    /**
     * Constructor
     */
    constructor() {
        this.state = {
            latest_version: 1,
            versions: new Map
        };
    };

    /**
     * Add version of entity
     *
     * @param {number} version
     * @param {function} closure - function which handles any updates to entity for version
     */
    version(version, closure) {
        if (this.state.versions.has(version)) {
            throw new Error(`Version ${version} already defined`);
        }
        this.state.versions.set(version, closure);
        if (version > this.state.latest_version) {
            this.state.latest_version = version;
        }
    };

    /**
     * Determines if entity requires an upgrade
     *
     * @param {module:Drawing/Entities.Base} entity
     * @returns {boolean}
     */
    isRequired(entity) {
        return entity.version < this.state.latest_version;
    };

    /**
     * Run upgrades
     *
     * Will run entity through all necessary version changes and return the resulting entity.
     *
     * @param {module:Drawing/Entities.Base} entity
     * @returns {Promise<module:Drawing/Entities.Base>}
     */
    async run(entity) {
        for (let i = entity.version + 1; i <= this.state.latest_version; i++) {
            let closure = this.state.versions.get(i);
            if (closure === undefined) {
                continue;
            }
            entity = await closure(entity);
        }
        return entity;
    };
}

module.exports = EntityUpgrade;
