const FIELD_NAMES = {
    EMAIL: 'email',
    PHONE: 'phone',
    ADDRESS: 'address',
    CITY: 'city',
    POSTAL_CODE: 'postal_code',
    STATE: 'state',
    MARKETING_SOURCE: 'marketing_source',
    PROJECT_TYPE: 'project_type',
    CUSTOMER_NOTES: 'customer_notes'
}

const FIELDS = {
    EMAIL: {name: 'email', label: 'Email'},
    PHONE: {name: 'phone', label: 'Phone #'},
    ADDRESS: {name: 'address', label: 'Address'},
    CITY: {name: 'city', label: 'City'},
    POSTAL_CODE: {name: 'postal_code', label: 'Postal Code'},
    STATE: {name: 'state', label: 'State/Province'},
    MARKETING_SOURCE: {name: 'marketing_source', label: 'How did you hear about us?'},
    PROJECT_TYPE: {name: 'project_type', label: 'Type of service needed'},
    CUSTOMER_NOTES: {name: 'customer_notes', label: 'Notes'}
};

const TYPES = {
    FREEFORM: 1,
    DROPDOWN: 2
}

const GOOGLE_RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';

const SMALL_LENGTH = 25;
const MEDIUM_LENGTH = 50;
const MAX_LENGTH = 200;

const STATUS_MESSAGES = {
    NOT_SETUP: '<span class="h-text t-grey">Not Setup</span>',
    DISABLED: '<span class="h-text t-grey">Disabled</span>',
    ENABLED: '<span class="h-text t-green">Enabled</span>'
};

module.exports = {
    STATUS_MESSAGES,
    SMALL_LENGTH,
    MEDIUM_LENGTH,
    MAX_LENGTH,
    FIELD_NAMES,
    FIELDS,
    TYPES,
    GOOGLE_RECAPTCHA_URL
}
