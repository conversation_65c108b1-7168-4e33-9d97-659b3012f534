<?php

namespace App\Http\Responses;

use Core\Classes\Path;
use Core\Components\Http\Exceptions\ResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Components\Http\Interfaces\ViewFactoryInterface;
use Core\Components\Http\Interfaces\ViewInterface;
use Core\Components\Http\Traits\ResponseHeadersTrait;

class LegacyFileResponse implements ResponseInterface
{
    use ResponseHeadersTrait;

    protected string $path;
    protected RequestInterface $request;
    protected ViewInterface $view;

    public function __construct($view, string $legacy_path, Path $path, RequestInterface $request, ViewFactoryInterface $view_factory)
    {
        $legacy_path = $path->appLegacy($legacy_path);
        if (!file_exists($legacy_path)) {
            throw new ResponseException('Legacy app file does not exist');
        }
        $this->path = $legacy_path;
        $this->request = $request;
        if (is_object($view) && $view instanceof ViewInterface) {
            $this->view = $view;
            return;
        }
        $this->view = $view_factory->fetch($view);
    }

    protected function runFile($__path)
    {
        $__include = null;
        $vars = static function () use ($__path, &$__include) {
            ob_start();
            $__include = include $__path;
            ob_end_clean();
            return get_defined_vars();
        };
        $vars = $vars();
        return [$__include, $vars];
    }

    // @todo figure out how to implement, probably not needed since this will go away
    public function getContent(): string
    {
        return '';
    }

    /**
     * Handle legacy files which will output directly to the main output stream
     */
    public function handle(): void
    {
        [$include, $vars] = $this->runFile($this->path);
        if (is_object($include) && $include instanceof ResponseInterface) {
            $include->handle();
            return;
        }
        $this->sendHeaders();
        $this->view->data($vars, ViewInterface::ACTION_MERGE);
        fwrite($this->request->io()->getOutputStream(), $this->view->render());
    }
}
