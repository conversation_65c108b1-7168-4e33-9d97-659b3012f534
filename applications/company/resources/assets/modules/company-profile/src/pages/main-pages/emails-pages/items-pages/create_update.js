'use strict';

const moment = require('moment-timezone');
const Popper = require('popper.js');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClickWatcher, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
const Tooltip = require('@ca-submodule/tooltip');

const FormValidator = require("@cas-validator-js");

const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const create_update_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/create_update.hbs');
const preview_append_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/emails/preview_append.hbs');
const preview_prepend_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/emails/preview_prepend.hbs');
const tag_menu_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/emails/tag_menu.hbs');
const tag_item_tpl = require('@cam-company-profile-tpl/pages/main-pages/emails-pages/items-pages/emails/tag_item.hbs');

const EmailTypes = {
    NEW_CUSTOMER: Api.Constants.EmailTemplates.Type.NEW_CUSTOMER,
    SALES_APPOINTMENT: Api.Constants.EmailTemplates.Type.SALES_APPOINTMENT,
    SALES_APPOINTMENT_REMINDER: Api.Constants.EmailTemplates.Type.SALES_APPOINTMENT_REMINDER,
    CUSTOMER_BID: Api.Constants.EmailTemplates.Type.CUSTOMER_BID,
    BID_ACCEPTED: Api.Constants.EmailTemplates.Type.BID_ACCEPTED,
    BID_REJECTED: Api.Constants.EmailTemplates.Type.BID_REJECTED,
    INSTALLATION_APPOINTMENT: Api.Constants.EmailTemplates.Type.INSTALLATION_APPOINTMENT,
    INSTALLATION_APPOINTMENT_REMINDER: Api.Constants.EmailTemplates.Type.INSTALLATION_APPOINTMENT_REMINDER,
    WARRANTIES: Api.Constants.EmailTemplates.Type.WARRANTIES,
    INVOICE: Api.Constants.EmailTemplates.Type.INVOICE,
    BID_FOLLOW_UP: Api.Constants.EmailTemplates.Type.BID_FOLLOW_UP
};

const EmailTypeNames = {
    [EmailTypes.NEW_CUSTOMER]: 'New Customer',
    [EmailTypes.SALES_APPOINTMENT]: 'Sales Appointment',
    [EmailTypes.SALES_APPOINTMENT_REMINDER]: 'Sales Appointment Reminder',
    [EmailTypes.CUSTOMER_BID]: 'Customer Bid',
    [EmailTypes.BID_ACCEPTED]: 'Bid Accepted',
    [EmailTypes.BID_REJECTED]: 'Bid Rejected',
    [EmailTypes.INSTALLATION_APPOINTMENT]: 'Installation Appointment',
    [EmailTypes.INSTALLATION_APPOINTMENT_REMINDER]: 'Installation Appointment Reminder',
    [EmailTypes.WARRANTIES]: 'Warranties',
    [EmailTypes.INVOICE]: 'Invoice',
    [EmailTypes.BID_FOLLOW_UP]: 'Bid Follow-Up'
};

const NewEmailTypes = [
    EmailTypes.BID_FOLLOW_UP
];

const EmailTemplateSources = {
    SYSTEM: Api.Constants.EmailTemplates.Source.SYSTEM,
    CUSTOM: Api.Constants.EmailTemplates.Source.CUSTOM
};

class CreateUpdate extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        let follow_up_instructions = 'Follow-up emails go to customers who still need to sign the bid contract. Configure the workflow and timing in <a data-navigate="settings.reminders.details">Company Settings</a>.';
        if (!profile_data.features.bid_follow_ups) {
            follow_up_instructions = `Bid Follow-Up Reminders is a new feature that is only available to customers on our current subscription packages. Your company is on a legacy subscription but you can upgrade to enable this feature. <a target="_blank" href="${fx_pages.ACCOUNT}">Click here</a> to review your subscription options.`;
        }
        Object.assign(this.state, {
            emails: parent.getParentByName('emails'),
            is_update: name === 'update',
            subject_tags_open: false,
            email_config: {
                [EmailTypes.NEW_CUSTOMER]: {
                    type: EmailTypes.NEW_CUSTOMER,
                    name: 'New Customer',
                    instructions: 'Sent when customer is added and appointment is not scheduled ("Send Introduction Email" checkbox must be checked).',
                    subject_tags: [
                        'company_name'
                    ],
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.SALES_APPOINTMENT]: {
                    type: EmailTypes.SALES_APPOINTMENT,
                    name: 'Sales Appointment',
                    instructions: 'Sent when a sales appointment is scheduled ("Notify Customer of Appointment" checkbox must be checked).',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'date_time', 'date', 'time', 'end_time', 'address', 'wisetack_prequal_link'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.SALES_APPOINTMENT_REMINDER]: {
                    type: EmailTypes.SALES_APPOINTMENT_REMINDER,
                    name: 'Sales Appointment Reminder',
                    instructions: 'Sent to customer 24 hours before sales appointment ("Reminder 24 Hours Before Appointment" setting must be enabled).',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'date_time', 'date', 'time', 'end_time', 'address', 'wisetack_prequal_link'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.CUSTOMER_BID]: {
                    type: EmailTypes.CUSTOMER_BID,
                    name: 'Customer Bid',
                    instructions: 'Sent with the bid to the customer. The bid link will be appended after the email content.',
                    subject_tags: [
                        'company_name', 'address', 'bid_name'
                    ],
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'viewBidLink'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.BID_ACCEPTED]: {
                    type: EmailTypes.BID_ACCEPTED,
                    name: 'Bid Accept',
                    instructions: 'Automatically sent when the customer accepts the bid.',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.BID_REJECTED]: {
                    type: EmailTypes.BID_REJECTED,
                    name: 'Bid Reject',
                    instructions: 'Automatically sent when the customer rejects the bid.',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.INSTALLATION_APPOINTMENT]: {
                    type: EmailTypes.INSTALLATION_APPOINTMENT,
                    name: 'Installation Appointment',
                    instructions: 'Sent when an install/job appointment is scheduled ("Notify Customer of Appointment" checkbox must be checked).',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    tags: [
                        'installerFirstName', 'installerLastName', 'installerBio', 'installerEmail', 'installerPhone', 'installerPicture', 'date_time', 'date', 'time', 'end_time', 'address'
                    ],
                    send_sales_option: true, // new
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.INSTALLATION_APPOINTMENT_REMINDER]: {
                    type: EmailTypes.INSTALLATION_APPOINTMENT_REMINDER,
                    name: 'Installation Appointment Reminder',
                    instructions: 'Sent to customer 24 hours before install/job appointment ("Reminder 24 Hours Before Appointment" setting must be enabled).',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    tags: [
                        'installerFirstName', 'installerLastName', 'installerBio', 'installerEmail', 'installerPhone', 'installerPicture', 'date_time', 'date', 'time', 'end_time', 'address'
                    ],
                    send_sales_option: true, // new
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.WARRANTIES]: {
                    type: EmailTypes.WARRANTIES,
                    name: 'Warranties',
                    instructions: 'Sent with the warranties to the customer.',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    send_sales_option: true, // new
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.INVOICE]: {
                    type: EmailTypes.INVOICE,
                    name: 'Invoice',
                    instructions: 'Sent with the invoice to the customer.',
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    send_sales_option: true, // new
                    source: EmailTemplateSources.SYSTEM
                },
                [EmailTypes.BID_FOLLOW_UP]: {
                    type: EmailTypes.BID_FOLLOW_UP,
                    name: 'Bid Follow-Up',
                    default_subject: 'Evaluation and Bid Follow-Up - {address}',
                    instructions: follow_up_instructions,
                    subject_tags: [
                        'company_name', 'address'
                    ],
                    tags: [
                        'evaluatorFirstName', 'evaluatorLastName', 'evaluatorBio', 'evaluatorEmail', 'evaluatorPhone', 'evaluatorPicture', 'viewBidLink'
                    ],
                    send_sales_option: true,
                    source: EmailTemplateSources.CUSTOM
                }
            },
            subject_tags: {},
            is_wisetack_enabled: profile_data.features.wisetack_api,
            is_merchant_approved: profile_data.wisetack.is_merchant_approved,
        });

        this.state.namespace = this.state.is_update ? 'e' : 'a';
    };

    /**
     * Check if Wisetack tags should be shown
     *
     * @returns {boolean}
     */
    isWisetackEnabledAndApproved() {
        const { is_wisetack_enabled, is_merchant_approved } = this.state
        return is_wisetack_enabled && is_merchant_approved;
    }

    /**
     * Pull data from form inputs
     *
     * @returns {object}
     */
    buildEntity() {
        let type = parseInt(this.state.validator.getInputElem('type').val()),
            config = this.state.email_config[type],
            entity = {
                name: this.state.validator.getInputElem('name').val(),
                source: config.source,
                type,
                subject: this.state.validator.getInputElem('subject').val(),
                content: this.state.validator.getInputElem('content').val(),
                is_send_from_salesperson: null
            };

        if (config.send_sales_option !== undefined) {
            entity.is_send_from_salesperson = this.elem.send_from_salesperson.is(':checked');
        } else {
            entity.is_send_from_salesperson = null;
        }
        return entity;
    };

    /**
     * Save email
     */
    save() {
        this.state.emails.showLoader();

        let data = this.buildEntity();

        let resource = Api.Resources.EmailTemplates(),
            request = this.state.is_update ? resource.partialUpdate(this.state.email_data.id, data) : resource.store(data);
        request.then(({data}) => {
            this.state.emails.hideLoader();
            let message = createSuccessMessage(`Email ${this.state.is_update ? 'edited' : 'added'} successfully`);
            this.router.main_route.layout.toasts.addMessage(message);
            this.router.navigate('emails.items.manager');
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    // for (let item in item_errors) {
                    //     if (this.state.field[item] === undefined) {
                    //         continue;
                    //     }
                    //     this.state.field[item].addError('fx-' + item, {message: item_errors[item]});
                    // }
                    this.state.emails.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save email, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Populate email with data from Api
     *
     * @param {object} data
     * @param {boolean} [duplicate=false]
     */
    async populateEmail(data) {
        if (data.status !== Api.Constants.EmailTemplates.Status.ACTIVE) {
            this.router.redirect('emails.items.manager');
            return;
        }
        this.state.validator.getInputElem('name').val(data.name);

        this.state.validator.getInputElem('subject').val(data.subject);
        this.state.validator.getInputElem('content').val(data.content);

        // append type, select it, then disable
        this.state.validator.getInputElem('type').append(`<option value="${data.type}">${EmailTypeNames[data.type]}</option>`);
        this.state.validator.getInputElem('type').val(data.type).prop('disabled', true);

        if (this.state.email_config[data.type].send_sales_option !== undefined) {
            this.elem.send_from_salesperson.prop('checked', data.is_send_from_salesperson).trigger('change');
        }

        this.elem.type_dropdown.trigger('change');
    };

    /**
     * Fetch email by id
     *
     * @param {string} id - UUID
     * @returns {Promise<void>}
     */
    async fetchData(id) {
        try {
            let {data: entity} = await Api.Resources.EmailTemplates()
                .fields(['id', 'status', 'name', 'type', 'subject', 'content', 'is_send_from_salesperson'])
                .retrieve(id);
            this.state.email_data = entity;
            return entity;
        } catch (e) {
            let message = createErrorMessage('Unable to fetch email info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    async fetchCompanyData() {
        try {
            let {data: entity} = await Api.Resources.Companies()
                .fields(
                    ['name', 'address', 'address_2', 'city', 'state', 'zip', 'website', 'color', 'email_from', 'logo_file_id']
                ).relations({
                    'phones': {},
                    'logo_media_urls': {}
                })
                .retrieve('current');
            this.state.company_data = entity;
            return entity;
        } catch (e) {
            let message = createErrorMessage('Unable to fetch company info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    async fetchUserData() {
        try {
            let {data: entity} = await Api.Resources.Users()
                .fields(['first_name', 'last_name', 'email', 'bio', 'phone_number'])
                .relations({
                    phones: {
                        'fields': ['number', 'description', 'is_primary']
                    }
                })
                .retrieve('current');
            this.state.user_data = entity;
            this.state.loaded = true;
        } catch (e) {
            let message = createErrorMessage('Unable to fetch user info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    async loadDefaultData() {
        return Promise.all([
            this.fetchCompanyData(), this.fetchUserData()
        ]);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        // show parent loader
        this.state.emails.showLoader();
        await this.loadDefaultData();
        if (this.state.is_update) {
            let data = await this.fetchData(request.params.email_id);
            this.populateEmail(data);
        } else {
            for (let item of NewEmailTypes) {
                this.state.validator.getInputElem('type').append(`<option value="${item}">${EmailTypeNames[item]}</option>`);
                this.state.validator.getInputElem('type').val(item).trigger('change');
            }
        }

        this.state.emails.hideLoader();
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.state.validator.getInputElem('type').empty();
        this.state.validator.getInputElem('type').prop('disabled', false);
        initSelectPlaceholder(this.elem.type_dropdown);

        this.elem.switch_container.show();
        this.state.validator.reset();
        this.state.editor.destroy();
        this.elem.root.scrollTop(0);

        this.clearError();

        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            subject: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            type: {
                required: true
            },
            content: {
                required: true,
                requiredMessage: 'Content is required'
            }
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {
                if (this.elem.content.val() === '') {
                    this.setError('Email content is required');
                }
            })
            .on('error', () => this.setError('Please review form errors below'));
    };

    setTagDefaults() {
        let company_address_2 = '';
        if (this.state.company_data.address_2 != null) {
            company_address_2 = ', ' + this.state.company_data.address_2;
        }
        let company_address = `${this.state.company_data.address}${company_address_2}, ${this.state.company_data.city}, ${this.state.company_data.state} ${this.state.company_data.zip}`;

        let company_phones = '';
        for (let phone in this.state.company_data.phones) {
            let this_phone = this.state.company_data.phones[phone];
            company_phones += this_phone.description + ': ' + this_phone.number + '<br/>';
        }

        this.state.tag_defaults = {
            company_address,
            company_phones,
            company_name: {
                label: 'Company Name',
                default: this.state.company_data.name
            },
            evaluatorFirstName: {
                label: 'Salesperson First Name',
                default: this.state.user_data.first_name
            },
            evaluatorLastName: {
                label: 'Salesperson Last Name',
                default: this.state.user_data.last_name
            },
            evaluatorBio: {
                label: 'Salesperson Bio',
                default: this.state.user_data.bio !== null ? this.state.user_data.bio : ''
            },
            evaluatorEmail: {
                label: 'Salesperson Email',
                default: this.state.user_data.email
            },
            evaluatorPhone: {
                label: 'Salesperson Phone',
                default: this.state.user_data.phone_number
            },
            evaluatorPicture: {
                label: 'Salesperson Picture',
                default: `<img style="max-height:180px;" src="${window.fx_url.assets.IMAGE+'no-image-user-profile.jpg'}" />`
            },
            date_time: {
                label: 'Appointment Date/Time',
                default: 'Monday, August 8, 2023 at 9:00 am'
            },
            date: {
                label: 'Appointment Date',
                default: 'Monday, August 8, 2023'
            },
            time: {
                label: 'Appointment Start Time',
                default: '9:00 am'
            },
            end_time: {
                label: 'Appointment End Time',
                default: '10:00 am'
            },
            address: {
                label: 'Appointment Address, City, State, Postal Code',
                default: '1234 Street Address, Cityville, MO 12345'
            },
            viewBidLink: {
                label: 'View Bid Link',
                default: `<a href="#" style="color: #${this.state.company_data.color};text-decoration: underline;">View Bid</a>`
            },
            installerFirstName: {
                label: 'Installer First Name',
                default: this.state.user_data.first_name
            },
            installerLastName: {
                label: 'Installer Last Name',
                default: this.state.user_data.last_name
            },
            installerBio: {
                label: 'Installer Bio',
                default: this.state.user_data.bio !== null ? this.state.user_data.bio : ''
            },
            installerEmail: {
                label: 'Installer Email',
                default: this.state.user_data.email
            },
            installerPhone: {
                label: 'Installer Phone',
                default: this.state.user_data.phone_number
            },
            installerPicture: {
                label: 'Installer Picture',
                default: `<img style="max-height:180px;" src="${window.fx_url.assets.IMAGE+'no-image-user-profile.jpg'}" />`
            },
            bid_name: {
                label: 'Bid Name',
                default: 'Example Bid'
            },
            wisetack_prequal_link: {
                label: 'Wisetack Prequal Link',
                default: ""
            }
        };
    };

    showSubjectTags() {
        let tag_menu = $(tag_menu_tpl());
        for (let item in this.state.subject_tags) {
            let this_item = this.state.subject_tags[item]
            tag_menu.append(tag_item_tpl({
                label: this_item.label,
                id: item
            }));
        }
        let that = this;
        onClickWatcher(tag_menu, jsSelector('tag'), function () {
            let tag = $(this).data('value'),
                subject = that.elem.subject.val();
            that.elem.subject.val(`${subject} {${tag}}`);
        }, false);

        this.state.tag_menu = tag_menu;
        this.elem.root.append(tag_menu);
        this.state.subject_tag_popper = new Popper(this.elem.subject_tags[0], this.state.tag_menu[0], {
            placement: 'bottom-end',
            preventOverflow: {
                boundariesElement: this.elem.root[0]
            }
        });

        this.state.subject_tags_open = true;
    };

    /**
     * Hide subject tags and destroy popper instance
     */
    hideSubjectTags() {
        if (!this.state.subject_tags_open) {
            return;
        }
        this.state.tag_menu.hide();
        this.state.subject_tag_popper.destroy();
        this.state.subject_tag_popper = null;
        this.state.subject_tags_open = false;
    };

    /**
     * Toggle subject tags using state subject tags open status
     */
    toggleSubjectTag() {
        if (this.state.subject_tags_open) {
            this.hideSubjectTags();
        } else {
            this.showSubjectTags();
        }
    };

    async setForm(config, new_email) {
        this.elem.instructions.html(config.instructions);
        if (new_email) {
            this.state.validator.getInputElem('name').val(config.name);
            this.state.validator.getInputElem('subject').val(config.default_subject);
        }

        this.setTagDefaults();

        let append_text = preview_append_tpl({
            company_logo: this.state.company_data.logo_media_urls.email_thumbnail,
            company_name: this.state.company_data.name,
            company_address: this.state.tag_defaults.company_address,
            company_phones: this.state.tag_defaults.company_phones,
            website: this.state.company_data.website,
            add_bid_link: config.type === EmailTypes.CUSTOMER_BID,
            company_color: `#${this.state.company_data.color}`
        });

        let textarea_config = {
            preset: 'simple',
            remove_empty_paragraphs: true,
            alignment: true,
            font_size: true,
            preview: true,
            preview_config: {
                title: `Email Preview: ${this.elem.name.val()}`,
                append: append_text
            }
        };


        let subject = this.state.validator.getInputElem('subject').val();
        if (config.tags !== undefined) {
            let tags = [],
                replacements = [];
            for (let tag of config.tags) {
                let tag_default = this.state.tag_defaults[tag];
                tags.push({
                    label: tag_default.label,
                    content: tag
                });
                replacements.push({
                    content: tag,
                    replacement: tag_default.default
                });
            }


            // // Set preview wisetack value
            if (this.isWisetackEnabledAndApproved()) {
                replacements = replacements.map(item => {
                    if (item.content === 'wisetack_prequal_link') {
                        item.replacement = `<p><a href="#">Click here</a> to prequalify for financing through Wisetack.</p>`;
                    }
                    return item;
                });
            }

            textarea_config.tags = tags;
            textarea_config.preview_config['tag_replacements'] = replacements;
        }

        if (config.subject_tags !== undefined) {
            for (let tag of config.subject_tags) {
                let tag_default = this.state.tag_defaults[tag];

                this.state.subject_tags[tag] = {
                    label: tag_default.label
                };
                subject = subject.replaceAll(`{${tag}}`, tag_default.default);
            }
        }

        if (config.send_sales_option === undefined) {
            this.elem.switch_container.hide();
        }

        textarea_config.preview_config.prepend = preview_prepend_tpl({
            email_from: this.state.company_data.email_from,
            todays_date: moment().format("MMMM D, YYYY"),
            subject
        });

        this.state.textarea = FormInput.init(this.elem.content, textarea_config);
        this.state.editor = await this.state.textarea.promise;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.initForm();

        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));

        this.elem.error = findChild(root, jsSelector('error'));

        this.elem.instructions = findChild(root, jsSelector('instructions'));
        this.elem.content = findChild(root, jsSelector('content'));

        this.elem.type_dropdown = findChild(root, jsSelector('type'));

        this.elem.name = findChild(root, jsSelector('name'));
        this.elem.subject = findChild(root, jsSelector('subject'));
        this.elem.subject_tags = findChild(root, jsSelector('subject-tags'));
        initSelectPlaceholder(this.elem.type_dropdown);

        this.elem.switch_container = findChild(root, jsSelector('switch-container'));
        this.elem.send_from_salesperson = findChild(root, jsSelector('send-from-salesperson'));
        FormInput.init(this.elem.send_from_salesperson);

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });

        onEvent(this.elem.subject_tags, 'click', (e) => {
            e.preventDefault();
            this.toggleSubjectTag();
            return false;
        });

        onEvent(this.elem.type_dropdown, 'change', (e) => {
            e.preventDefault();
            let type = this.elem.type_dropdown.val();
            if (type === '') {
                return;
            }
            this.setForm(this.state.email_config[type], !this.state.is_update);
            return false;
        });

        onEvent(this.elem.name, 'change', (e) => {
            e.preventDefault();
            this.state.textarea.setPreviewTitle(`Email Preview: ${this.elem.name.val()}`);
            return false;
        });

        onEvent(this.elem.root, 'click', (e) => {
            if (this.state.subject_tags_open) {
                this.hideSubjectTags();
            }
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return create_update_tpl({
            ns: this.state.namespace,
            cancel_route: 'emails.items.manager',
            title: `${this.state.is_update ? 'Edit' : 'Add'} Email`
        });
    };
}

module.exports = CreateUpdate;
