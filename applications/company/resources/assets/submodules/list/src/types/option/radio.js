'use strict';

const Option = require('../option');

/**
 * @memberof module:List/Types/Option
 */
class Radio extends Option {
    /**
     * Constructor
     *
     * @param {object} [config={}]
     */
    constructor(config = {}) {
        super(Object.assign({
            active_icon: 'icon--input-radio-active'
        }, config));
    };

    /**
     * Set selected items
     *
     * @param {(null|number|number[])} id - Id(s) of items to select, passing null clears everything
     * @param {boolean} [notify=true] - Whether to emit changed event
     */
    setSelected(id, notify = true) {
        let clear = id === null,
            old_ids = this.state.selected;
        if (clear) {
            this.deselectAll();
            if (notify) {
                this.notifyChange(old_ids);
            }
            return;
        }
        let item = this.getItem(lang.isArray(id) ? id.pop() : id);
        // if item is already active, we don't do anything which mimics browser radio input functionality
        if (item.active) {
            return;
        }
        this.deselectAll();
        this.setItemState(item, true);
        this.state.selected = [id];
        if (notify) {
            this.notifyChange(old_ids);
        }
    };

    /**
     * Handle item action
     *
     * @protected
     *
     * @param {number} id
     */
    handleItemAction(id) {
        let item = this.getItem(id);
        // if item is locked, then no action is allowed
        if (item.locked) {
            return;
        }
        this.setSelected(id);
    };
}

module.exports = Radio;
