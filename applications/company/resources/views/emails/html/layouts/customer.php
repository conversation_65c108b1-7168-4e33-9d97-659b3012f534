<html>
<head>
<style type="text/css">
    body {
        height:100% !important;
        background-color:#ffffff;
        margin:0;
        color:#151719;
        font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
        line-height:1.5;
    }
    div.emailContent {
        width:100%;
        margin:0px auto 0 auto;
        padding:10px 0 15px 0;
        background-color:#ffffff;
    }
    div.inner {
        margin:0px 30px 0px 30px;
    }
    div.emailFooter {
        width:100%;
    }

    span.highlight {
        color:<?=$company['color']?>;
        font-weight:bold;
    }

    a {
        color:<?=$company['color']?>;
    }

    a:visited {
        color:<?=$company['color']?>;
    }
</style>
</head>
<body>
<div class="emailContent">
    <div class="inner">
<?=$content?>
    </div>
</div>
<div class="emailFooter">
    <div class="inner">
        <p style="text-align:center;padding-bottom:20px;">
            <?=$company['name_address']?><br/>
<?php foreach ($company['phones'] as $phone): ?>
            <?=$phone['description']?> <?=$phone['number']?><br />
<?php endforeach; ?>
<?php if ($company['website'] !== null): ?>
            <a href="http://<?=$company['website']?>"><?=$company['website']?></a>
<?php endif; ?>
        </p>
<?php if (isset($unsubscribe_url)): ?>
        <p style="text-align:center;padding-bottom:20px;"><a href="<?=$unsubscribe_url?>">Unsubscribe</a></p>
<?php endif; ?>
    </div>
</div>
</body>
</html>