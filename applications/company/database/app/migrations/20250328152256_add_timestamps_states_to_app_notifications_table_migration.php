<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema\Table;


final class AddTimestampsStatesToAppNotificationsTableMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->updateTable('appNotifications')
            ->columns(function (Blueprint $table) {
                $table->json('metadata')->after('link')->nullable();
                $table->unsignedTinyInteger('associationType')->after('metadata')->nullable();
                $table->unsignedInteger('associationID')->after('associationType')->nullable();
            })
            ->index('search', Table::INDEX_TYPE_FULLTEXT, ['title', 'summary'])
            ->useHistory(false)
            ->alter();

        $this->updateTable('appNotifications')
            ->column('associationUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'associationID',
                'nullable' => true
            ])
            ->useHistory(false)
            ->alter();


        $this->updateTable('appNotificationDistribution')
            ->columns(function (Blueprint $table) {
                $table->dateTime('seenAt')->after('status')->nullable();
                $table->dateTime('readAt')->after('seenAt')->nullable();
                $table->dateTime('completedAt')->after('readAt')->nullable();
            })
            ->useHistory(false)
            ->alter();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $this->updateTable('appNotificationDistribution')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('seenAt');
                $table->dropColumn('readAt');
                $table->dropColumn('completedAt');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('appNotifications')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('metadata');
                $table->dropColumn('associationType');
                $table->dropColumn('associationID');
                $table->dropColumn('associationUUID');
            })
            ->useHistory(false)
            ->alter();
    }
}