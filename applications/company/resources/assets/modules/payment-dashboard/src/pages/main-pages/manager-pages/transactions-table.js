'use strict';

import moment from 'moment-timezone';

import Api, {Constants} from '@ca-package/api';
import {Base as Table} from '@ca-submodule/table';
import * as NotificationToast from '@ca-submodule/notification-toast';
const Tooltip = require('@ca-submodule/tooltip');
const Number = require('@cac-js/utils/number');

import VoidConfirmationModal from '../../../modals/void-confirmation';
import RefundConfirmationModal from '../../../modals/refund-confirmation';
import TransactionDetailsModal from '../../../modals/transaction-details';

// Transaction status constants matching backend PropelrTransaction model
const TRANSACTION_STATUS = {
    CAPTURED: 1,
    PENDING_SETTLEMENT: 2,
    UNDER_REVIEW: 3,
    SETTLED: 4,
    FUNDED: 5,
    VOIDED: 6,
    REFUNDED: 7,
    AUTH_DECLINED: 8,
    RETRYABLE: 9,
    SETTLEMENT_REJECTED: 10,
    SETTLEMENT_ERROR: 11,
    ZERO_AMOUNT: 12
};

const STATUS_DETAILS = {
    // Valid transaction statuses from PropelrTransaction model
    'CAPTURED': { class: 't-green', label: 'Captured' },
    'PENDING SETTLEMENT': { class: 't-yellow', label: 'Pending Settlement' },
    'UNDER REVIEW': { class: 't-yellow', label: 'Under Review' },
    'SETTLED': { class: 't-blue', label: 'Settled' },
    'FUNDED': { class: 't-green', label: 'Funded' },
    'VOIDED': { class: 't-grey', label: 'Voided' },
    'REFUNDED': { class: 't-orange', label: 'Refunded' },
    'AUTH DECLINED': { class: 't-red', label: 'Auth Declined' },
    'RETRYABLE': { class: 't-yellow', label: 'Retryable' },
    'SETTLEMENT REJECTED': { class: 't-red', label: 'Settlement Rejected' },
    'SETTLEMENT ERROR': { class: 't-red', label: 'Settlement Error' },
    'ZERO AMOUNT': { class: 't-grey', label: 'Zero Amount' },
    // Fallback for unknown statuses
    'UNKNOWN': { class: 't-grey', label: 'Unknown' },
};


export default class TransactionsTable {
    constructor(elem, state) {
        if (!elem || !state) {
            throw new Error('Element and state are required to initialize Table');
        }

        this.elem = elem;
        this.state = Object.assign(state, {
            table_component: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                },
                filters: {
                    created_at: [
                        Table.Operators.BETWEEN, [
                            moment.tz(state.layout.user.timezone)
                                .subtract(1, 'months')
                                .startOf('month')
                                .startOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
                            moment.tz(state.layout.user.timezone)
                                .endOf('day')
                                .utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
                        ]
                    ]
                }
            }
        });

        this.toast_controller = NotificationToast.getController();
    };

    /**
     * Format the status with corresponding HTML
     * @param {string} status - The status to format
     */
    formatStatus(status) {
        if (!status) return null;
        let details = STATUS_DETAILS[status.toUpperCase()] || STATUS_DETAILS['UNKNOWN'];
        return `<span class="h-text ${details.class}">${details.label}</span>`;
    };

    /**
     * Show success toast notification
     * @param {string} message - Success message to display
     */
    showSuccessToast(message) {
        const toast = NotificationToast.createMessage(message, 'success');
        this.toast_controller.addMessage(toast);
    }

    /**
     * Show error toast notification
     * @param {string} message - Error message to display
     */
    showErrorToast(message) {
        const toast = NotificationToast.createMessage(message, 'error');
        this.toast_controller.addMessage(toast);
    }

    /**
     * Initialize and create the table
     */
    create() {
        try {
            if (this.state.table_loaded) {
                this.refresh();
                return
            }

            this.state.table_component = new Table(this.elem)
                .on('row_click', this.handleRowClick.bind(this))
                .on('scope_change', this.handleScopeChange.bind(this))
                .on('table_drawn', this.handleTableDrawn.bind(this));

            this.setColumns();
            this.setRowActions();
            this.setButtons();
            this.setHeader();
            this.setFilterOptions();
            this.setAjaxRequest();

            if (this.state.table_scope) {
                this.state.table_component.setState(this.state.table_scope);
            }

            this.state.table_component.build();
            this.state.table_loaded = true;
        } catch (error) {
            console.error('Error creating the table:', error);
        }
    };


    /**
     * Refresh the table
     */
    refresh() {
        this.state.table_component.draw();
    };

    /**
     * Update the scope of the table
     * @param {Object} data - The new scope data
     * @param {boolean} setState - Whether to set the table state
     */
    updateScope(data, setState = false) {
        Object.assign(this.state.table_scope, data)

        if (setState) {
            this.state.table_component.setState(data);
        }
    };

    /**
     * Handle row click event
     * @param {Object} data - The data of the clicked row
     */
    handleRowClick(data) {
        this.handleViewDetails(data);
    };

    /**
     * Handle scope change event
     * @param {Object} scope - The new scope of the table
     */
    handleScopeChange(scope) {
        this.state.table_scope = scope;
        window.history.replaceState(null, '', window.location.href.split('?')[0] + Table.buildUrlFromScope(this.state.table_scope));
    };

    /**
     * Handle table drawn event to initialize tooltips
     */
    handleTableDrawn() {
        // Initialize tooltips for table content after table is drawn
        Tooltip.initAll(this.elem);
    };

    /**
     * Build span tooltip for column headers
     *
     * @param {string} text
     * @returns {string}
     */
    buildTooltip(text) {
        return `<span data-tooltip data-type="info">${text}</span>`
    };


    /**
     * Set the columns configuration
     */
    setColumns() {
        this.state.table_component.setColumns({
            id: {
                label: 'Transaction ID',
                value: data => data.retref || null,
            },
            customer_name: {
                label: 'Customer Name',
                value: data => {
                    const firstName = data.customer_first_name || '';
                    const lastName = data.customer_last_name || '';
                    return (firstName + ' ' + lastName).trim() || null;
                },
                orderable: false,
            },
            customer_email: {
                label: 'Customer Email',
                value: data => data.customer_email || null,
                orderable: false,
            },
            customer_phone: {
                label: 'Customer Phone',
                value: data => data.customer_phone || null,
                orderable: false,
            },
            invoice_number: {
                label: 'Invoice #',
                value: data => data.invoice_number || null,
                orderable: false,
            },
            total_amount: {
                label: 'Total',
                value: data => data.total_amount ? Number.toCurrency(data.total_amount) : null,
            },
            type: {
                label: 'Payment Type',
                value: data => {
                    if (data.type === Constants.PropelrTransaction.Type.CREDIT_CARD) return 'Credit Card';
                    if (data.type === Constants.PropelrTransaction.Type.ACH) return 'ACH';
                    return data.type ? this.buildTooltip(`Type ${data.type}`) : null;
                },
            },
            payment_info: {
                label: 'Payment Info',
                value: data => data.payment_info || null,
                orderable: false,
            },
            status_name: {
                label: 'Status',
                value: data => this.formatStatus(data.status_name),
                orderable: false,
            },
            batch_id: {
                label: 'Batch #',
                value: data => data.batch_id || null,
            },
            funding_id: {
                label: 'Funding #',
                value: data => data.funding_id || null,
            },
            captured_at: {
                label: 'Captured',
                value: data => this.formatDate(data.captured_at),
            },
            settled_at: {
                label: 'Settled At',
                value: data => this.formatDate(data.settled_at),
            },
            funded_at: {
                label: 'Funded At',
                value: data => this.formatDate(data.funded_at),
            },
            voided_at: {
                label: 'Voided',
                value: data => this.formatDate(data.voided_at),
            },
            refunded_at: {
                label: 'Refunded',
                value: data => this.formatDate(data.refunded_at),
            },
        });
    }

    /**
     * Format the date
     * @param {string} date - The date to format
     * @returns {string} - The formatted date string
     */
    formatDate(date) {
        return date ? moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY') : null;
    };

    /**
     * Set the row actions configuration
     */
    setRowActions() {
        this.state.table_component.setRowActions({
            details: {
                label: 'Details',
                visible: () => true,
                action: (data) => {
                    this.handleViewDetails(data);
                }
            },
            void: {
                label: 'Void',
                visible: (data) => {
                    return !data.voided_at && !data.refunded_at && !data.settled_at &&
                        data.status === TRANSACTION_STATUS.CAPTURED && data.status === TRANSACTION_STATUS.PENDING_SETTLEMENT
                    && data.status === TRANSACTION_STATUS.UNDER_REVIEW
                        ;
                },
                action: (data) => {
                    this.handleVoidTransaction(data);
                }
            },
            refund: {
                label: 'Refund',
                visible: (data) => {
                    return !data.voided_at && !data.refunded_at && 
                           (data.status === TRANSACTION_STATUS.SETTLED || data.status === TRANSACTION_STATUS.FUNDED);
                },
                action: (data) => {
                    this.handleRefundTransaction(data);
                }
            },
        });
    };

    /**
     * Handle void transaction action
     */
    async handleVoidTransaction(transaction) {
        const modal = new VoidConfirmationModal();
        
        return new Promise((resolve) => {
            modal.openWithTransaction(transaction, {
                promise: {
                    resolve: (result) => {
                        if (result.success) {
                            this.showSuccessToast(result.message);
                            this.refresh();
                        } else if (!result.cancelled) {
                            this.showErrorToast(result.error);
                        }
                        resolve(result);
                    }
                }
            });
        });
    };

    /**
     * Handle refund transaction action
     */
    async handleRefundTransaction(transaction) {
        const modal = new RefundConfirmationModal();
        
        return new Promise((resolve) => {
            modal.openWithTransaction(transaction, {
                promise: {
                    resolve: (result) => {
                        if (result.success) {
                            this.showSuccessToast(result.message);
                            this.refresh();
                        } else if (!result.cancelled) {
                            this.showErrorToast(result.error);
                        }
                        resolve(result);
                    }
                }
            });
        });
    };

    /**
     * Handle view transaction details action
     */
    handleViewDetails(transaction) {
        try {
            const modal = new TransactionDetailsModal(this.state);
            modal.openWithTransaction(transaction);
        } catch (error) {
            console.error('Error opening transaction details modal:', error);
            this.showErrorToast('Failed to load transaction details');
        }
    };


    setButtons() {
        this.state.table_component.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table_component.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/payment-transactions' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                }
            },
        });
    }

    /**
     * Set the header configuration with search
     */
    setHeader() {
        this.state.table_component.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Transactions'
        });
    }

    /**
     * Set the filter options for the table
     */
    setFilterOptions() {
        this.state.table_component.setFilterOptions({
            created_at: {
                label: 'Date Range',
                type: Table.FilterValueTypes.DATE,
            },
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: { label: 'Captured', value: 1 },
                    2: { label: 'Pending Settlement', value: 2 },
                    3: { label: 'Under Review', value: 3 },
                    4: { label: 'Settled', value: 4 },
                    5: { label: 'Funded', value: 5 },
                    6: { label: 'Voided', value: 6 },
                    7: { label: 'Refunded', value: 7 },
                    8: { label: 'Auth Declined', value: 8 },
                    9: { label: 'Retryable', value: 9 },
                    10: { label: 'Settlement Rejected', value: 10 },
                    11: { label: 'Settlement Error', value: 11 },
                    12: { label: 'Zero Amount', value: 12 }
                }
            },
            type: {
                label: 'Payment Type',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: { label: 'Credit Card', value: 1 },
                    2: { label: 'ACH', value: 2 }
                }
            },
            total_amount: {
                label: 'Total Amount',
                type: Table.FilterValueTypes.INTEGER,
            },
            captured_at: {
                label: 'Captured Date',
                type: Table.FilterValueTypes.DATE,
            },
            settled_at: {
                label: 'Settled Date',
                type: Table.FilterValueTypes.DATE,
            },
            funded_at: {
                label: 'Funded Date',
                type: Table.FilterValueTypes.DATE,
            },
            voided_at: {
                label: 'Voided Date',
                type: Table.FilterValueTypes.DATE,
            },
            refunded_at: {
                label: 'Refunded Date',
                type: Table.FilterValueTypes.DATE,
            }
        });
    }

    /**
     * Set the AJAX request for fetching table data
     */
    setAjaxRequest() {
        this.state.table_component.setAjax(Api.Resources.PaymentsDashboardTransactions, (request) => {
            request.accept('application/vnd.adg.fx.payments-dashboard-transactions-v1+json')
        });
    };
}
