<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Fields\Option;

use App\Resources\Form\Item\Group\FieldResource;
use App\Services\Form\Components\Fields\OptionField;
use App\Services\Form\Traits\Field\{CountTrait, InternalTrait, Option\ColumnsTrait};
use Core\Components\Http\StaticAccessors\View;

/**
 * Class CheckboxField
 *
 * @package App\Services\Form\Components\Fields\Option
 */
class CheckboxField extends OptionField
{
    use ColumnsTrait;
    use CountTrait;
    use InternalTrait;

    /**
     * @var int Field type
     */
    protected int $type = FieldResource::TYPE_CHECKBOX;

    /**
     * Set config
     *
     * Pull columns info from config as necessary.
     *
     * @param array $config
     * @return $this
     */
    public function setConfig(array $config): self
    {
        parent::setConfig($config);
        $this->setColumnsConfig($config);
        $this->setCountConfig($config);
        $this->setInternalConfig($config);
        return $this;
    }

    /**
     * Build field config, add columns config if available
     *
     * @return array
     */
    protected function buildConfig(): array
    {
        $config = parent::buildConfig();
        $config = $this->getColumnsConfig($config);
        $config = $this->getCountConfig($config);
        return $this->getInternalConfig($config);
    }

    /**
     * Get variables needed to build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    protected function getTemplateVars(int $layout_type): array
    {
        $vars = parent::getTemplateVars($layout_type);
        array_unshift($vars['classes'], 't-checkbox');
        $vars['value'] = View::fetch('services.form.structure.fields.value_with_default', [
            'default_value' => 'N/A' // @todo add config for this later
        ])->render();
        return $vars;
    }
}
