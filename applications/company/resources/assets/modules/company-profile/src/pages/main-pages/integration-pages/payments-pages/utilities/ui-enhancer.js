'use strict';

const Inputmask = require('inputmask');
const NumberInput = require("@cas-form-input-js/number");
const FormValidator = require('@ca-submodule/validator');
const states = require('@cac-js/data/states');
const us_territories = require('@cac-js/data/us_territories');
const provinces = require('@cac-js/data/provinces');
const { OWNERSHIP_TYPE_TITLES } = require('./constants');
const $ = require("jquery");

/**
 * UI Enhancement utilities for the Payment Setup form
 * Handles form validation, input initialization, and UI enhancements
 */

/**
 * Get validation rules configuration for the payment setup form
 * @returns {object} FormValidator configuration object
 */
function getValidationRules() {
    return {
        // ------ Required text fields ------
        dba_name: {
            required: true,
            maxlength: 100
        },
        legal_business_name: {
            required: true,
            maxlength: 100
        },
        tax_filing_name: {
            required: true,
            maxlength: 100
        },
        tax_filing_method: {
            required: true
        },
        tax_id: {
            required: true,
            pattern: /^\d{3}-\d{2}-\d{4}$/,
            'pattern-message': 'Please enter a valid Tax ID'
        },

        // ------ Contact info ------
        owner_first_name: {
            required: true,
            maxlength: 50
        },
        owner_last_name: {
            required: true,
            maxlength: 50
        },
        owner_email: {
            required: true,
            type: 'email',
            maxlength: 100
        },

        // ------ Business address ------
        business_address_line1: {
            required: true,
            maxlength: 100
        },
        business_address_line2: {
            required: true,
            maxlength: 50
        },
        business_city: {
            required: true,
            pattern: /^[A-Za-z\s]+$/,
            'pattern-message': 'Please enter a valid city name (letters and spaces only)',
            maxlength: 50
        },
        business_state: {
            required: true
        },
        business_zip: {
            required: true,
            pattern: /^\d{5}(-\d{4})?$/,
            'pattern-message': 'Please enter a valid postal code (XXXXX or XXXXX-XXXX)',
            maxlength: 10
        },
        business_country: {
            required: true
        },

        // ------ Mailing address ------
        mailing_address_line1: {
            required: true,
            maxlength: 100
        },
        mailing_address_line2: {
            required: false,
            maxlength: 50
        },
        mailing_city: {
            required: true,
            pattern: /^[A-Za-z\s]+$/,
            'pattern-message': 'Please enter a valid city name (letters and spaces only)',
            maxlength: 50
        },
        mailing_state: {
            required: true
        },
        mailing_zip: {
            required: true,
            pattern: /^\d{5}(-\d{4})?$/,
            'pattern-message': 'Please enter a valid postal code (XXXXX or XXXXX-XXXX)',
            maxlength: 10
        },
        mailing_country: {
            required: true
        },

        // ------------ Owner info ------------
        owner_name: {
            required: true,
            maxlength: 100
        },
        owner_title: {
            required: true,
            maxlength: 50
        },
        owner_phone: {
            required: true,
            pattern: /^[2-9]\d{2}-\d{3}-\d{4}$/,
            'pattern-message': 'Phone must be XXX-XXX-XXXX format. Cannot start with 0 or 1 and must not be all the same numbers.',
            'copilot-phone': true
        },
        owner_email_address: {
            required: true,
            type: 'email',
            maxlength: 100
        },
        drivers_license_number: {
            required: true,
            maxlength: 20
        },
        drivers_license_state: {
            required: true
        },
        owner_address_line1: {
            required: true,
            maxlength: 100
        },
        owner_address_line2: {
            required: true,
            maxlength: 50
        },
        owner_city: {
            required: true,
            pattern: /^[A-Za-z\s]+$/,
            'pattern-message': 'Please enter a valid city name (letters and spaces only)',
            maxlength: 50
        },
        owner_state: {
            required: true
        },
        owner_zip: {
            required: true,
            pattern: /^\d{5}(-\d{4})?$/,
            'pattern-message': 'Please enter a valid postal code (XXXXX or XXXXX-XXXX)',
            maxlength: 10
        },
        owner_country: {
            required: true
        },

        // ------------ Banking info ------------
        deposit_account_number: {
            required: true,
            pattern: /^\d{4,17}$/,
            'pattern-message': 'Account number must be 4-17 digits',
            maxlength: 17
        },
        deposit_routing_number: {
            required: true,
            routing: true
        },
        deposit_account_type: {
            required: true
        },
        deposit_bank_name: {
            required: true,
            maxlength: 100
        },
        withdrawal_account_number: {
            required: true,
            pattern: /^\d{4,17}$/,
            'pattern-message': 'Account number must be 4-17 digits',
            maxlength: 17
        },
        withdrawal_routing_number: {
            required: true,
            routing: true
        },
        withdrawal_account_type: {
            required: true
        },
        withdrawal_bank_name: {
            required: true,
            maxlength: 100
        },

        // ------------ Phone fields ------------
        business_phone: {
            pattern: /^[2-9]\d{2}-\d{3}-\d{4}$/,
            'pattern-message': 'Phone must be XXX-XXX-XXXX format. Cannot start with 0 or 1 and must not be all the same numbers.',
            'copilot-phone': true
        },
        contact_phone: {
            pattern: /^[2-9]\d{2}-\d{3}-\d{4}$/,
            'pattern-message': 'Phone must be XXX-XXX-XXXX format. Cannot start with 0 or 1 and must not be all the same numbers.',
            'copilot-phone': true
        },
        owner_mobile_phone: {
            pattern: /^[2-9]\d{2}-\d{3}-\d{4}$/,
            'pattern-message': 'Phone must be XXX-XXX-XXXX format. Cannot start with 0 or 1 and must not be all the same numbers.',
            'copilot-phone': true
        },

        // ------------ Misc fields ------------
        website_address: {
            type: 'url'
        },
        contact_email: {
            type: 'email',
            maxlength: 100
        },
        owner_ssn: {
            pattern: /^\d{3}-\d{2}-\d{4}$/,
            'pattern-message': 'SSN must be in format XXX-XX-XXXX'
        },
        owner_ownership_pct: {
            pattern: /^(100(\.0{1,2})?|\d{1,2}(\.\d{1,2})?)$/,
            'pattern-message': 'Please enter a valid percentage (0-100)'
        },

        // ------------ Currency fields ------------
        average_monthly_volume: {
            currency: true
        },
        high_ticket_amount: {
            currency: true
        },
        average_ticket_amount: {
            currency: true
        },
    };
}

/**
 * Get additional validation rules for dynamically created additional owner fields
 * @param {jQuery} $form - The form element
 * @returns {object} Additional validation rules object
 */
function getAdditionalOwnerValidationRules($form) {
    const additional_rules = {};

    // Find all additional owner fields and create validation rules for them
    $form.find('input[name^="additional_owner_"]').each(function() {
        const $field = $(this);
        const field_name = $field.attr('name');

        if (field_name.endsWith('_first_name')) {
            additional_rules[field_name] = {
                required: true,
                maxlength: 50
            };
        } else if (field_name.endsWith('_last_name')) {
            additional_rules[field_name] = {
                required: true,
                maxlength: 50
            };
        } else if (field_name.endsWith('_ownership_pct')) {
            additional_rules[field_name] = {
                required: true,
                pattern: /^(100(\.0{1,2})?|\d{1,2}(\.\d{1,2})?)$/,
                'pattern-message': 'Please enter a valid percentage (0-100)'
            };
        }
    });

    return additional_rules;
}


/**
 * Populate state dropdown menus
 * @param {Array} dropdowns - Array of dropdown elements
 */
function populateStateDropdowns(dropdowns) {
    const state_groups = [
        { label: 'States', options: states },
        { label: 'US Territories', options: us_territories },
        { label: 'Provinces', options: provinces }
    ];

    dropdowns.forEach($dropdown => {
        if ($dropdown?.length) {
            state_groups.forEach(group => {
                const group_options = Object.entries(group.options)
                    .map(([key, value]) => `<option value="${key}">${value}</option>`)
                    .join('');
                $dropdown.append(`<optgroup label="${group.label}">${group_options}</optgroup>`);
            });
        }
    });
}


/**
 * Setup tax filing method change handler and dynamic masking for tax ID
 * @param {jQuery} $form - The form element
 * @param {FormValidator} validator - The form validator instance
 */
function setupTaxFilingMethodHandler($form, validator) {
    const $tax_filing_method = $form.find('select[data-js="tax-filing-method"]');
    const $tax_id = $form.find('input[data-js="tax-id"]');

    if (!$tax_filing_method.length || !$tax_id.length) return;

    // Handle tax filing method changes
    $tax_filing_method.on('change', function() {
        const method = $(this).val();
        handleTaxFilingMethodChange(method, $tax_id, validator);
    });
}

/**
 * Handle tax filing method change and update tax ID masking and validation
 * @param {string} method - The selected tax filing method ('SSN' or 'EIN')
 * @param {jQuery} $tax_id - The tax ID input element
 * @param {FormValidator} validator - The form validator instance
 */
function handleTaxFilingMethodChange(method, $tax_id, validator) {
    if (!$tax_id?.length) return;

    // Clear current value when switching methods
    $tax_id.val('');

    // Remove existing inputmask
    if ($tax_id[0].inputmask) {
        $tax_id[0].inputmask.remove();
    }

    // Update validation pattern and message based on method
    let pattern, message, mask;

    if (method === 'SSN') {
        pattern = /^\d{3}-\d{2}-\d{4}$/;
        message = 'SSN must be in format XXX-XX-XXXX';
        mask = '***********';
    } else if (method === 'EIN') {
        pattern = /^\d{2}-\d{7}$/;
        message = 'EIN must be in format XX-XXXXXXX';
        mask = '99-9999999';
    } else {
        // No method selected, disable input
        $tax_id.prop('disabled', true);
        return;
    }

    // Enable input
    $tax_id.prop('disabled', false);

    // Apply new input mask
    Inputmask(mask).mask($tax_id[0]);

    // Update validation if validator is available
    if (validator && validator.updateFieldRules) {
        validator.updateFieldRules('tax_id', {
            required: true,
            pattern: pattern,
            'pattern-message': message
        });
    }
}

/**
 * Setup ownership type change handler and initialize owner title dropdown
 * @param {jQuery} $form - The form element
 */
function setupOwnershipTypeHandler($form) {
    const $ownership_type = $form.find('select[data-js="ownership-type-code"]');
    const $owner_title = $form.find('select[data-js="owner-title"]');

    if (!$ownership_type.length || !$owner_title.length) return;

    // Initialize owner title dropdown as disabled
    $owner_title.prop('disabled', true);

    // Handle ownership type changes
    $ownership_type.on('change', function() {
        handleOwnershipTypeChange($(this).val(), $owner_title);
    });
}

/**
 * Handle ownership type dropdown change and populate owner title options
 * @param {string} ownership_type - The selected ownership type code
 * @param {jQuery} $owner_title - The owner title dropdown element
 */
function handleOwnershipTypeChange(ownership_type, $owner_title) {
    if (!$owner_title || !$owner_title.length) return;
    
    // Clear current options
    $owner_title.empty();
    
    if (!ownership_type || !OWNERSHIP_TYPE_TITLES[ownership_type]) {
        // No valid ownership type selected - disable and show placeholder
        $owner_title.prop('disabled', true);
        $owner_title.append('<option value="" disabled selected>Select ownership type first</option>');
        return;
    }
    
    // Enable dropdown and add default option
    $owner_title.prop('disabled', false);
    $owner_title.append('<option value="" disabled selected>--</option>');
    
    // Populate with titles for the selected ownership type
    const titles = OWNERSHIP_TYPE_TITLES[ownership_type];
    titles.forEach(title => {
        $owner_title.append(`<option value="${title.value}">${title.label}</option>`);
    });
    
    // Clear any previous selection
    $owner_title.val('');
    
    // Trigger change event for validation
    $owner_title.trigger('change');
}

function initializeFormInputs($form, elements, FormInput) {
    const currency_fields = [
        $form.find('input[data-js="average-monthly-volume"]'),
        $form.find('input[data-js="high-ticket-amount"]'),
        $form.find('input[data-js="average-ticket-amount"]')
    ];

    currency_fields.forEach($input => {
        FormInput.init($input, {
            type: NumberInput.Type.CURRENCY,
            right_align: true,
            allow_minus: false,
            label: '$',
        });
    });

    const date_config = {
        pickr_config: {
            dateFormat: "m/d/Y",
            altFormat: "m/d/Y",
            maxDate: "today",
            allowInput: false
        }
    };

    if (elements.business_start_date?.length) {
        FormInput.init(elements.business_start_date, date_config);
    }

    if (elements.owner_dob?.length) {
        FormInput.init(elements.owner_dob, date_config);
    }

    const sensitive_fields = [
        elements.owner_ssn,
        elements.deposit_account_number,
        elements.deposit_routing_number,
        elements.withdrawal_account_number,
        elements.withdrawal_routing_number
    ];

    sensitive_fields.forEach(field => {
        if (field?.length) {
            FormInput.init(field, {
                min_score: false
            });
        }
    });
}

function setupFormBehaviors($form) {
    $form.find('input[type="tel"]').each(function() {
        Inputmask('************').mask(this);
    });

    $form.find('input[data-js*="routing-number"], input[data-js*="account-number"]').on('input', function() {
        this.value = this.value.replace(/\D/g, '');
    });
    $form.find('input[data-js="owner-ssn"]').on('input', function() {
        this.value = this.value.replace(/[^\d-]/g, '');
    });

    // Initialize tax_id as disabled until tax filing method is selected
    $form.find('input[data-js="tax-id"]').prop('disabled', true);

    $form.find('select[data-js*="country"]').each(function() {
        $(this).val('US').prop('disabled', true);
    });
}

function showMailingSection(elements) {
    elements.mailing_address_group?.removeClass('t-hidden');
    elements.add_mailing_address_button?.closest('.c-ps-add-section').addClass('t-hidden');
    
    if (elements.mailing_state?.children().length <= 1) {
        populateStateDropdowns([elements.mailing_state]);
    }
}

function hideMailingSection(elements) {
    elements.mailing_address_group?.addClass('t-hidden');
    elements.add_mailing_address_button?.closest('.c-ps-add-section').removeClass('t-hidden');
    
    elements.mailing_address_group?.find('input, select').each(function() {
        const $field = $(this);
        if ($field.attr('name') === 'mailing_country' || $field.attr('data-js') === 'mailing-country') {
            $field.val('US');
        } else {
            $field.val('');
        }
        $field.trigger('change');
    });
}

function showAdditionalOwnersSection(elements, state) {
    elements.additional_owners_group?.removeClass('t-hidden');
    elements.add_additional_owner_button?.addClass('t-hidden');

    if (state.additional_owners_count === 0) {
        addAdditionalOwnerEntry(elements, state);
    }
}

function addAdditionalOwnerEntry(elements, state) {
    if (state.additional_owners_count >= 2) {
        return;
    }

    const index = state.additional_owners_count;
    const owner_html = `
        <div class="c-ps-additional-owner-entry" data-js="additional-owner-${index}">
            <div class="c-ps-additional-owner-header">
                <h5 class="c-ps-subtitle">Additional Owner</h5>
                <a class="c-ps-remove-button" data-js="remove-additional-owner-${index}">
                    <div data-text>Remove</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
                </a>
            </div>
            <div class="c-f-wrapper">
                <div class="f-field">
                    <label class="f-f-label" for="additional_owner_${index}_first_name">First Name</label>
                    <input class="f-f-input" id="additional_owner_${index}_first_name" name="additional_owner_${index}_first_name" type="text" required data-js="additional-owner-${index}-first-name" />
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="additional_owner_${index}_last_name">Last Name</label>
                    <input class="f-f-input" id="additional_owner_${index}_last_name" name="additional_owner_${index}_last_name" type="text" required data-js="additional-owner-${index}-last-name" />
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="additional_owner_${index}_ownership_pct">Ownership Percentage</label>
                    <input class="f-f-input" id="additional_owner_${index}_ownership_pct" name="additional_owner_${index}_ownership_pct" type="text" required data-fx-form-input="number" data-js="additional-owner-${index}-ownership-pct" />
                </div>
            </div>
        </div>
    `;

    elements.additional_owners_container?.append(owner_html);
    state.additional_owners_count++;

    const $remove_button = elements.additional_owners_container?.find(`[data-js="remove-additional-owner-${index}"]`);
    $remove_button?.on('click', (e) => {
        e.preventDefault();
        removeAdditionalOwnerEntry(elements, state, index);
        return false;
    });

    if (state.additional_owners_count >= 2) {
        elements.add_additional_owner_entry_button?.addClass('t-hidden');
    }
}

function removeAdditionalOwnerEntry(elements, state, index) {
    elements.additional_owners_container?.find(`[data-js="additional-owner-${index}"]`).remove();
    state.additional_owners_count--;

    renumberAdditionalOwners(elements, state);

    if (state.additional_owners_count < 2) {
        elements.add_additional_owner_entry_button?.removeClass('t-hidden');
    }
}

function renumberAdditionalOwners(elements, state) {
    const $entries = elements.additional_owners_container?.find('.c-ps-additional-owner-entry');
    
    $entries?.each((new_index, entry) => {
        const $entry = $(entry);
        const old_index = $entry.attr('data-js')?.replace('additional-owner-', '');
        
        $entry.attr('data-js', `additional-owner-${new_index}`);
        
        $entry.find('input').each(function() {
            const $input = $(this);
            const name = $input.attr('name');
            const id = $input.attr('id');
            const data_js = $input.attr('data-js');
            
            if (name) {
                const new_name = name.replace(`additional_owner_${old_index}_`, `additional_owner_${new_index}_`);
                $input.attr('name', new_name);
            }
            
            if (id) {
                const new_id = id.replace(`additional_owner_${old_index}_`, `additional_owner_${new_index}_`);
                $input.attr('id', new_id);
            }
            
            if (data_js) {
                const new_data_js = data_js.replace(`additional-owner-${old_index}-`, `additional-owner-${new_index}-`);
                $input.attr('data-js', new_data_js);
            }
        });
        
        $entry.find('label').each(function() {
            const $label = $(this);
            const for_attr = $label.attr('for');
            
            if (for_attr) {
                const new_for = for_attr.replace(`additional_owner_${old_index}_`, `additional_owner_${new_index}_`);
                $label.attr('for', new_for);
            }
        });
        
        const $remove_button = $entry.find(`[data-js="remove-additional-owner-${old_index}"]`);
        if ($remove_button.length) {
            $remove_button.attr('data-js', `remove-additional-owner-${new_index}`);
            
            $remove_button.off('click').on('click', (e) => {
                e.preventDefault();
                removeAdditionalOwnerEntry(elements, state, new_index);
                return false;
            });
        }
    });
}

/**
 * Custom validation function for CoPilot phone requirements
 * @param {string} value - The phone number value
 * @returns {boolean} - Whether the phone is valid
 */
function validateCoPilotPhone(value) {
    if (!value) return true; // Allow empty values for optional fields
    
    // Remove formatting and keep only digits
    const digits_only = value.replace(/\D/g, '');
    
    // Must be exactly 10 digits
    if (digits_only.length !== 10) return false;
    
    // Area code cannot start with 0 or 1
    if (digits_only[0] === '0' || digits_only[0] === '1') return false;
    
    // Cannot be all the same digits
    if (new Set(digits_only.split('')).size === 1) return false;
    
    // Check for common invalid patterns
    const invalid_patterns = [
        '1234567890',
        '0123456789', 
        '9876543210',
        '5555555555',
        '1111111111',
        '0000000000'
    ];
    
    return !invalid_patterns.includes(digits_only);
}

function reinitializeValidator(elements, state) {
    if (state.validator) {
        // Only reset validation state, not form data
        state.validator.state.instance.reset();  // Resets Parsley validation state
        state.validator.clearAllFieldErrors();   // Clear any existing error messages
        
        // Destroy the Parsley instance properly
        state.validator.state.instance.destroy();
        state.validator = null;
    }
    
    const base_rules = getValidationRules();
    const additional_owner_rules = getAdditionalOwnerValidationRules(elements.setup_form);
    const all_rules = Object.assign({}, base_rules, additional_owner_rules);
    
    // Register custom CoPilot phone validator
    const config = {
        validators: {
            'copilot-phone': {
                fn: validateCoPilotPhone,
                message: 'Phone must be XXX-XXX-XXXX format. Cannot start with 0 or 1 and must not be all the same numbers.'
            }
        }
    };
    
    state.validator = FormValidator.create(elements.setup_form, all_rules, config);
}

const UI = {
    getValidationRules,
    populateStateDropdowns,
    setupTaxFilingMethodHandler,
    handleTaxFilingMethodChange,
    setupOwnershipTypeHandler,
    handleOwnershipTypeChange,
    initializeFormInputs,
    setupFormBehaviors,
    showMailingSection,
    hideMailingSection,
    showAdditionalOwnersSection,
    addAdditionalOwnerEntry,
    reinitializeValidator
};

module.exports = UI;