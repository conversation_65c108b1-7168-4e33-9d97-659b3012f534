<?php

namespace App\ResourceDelegates;

use App\ResourceMediaHandlers\Media\FileHandler;
use App\Resources\CompanyResource;
use App\Resources\FileResource;
use App\Resources\MediaResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use App\Traits\ResourceDelegate\TrainingActionTrait;
use Carbon\Carbon;
use Common\Models\File;
use Common\Models\Media;
use Common\Models\TrainingAction;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class MediaDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;
    use TrainingActionTrait;

    /**
     * Note: These match up to the official extensions for the mime types we accept, it doesn't always match the normal
     *       extensions you usually see.
     *
     *       mp3 -> mpga
     *       mov -> qt
     *
     * @var array
     */
    protected $allowed_mimes = [
        'jpeg', 'png', 'gif', 'pdf', 'mp4', 'm4v', 'qt', 'wmv', 'avi', 'ogv', 'webm', 'doc', 'docx',
        'ppt', 'pptx', 'pps', 'ppsx', 'xls', 'xlsx', 'mpga', 'oga', 'wav', 'csv', 'txt'
    ];

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('company')->resource(CompanyResource::class);
        $list->oneOrMany('file')->resource(FileResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('mediaID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([MediaResource::ACTION_CREATE, MediaResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id')
            ->onAction(MediaResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->validation('Name', 'required|max_length[200]');

        $list->field('description')
            ->validation('Description', 'nullable|optional|max_length[5000]');

        $list->field('is_bid_media')
            ->column('isBidMedia')
            ->validation('Is Bid Media', 'required|type[bool]')
            ->onAction(MediaResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required')
                    ->save();
            })
            ->onAction(MediaResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|cast[bool]');
            });

        $list->field('file_id')
            ->typeUuid()
            ->column('fileID', true)
            ->validation('File Id', 'required|uuid|check_file_id')
            ->onAction(MediaResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('content_type')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('files AS ctf', 'ctf.fileID', '=', 'media.fileID');
            })
            ->rawColumn('ctf.contentType', 'content_type')
            ->onAction(MediaResource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false)->validation('Content Type', 'required');
            })
            ->onAction(MediaResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->mediaField('file')
            ->validation('File', [
                'mimes' => $this->allowed_mimes
            ]);

        $list->field('size')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('files', 'files.fileID', '=', 'media.fileID');
            })
            ->rawColumn('files.size', 'size')
            ->onAction(MediaResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([MediaResource::ACTION_CREATE, MediaResource::ACTION_NESTED_CREATE])
            ->onAction(MediaResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list, true, false, true);

        $list->modify(['name'], function (Field $field) {
            return $field->enableAction(MediaResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, MediaResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('company_id')->disable();
            // if has user, then apply max size
            $list->getMedia('file')
                ->validation('File', [
                    'mimes' => $this->allowed_mimes,
                    'max_size' => '200MB'
                ]);
        }

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('file')
            ->directoryName('media')
            ->urlSlug('company-media')
            ->versions(function (MediaType $type) {
                $type->original()->handler(FileHandler::class);
            });

        return $list;
    }

    public function validationRules(Rules $rules, MediaResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        $rules->register('check_file_id', function (UuidInterface $id) use ($resource) {
            $file = $resource->relationResource('file')->find($id->toString());
            if ($file === null) {
                return 'file_not_found';
            }
            if ($file->type !== File::TYPE_MEDIA) {
                return 'file_invalid_type';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} must be a media file'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', MediaResource::getStatuses());
        return $config;
    }

    public function createModelDataAfter($model_data, Request $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }
        $model_data['status'] = Media::STATUS_ACTIVE;

        // if isBidMedia is passed we need to format it correctly because uppy passes it as a string and not bool
        if (isset($model_data['isBidMedia']) && $model_data['isBidMedia']) {
            $model_data['isBidMedia'] = true;
        }
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case Media::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case Media::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $model_data['archivedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        return $model_data;
    }

    public function anyCreateSaveAfter(CreateRequest $request)
    {
        $this->completeTrainingAction($request, TrainingAction::CREATE_MEDIA_ITEM);
    }

    public function close(Request $request)
    {
        $this->recordCompletedTrainingActions($request);
    }

    public function queryScopeGlobal($query, MediaResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->searchWithRank($term);
    }

    public function actionAllowed($action, MediaResource $resource)
    {
        if (($action & MediaResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function modelIsDeletable(Media $model)
    {
        // @todo check if is deletable
        return true;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'bid-list-v1':
            case 'bid-v1':
                $scope->fields([
                    'id', 'name', 'is_bid_media'
                ], true);
                $scope->filter('is_bid_media', 'eq', true);
                // only apply ordering and active filtering when grabbing the list for the modal otherwise we could
                // cause data to be missing from bid item media if the associated media item is archived
                if ($format === 'bid-list-v1') {
                    // disable pagination for the bid modal due to some companies having more than 100 media items
                    $scope->disablePagination();
                    $scope->query(function ($query) {
                        return $query->active()->ordered();
                    });
                }
                $scope->with([
                    'file' => [
                        'fields' => ['id', 'name', 'content_type', 'extension']
                    ]
                ]);
                break;
            case 'collection-v1':
                $scope->fields(['id', 'name', 'size', 'is_bid_media', 'created_at', 'updated_at', 'content_type'], true);
                $scope->filter('status', 'eq', MediaResource::STATUS_ACTIVE);
                $scope->with(['file_media_urls', 'file' => [
                    'fields' => ['content_type']
                ]]);
                break;
        }
    }
}
