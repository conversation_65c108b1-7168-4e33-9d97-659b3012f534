<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class Feature extends Model
{
    public const BID_V2 = 1;
    public const MEDIA_LIBRARY = 2;
    public const BID_V1 = 3;
    public const CREW_MANAGEMENT = 4;
    public const TEXT_MESSAGING = 5;
    public const CALENDAR_FEED = 6;
    public const DRAWING_APP = 7;
    public const ACH_PAYMENT = 8;
    public const PROJECT_FILES = 9;
    public const GOOGLE_API = 10;
    public const GOOGLE_CALENDAR = 11;
    public const MARKETPLACE = 12;
    public const CUSTOM_REPORTS = 13;
    public const PRODUCT_ATTRIBUTES = 14;
    public const PRODUCT_COMPONENTS = 15;
    public const BID_FOLLOW_UPS = 16;
    public const TASKS = 17;
    public const LEADS = 18;
    public const WISETACK_API = 19;
    public const WEBSITE_LEADS_FORM = 20;

    public const NOTIFICATION_CENTER = 21;

    protected $table = 'features';
    protected $primaryKey = 'featureID';
    protected $fillable = ['featureID', 'name'];
}
