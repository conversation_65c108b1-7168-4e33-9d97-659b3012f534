<?php

declare(strict_types=1);

namespace App\Services\Form\Traits\Field;

/**
 * Trait InternalTrait
 *
 * @package App\Services\Form\Traits\Field
 */
trait InternalTrait
{
    /**
     * @var bool|null Determines if input is marked as internal in UI
     */
    protected ?bool $display_is_internal = null;

    /**
     * Import nested array data with type check to prevent type errors
     *
     * @param array $config
     * @param array $data
     */
    abstract public function importFromArray(array $config, array $data): void;

    /**
     * Export class data to array format
     *
     * @param array $fields
     * @param array $config
     * @return array|null
     */
    abstract public function exportToArray(array $fields, array $config = []): ?array;

    /**
     * Set if field is marked as internal
     *
     * @param bool|null $status
     * @return $this
     */
    public function setDisplayIsInternal(?bool $status = true): self
    {
        $this->display_is_internal = $status;
        return $this;
    }

    /**
     * Get display is internal config item
     *
     * @return bool|null
     */
    public function getDisplayIsInternal(): ?bool
    {
        return $this->display_is_internal;
    }

    /**
     * Set class data from config array
     *
     * @param array $config
     */
    public function setInternalConfig(array $config): void
    {
        $this->importFromArray([
            'display.is_internal' => ['bool', 'setDisplayIsInternal']
        ], $config);
    }

    /**
     * Add internal display config items to existing list
     *
     * @param array $config
     * @return array
     */
    public function getInternalConfig(array $config): array
    {
        return $this->exportToArray([
            'display.is_internal' => 'getDisplayIsInternal'
        ], [
            'initial' => $config
        ]);
    }
}
