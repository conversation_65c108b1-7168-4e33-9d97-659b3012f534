/**
 * @module BidCreator/Entities/LineItem/General
 */

'use strict';

const lang = require('lodash/lang');

const Base = require('./base');

/**
 * @alias module:BidCreator/Entities/LineItem/General
 */
class General extends Base {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super(data, existing);
        this.state.type = Base.Type.GENERAL;
    };

    /**
     * Compare specified line item against internal data and return if they are equal
     *
     * @param {General} line_item
     * @returns {boolean}
     */
    matches(line_item) {
        if (
            line_item.name !== this.name ||
            !line_item.quantity.equals(this.quantity) ||
            !line_item.amount.equals(this.amount)
        ) {
            return false;
        }
        return true;
    };

    /**
     * Validate general line item
     *
     * @returns {Array}
     */
    validate() {
        let errors = super.validate();
        if (lang.isNil(this.amount)) {
            errors.push('No amount defined');
        }
        return errors;
    };
}

module.exports = General;
