<?php

namespace Common\Models\Interfaces;

/**
 * Interface LeadInterface
 *
 * @package Common\Models\Interfaces
 */
interface LeadInterface
{
    const STATUS_NEW = 1;
    const STATUS_WORKING = 2;
    const STATUS_CONVERTED = 3;
    const STATUS_DEAD = 4;

    const PRIORITY_HOT = 1;
    const PRIORITY_WARM = 2;
    const PRIORITY_COLD = 3;
    const PRIORITY_DEAD = 4;

    const ORIGIN_MANUAL_ADD = 1;
    const ORIGIN_WEBSITE_LEADS_FORM = 2;

    public function assignedTo();

    public function customer();

    public function convertedByUser();

    public function deadByUser();

    public function projectType();

    public function marketingType();

    public function tasks();

    public function workingByUser();

    public function scopeOfCompany($query, $company);

    public function scopeOfUser($query, $user);
}
