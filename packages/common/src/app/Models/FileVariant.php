<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class FileVariant extends HistoryEntityModel implements Interfaces\FileVariantInterface
{
    use Common\FileVariantCommon;

    protected $table = 'fileVariants';
    protected $primaryKey = 'fileVariantID';
    protected $fillable = [
        'fileVariantID', 'fileID', 'status', 'type', 'name', 'contentType', 'extension', 'size', 'data', 'contentHash',
        'time', 'isValid', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
