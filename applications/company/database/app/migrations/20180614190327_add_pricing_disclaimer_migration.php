<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class AddPricingDisclaimerMigration extends Migration
{
    public function up()
    {
        $this->updateTable('productItems')
            ->columns(function (Blueprint $table) {
                $table->string('pricingDisclaimer', 500)->nullable()->after('description');
            })
            ->alter();

        $this->updateTable('bidItemProductLineItems')
            ->columns(function (Blueprint $table) {
                $table->string('description', 1000)->nullable()->after('name');
                $table->string('pricingDisclaimer', 500)->nullable()->after('description');
            })
            ->column('unitID', Table::COLUMN_TYPE_UUID, [
                'after' => 'isIntangible'
            ])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('productItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('pricingDisclaimer');
            })
            ->alter();

        $this->updateTable('bidItemProductLineItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['description', 'pricingDisclaimer', 'unitID']);
            })
            ->alter();
    }
}
