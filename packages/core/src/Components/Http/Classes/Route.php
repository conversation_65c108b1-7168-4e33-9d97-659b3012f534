<?php

namespace Core\Components\Http\Classes;

use Core\Classes\Arr;
use Core\Components\Http\Interfaces\Route\BuilderInterface;
use Core\Components\Http\Interfaces\RouteInterface;

/**
 * Class Route
 *
 * @package Core\Components\Http\Classes
 */
class Route implements RouteInterface
{
    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $path;

    /**
     * @var array
     */
    protected $bindings;

    /**
     * @var array
     */
    protected $parsed_path;

    /**
     * @var array
     */
    protected $methods;

    /**
     * @var MiddlewareStack
     */
    protected $middleware;

    /**
     * @var array
     */
    protected $controller;

    /**
     * @var array
     */
    protected $input = [];

    /**
     * @param BuilderInterface $route
     */
    public function fromBuilder(BuilderInterface $route)
    {
        $this->name = $route->getName();
        $this->path = $route->getPath();
        $this->bindings = $route->getBindings();
        $this->parsed_path = $route->getParsedPath();
        $this->methods = $route->getMethods();
        $this->middleware = $route->getMiddleware();
        $this->controller = $route->getController();
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     * @param array $data
     * @return mixed|string
     */
    public function fillPath(array $data = [])
    {
        if (count($this->bindings) === 0) {
            return $this->path;
        }
        $data_keys = array_keys($data);
        $data_keys = array_map(function ($value) {
            return '{' . $value . '}';
        }, $data_keys);
        return str_replace($data_keys, array_values($data), $this->path);
    }

    /**
     * @return array
     */
    public function getBindings()
    {
        return $this->bindings;
    }

    /**
     * @return array
     */
    public function getParsedPath()
    {
        return $this->parsed_path;
    }

    /**
     * @return array
     */
    public function getMethods()
    {
        return $this->methods;
    }

    /**
     * @return MiddlewareStack
     */
    public function getMiddleware()
    {
        return $this->middleware;
    }

    /**
     * @return array
     */
    public function getController()
    {
        return $this->controller;
    }

    /**
     * @param array $input
     */
    public function setInput(array $input)
    {
        $this->input = $input;
    }

    /**
     * @param null|string $key
     * @param null|mixed $default
     * @return mixed
     */
    public function input($key = null, $default = null)
    {
        return Arr::get($this->input, $key, $default);
    }
}
