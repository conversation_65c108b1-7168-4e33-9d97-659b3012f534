<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="enable-button">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>

            <button class="c-pha-tertiary t-edit t-hidden" data-js="edit-button" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>

    <div class="m-company-settings m-leads-settings">
        <div class="c-cs-wrapper">
            <div class="c-csw-content space-between">
                    <div class="f-field flex no-margin" data-js="switch-container">
                        <h4 class="c-cswc-title">
                            Lead Website Form
                            <span data-tooltip data-type="info"> Allow leads to be captured from your website by embedding a form.</span>
                        </h4>
                    </div>
                    <div class="c-cswc-content" data-js="feature-status">
                        <span class="h-text t-grey">Not setup</span>
                    </div>
            </div>
        </div>

        <div data-js="form-preview-container" class="t-hidden leads-form-details-preview-container"></div>
        <p>Build a form that potential customers can fill out directly on your website. Once the form is submitted, the customer's information will automatically be added to the Leads section of {{brand_name}}. Simply design your form, copy the provided code, and paste it into any page on your website.</p>

        <div class="t-hidden" data-js="code-container">
            <p>Copy this code to use for on your website.</p>
            <div class="t-bottom-gap">
                <div class="c-s-content">
                    <div class="c-sc-group f-field">
                        <div class="c-scg-button">
                            <div class="c-scg-input f-f-input"><input data-js="html-code-input" id="code-snippet" type="text" readonly></div>
                            <button class="b-text t-primary" data-js="html-code-copy-button" data-clipboard-target="#code-snippet">Copy</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>