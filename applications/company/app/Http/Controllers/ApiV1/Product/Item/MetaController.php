<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1\Product\Item;

use App\Resources\Product\Item\MetaResource;
use App\Traits\Resource\Controller\ActionTrait;

/**
 * Class MetaController
 *
 * @package App\Http\Controllers\ApiV1\Product\Item
 */
class MetaController
{
    use ActionTrait;

    /**
     * @var string Resource class for controller
     */
    protected $resource = MetaResource::class;

    /**
     * MetaController constructor
     */
    public function __construct()
    {
        $this->registerFormat('bid-v1', 'application/vnd.adg.fx.bid-v1+json');
    }
}
