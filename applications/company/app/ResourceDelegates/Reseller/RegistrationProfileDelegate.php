<?php

namespace App\ResourceDelegates\Reseller;

use App\Resources\AffiliateResource;
use App\Resources\Reseller\RegistrationProfileResource;
use App\Resources\SubscriptionResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\Reseller;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Validation\Classes\Rules;

class RegistrationProfileDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('affiliate')->resource(AffiliateResource::class);
        $list->oneOrMany('subscription')->resource(SubscriptionResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('resellerRegistrationProfileID')
            ->noSave()
            ->onAction(RegistrationProfileResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('reseller_id')
            ->column('resellerID', true)
            ->validation('Reseller Id', 'required|type[int]|check_reseller_id');

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[100]');

        $list->field('description')
            ->validation('Description', 'trim|nullable|optional|max_length[200]');

        $list->field('code')
            ->validation('Code', 'trim|required|max_length[100]|check_code');

        $list->field('subscription_id')
            ->column('subscriptionID', true)
            ->validation('Subscription Id', 'required|type[int]|check_subscription_id');

        $list->field('affiliate_id')
            ->column('affiliateID', true)
            ->validation('Affiliate Id', 'nullable|optional|type[int]|check_affiliate_id');

        $list->field('is_email_verification_disabled')
            ->column('isEmailVerificationDisabled')
            ->validation('Is Email Verification Disabled', 'required|type[bool]');

        $list->field('is_active')
            ->column('isActive')
            ->validation('Is Active', 'required|type[bool]');

        $list->field('expires_at')
            ->typeDateTime()
            ->column('expiresAt');

        $this->timestampFields($list, false);

        return $list;
    }

    public function validationRules(Rules $rules, RegistrationProfileResource $resource)
    {
        $rules->register('check_reseller_id', function ($id) {
            // @todo convert to related resource check once Reseller resource is needed
            if (Reseller::withTrashed()->where('resellerID', $id)->count() !== 0) {
                return true;
            }
            return 'check_reseller_id';
        }, [
            'check_reseller_id' => 'Unable to find reseller'
        ]);

        $rules->register('check_code', function ($code) use ($resource) {
            if ($resource->isCodeInUse($code)) {
                return 'check_code';
            }
            return true;
        }, [
            'check_code' => '{label} already in use'
        ]);

        $rules->register('check_subscription_id', function ($id) use ($resource) {
            if ($resource->relationResource('subscription')->entityExists($id)) {
                return true;
            }
            return 'check_subscription_id';
        }, [
            'check_subscription_id' => 'Unable to find subscription'
        ]);

        $rules->register('check_affiliate_id', function ($id) use ($resource) {
            if ($resource->relationResource('affiliate')->entityExists($id)) {
                return true;
            }
            return 'check_affiliate_id';
        }, [
            'check_affiliate_id' => 'Unable to find affiliate'
        ]);

        return $rules;
    }

    public function actionAllowed($action, RegistrationProfileResource $resource)
    {
        if (($action & RegistrationProfileResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        return $resource->acl()->user() === null;
    }
}
