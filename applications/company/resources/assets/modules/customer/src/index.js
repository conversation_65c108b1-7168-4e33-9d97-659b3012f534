'use strict';

import $ from 'jquery';
import moment from 'moment-timezone';

import Api from '@ca-package/api';
import {findChild, insertTemplate, jsSelector} from '@ca-package/dom';
import {Base as Table} from '@ca-submodule/table';

import {Delete as DeleteModal} from './modals/delete';

const Cookie = require('@cac-js/utils/cookie');

import layout_tpl from '@cam-customer-tpl/layout.hbs';

/**
 * @memberof module:Customer
 */
export class Controller {
    /**
     * Customer constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            page_name: 'customer',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            layout,
            modals: {},
            table_scope: {
                sorts: {
                    first_name: Table.Sort.ASC,
                    last_name: Table.Sort.ASC,
                }
            }
        };
        this.boot();
    };

    /**
     * Get layout instance
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get delete modal
     *
     * @readonly
     *
     * @returns {module:Customer/Modals.Delete}
     */
    get delete_modal() {
        if (this.state.modals.delete === undefined) {
            this.state.modals.delete = new DeleteModal(this);
        }
        return this.state.modals.delete;
    };

    /**
     * Get table
     *
     * @readonly
     *
     * @returns {module:Table.Base}
     */
    get table() {
        return this.state.table;
    };

    /**
     * Trigger delete modal for row
     *
     * @param {Object} data - row data from table
     */
    handleDelete(data) {
        this.delete_modal.open(data);
    };

    /**
     * Generate customer management page url
     *
     * @param {number} id - customer id
     * @returns {string}
     */
    customerManagementUrl(id) {
        return window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', id);
    };

    /**
     * Generate property page url filtered by customer id
     *
     * @param {number} id - customer id
     * @returns {string}
     */
    propertyPageUrl(id) {
        return window.fx_pages.PROPERTIES.replace('{customer_id}', id);
    };

    /**
     * Define the default settings for customer DataTable
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            load_tooltip: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Create the customer DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                window.location.href = this.customerManagementUrl(data.id);
            }).on('scope_change', (scope) => {
                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_table_visibility`, config.visibility.toString());
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Customers'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            is_unsubscribed: {
                label: 'Unsubscribed',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            city: {
                label: 'Billing City',
                type: Table.FilterValueTypes.SELECT,
                options: customer_data.filter_options.cities
            },
            state: {
                label: 'Billing State',
                type: Table.FilterValueTypes.SELECT,
                options: customer_data.filter_options.states
            },
            zip: {
                label: 'Billing Postal Code',
                type: Table.FilterValueTypes.SELECT,
                options: customer_data.filter_options.postal_codes
            },

            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: customer_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: customer_data.filter_options.all_users
            }
        });

        // set columns config
        this.state.table.setColumns({
            first_name: {
                label: 'First Name',
                value: (data) => {
                    return `<a href="${this.customerManagementUrl(data.id)}">${data.first_name}</a>`
                }
            },
            last_name: {
                label: 'Last Name',
                value: (data) => {
                    return `<a href="${this.customerManagementUrl(data.id)}">${data.last_name}</a>`
                }
            },
            business_name: {
                label: 'Business Name',
                value: (data) => {
                    if (data.business_name === null) {
                        return;
                    }
                    return `<a href="${this.customerManagementUrl(data.id)}">${data.business_name}</a>`
                }
            },
            email: {
                label: 'Email',
            },
            primary_phone_number: {
                label: 'Primary Phone',
            },
            address: {
                label: 'Billing Address',
            },
            address_2: {
                label: 'Billing Address 2',
            },
            city: {
                label: 'Billing City',
            },
            state: {
                label: 'Billing State/Province',
            },
            zip: {
                label: 'Billing Postal Code',
            },
            is_unsubscribed: {
                label: 'Is Unsubscribed?',
                value: (data) => {
                    return data.is_unsubscribed === true ? 'Yes' : '';
                }
            },
            lead_name: {
                label: 'Lead Name',
                value: (data) => {
                    if (data.lead !== null) {
                        return `<a href="${fx_pages.LEAD.replace('{lead_id}', data.lead.lead_uuid)}">${data.lead_name}</a>`;
                    }
                    return;
                }
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            created_by_user_name: {
                label: 'Created By',
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            }
        });

        // set row action config
        this.state.table.setRowActions({
            view_details: {
                label: 'View Details',
                link: {
                    href: data => this.customerManagementUrl(data.id)
                }
            },
            view_properties: {
                label: 'View Properties',
                link: {
                    href: data => this.propertyPageUrl(data.id)
                }
            },
            delete: {
                label: 'Delete',
                visible: customer_data.delete,
                negate: true,
                action: (data) => {
                    this.handleDelete(data);
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    let url = window.fx_url.API + 'export/customers' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                    window.location.href = url;
                },
                visible: customer_data.export,
                type_class: 't-tertiary-icon',
                icon: 'system--download-line'
            },
            add: {
                label: 'New Customer',
                action: () => {
                    window.location.href = window.fx_pages.CUSTOMER_ADD;
                },
                visible: customer_data.add,
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.Customers, (request) => {
            request.accept('application/vnd.adg.fx.collection-v1+json');
        });
    };

    /**
     * Get Url parameters to build table scope
     *
     * @returns {string|null}
     */
    getUrlParameters() {
        let parameters = window.location.search;
        if (parameters === '') {
            return null;
        }
        return parameters.substring(1);
    };

    /**
     * Draw table
     */
    load() {
        let url_parameters = this.getUrlParameters();
        // if request query contains scope we use it
        if (url_parameters !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(url_parameters);
            // otherwise we get it from the cookie
        } else if (this.state.saved_scope !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
        }

        // otherwise we pull from the default scope stored in the state
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Boot customer
     */
    boot() {
        this.layout.setModeWindow();
        this.layout.setTitle('Customers');
        this.elem.root = insertTemplate(this.layout.elem.content, layout_tpl());
        this.elem.table = findChild(this.elem.root, jsSelector('data-table'));

        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }

        this.createTable();
        this.load();
    };
}
