<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateEvaluationInvoiceMigration extends Migration
{
    public function up()
    {
        Schema::fullTextIndex('evaluationInvoice', 'search', ['invoiceNumber']);
        Schema::fullTextIndex('evaluationBid', 'search', ['bidAcceptanceNumber', 'projectCompleteNumber']);
        Schema::fullTextIndex('customBid', 'search', ['bidAcceptanceNumber', 'projectCompleteNumber']);
    }

    public function down()
    {
        $this->schema->table('evaluationInvoice', function (Blueprint $table) {
            $table->dropIndex('search');
        });
        $this->schema->table('evaluationBid', function (Blueprint $table) {
            $table->dropIndex('search');
        });
        $this->schema->table('customBid', function (Blueprint $table) {
            $table->dropIndex('search');
        });
    }
}
