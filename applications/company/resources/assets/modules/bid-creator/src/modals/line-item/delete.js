/**
 * @module BidCreator/Modals/LineItem/Delete
 */

'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const LineItem = require('../../entities/line_item');

/**
 * @alias module:BidCreator/Modals/LineItem/Delete
 */
class Delete extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Delete Line Item');
    };

    /**
     * Open modal
     *
     * @param {module:BidCreator/Sidebar/Components/Pricing/LineItemForm} form
     * @param {module:BidCreator/Entities/LineItem/Base} line_item
     */
    open(form, line_item) {
        this.state.form = form;
        this.state.line_item = line_item;
        let types = {
            [LineItem.Type.GENERAL]: 'general',
            [LineItem.Type.PRODUCT]: 'product',
            [LineItem.Type.DISCOUNT]: 'discount',
            [LineItem.Type.FEE]: 'fee'
        };
        this.setContent(`<p>Are you sure you want to remove the ${types[line_item.type]} line item '${line_item.name}'?</p>`);
        super.open();
    };

    /**
     * Handle yes which means the user wants to delete
     */
    handleYes() {
        this.startWorking();
        this.state.form.setExternalClose(false);
        this.state.line_item.delete().then(() => {
            this.resetWorking();
            this.state.form.controller.pop();
            this.close();
        }, () => {
            // @todo implement proper flash message once they are built into modals
            alert('Unable to delete line item, please contact support');
            this.resetWorking();
        });
    };

    /**
     * Handle no which means the user wants to abort
     */
    handleNo() {
        this.state.form.setExternalClose(true);
        this.close();
    };
}

module.exports = Delete;
