<?php

use App\Classes\Queue\Drivers\RedisDriver;
use App\NotificationJobs;
use App\ResourceJobs;

return [
    'channels' => [
        'main' => [
            'id' => 1
        ],
        'google-api' => [
            'id' => 2
        ],
        'media' => [
            'id' => 3
        ],
        'hubspot-api' => [
            'id' => 4
        ]
    ],
    'default_channel' => 'main',
    'connections' => [
        'redis' => [
            'driver' => RedisDriver::class,
            'redis_connection' => 'queue',
            'db_connection' => 'utility'
        ]
    ],
    'default_connection' => 'redis',
    'jobs' => [
        //10
        //11

        // resource jobs
        ResourceJobs\Company\Invoice\PayJob::class, // 1
        ResourceJobs\Company\Subscription\SettleJob::class, //2
        ResourceJobs\Project\Event\CalendarPushJob::class, //6
        ResourceJobs\Project\Event\CalendarDeleteJob::class, //7
        ResourceJobs\Bid\Item\GenerateJob::class, //19
        ResourceJobs\User\MailingListPushJob::class, //22
        ResourceJobs\User\MailingListRemoveJob::class, //25
        ResourceJobs\User\MarketingListPushJob::class, //37
        ResourceJobs\Company\GoogleDisconnectJob::class, //33
        ResourceJobs\User\GoogleDisconnectJob::class, //34
        ResourceJobs\Company\CustomReport\GenerateJob::class, //35
        ResourceJobs\Company\MarketingListPushJob::class, //36
        ResourceJobs\Company\SupportListPushJob::class, //4

        // jobs
        \App\Services\GoogleApi\Calendar\Jobs\ProjectEventUpdateOrCreateJob::class, //3
        \App\Services\GoogleApi\Calendar\Jobs\ProjectEventDeleteJob::class, //4
        \App\Services\GoogleApi\Calendar\Jobs\EventPullAllJob::class, //8
        \App\Services\GoogleApi\Calendar\Jobs\EventPushAllJob::class, //9
        \App\Services\GoogleApi\Calendar\Jobs\DisconnectJob::class, //12
        \App\Services\Email\Jobs\BuildJob::class, //14
        \App\Services\Email\Jobs\SendJob::class, //15
        \App\Services\Text\Jobs\BuildJob::class, //23
        \App\Services\Text\Jobs\SendJob::class, //24
        \App\Services\GoogleApi\Calendar\Jobs\ExpandPullWindowJob::class, //27
        \App\Services\GoogleApi\Calendar\Jobs\ExpandPushWindowJob::class, //28
        \App\Services\GoogleApi\Calendar\Jobs\NotificationChannelStartJob::class, //29
        \App\Services\GoogleApi\Calendar\Jobs\RemoveJob::class, //30
        \App\Services\GoogleApi\Jobs\DisconnectJob::class, //31
        \App\Services\HubspotApi\Jobs\CompanyUpdateOrCreateJob::class, //39
        \App\Services\HubspotApi\Jobs\ContactUpdateOrCreateJob::class, //40
        \App\Services\HubspotApi\Jobs\DealUpdateOrCreateJob::class, //41
        \App\Services\HubspotApi\Jobs\DealSyncContactsJob::class, //42
        \App\Services\Training\Jobs\CompleteActionJob::class, //43
        \App\Jobs\CalendarSyncJob::class, //32

        // notification jobs
        NotificationJobs\Customer\CreatedNotificationJob::class, //13
        NotificationJobs\Evaluation\ViewNotificationJob::class, //16
        NotificationJobs\Bid\Item\ViewNotificationJob::class, //18

        NotificationJobs\Bid\Item\FollowUpNotification1Job::class, //45
        NotificationJobs\Bid\Item\FollowUpNotification2Job::class, //46
        NotificationJobs\Bid\Item\FollowUpNotification3Job::class, //47
        NotificationJobs\Bid\Item\FollowUpNotification4Job::class, //48
        NotificationJobs\Bid\Item\FollowUpNotification5Job::class, //49

        NotificationJobs\Bid\Item\FollowUpNotification6Job::class, //53
        NotificationJobs\Bid\Item\FollowUpNotification7Job::class, //54
        NotificationJobs\Bid\Item\FollowUpNotification8Job::class, //55
        NotificationJobs\Bid\Item\FollowUpNotification9Job::class, //56
        NotificationJobs\Bid\Item\FollowUpNotification10Job::class, //57

        NotificationJobs\Email\Message\RecipientBounceNotificationJob::class, //20
        NotificationJobs\Project\Event\CreatedNotificationJob::class, //5
        NotificationJobs\Project\Event\ReminderNotificationJob::class, //21
        NotificationJobs\User\ProjectEventCreatedNotificationJob::class, //60
        NotificationJobs\User\AccessLinkNotificationJob::class, //26
        NotificationJobs\User\LeadAssignmentNotificationJob::class, //51
        NotificationJobs\User\PasswordResetNotificationJob::class, //17
        NotificationJobs\User\ProjectAssignmentNotificationJob::class, //52
        NotificationJobs\User\TaskAssignmentNotificationJob::class, //50
        NotificationJobs\User\BidAcceptedNotificationJob::class, //58
        NotificationJobs\User\BidRejectedNotificationJob::class, //59

        NotificationJobs\User\BidViewNotificationJob::class, //61
    ]
];
