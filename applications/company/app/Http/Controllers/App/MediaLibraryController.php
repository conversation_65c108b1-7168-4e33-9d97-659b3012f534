<?php

namespace App\Http\Controllers\App;

use App\Resources\MediaResource;
use App\Services\CompanyFeatureService;
use Common\Models\Feature;
use Core\Classes\File;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;

class MediaLibraryController
{
    protected $request;

    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }

    protected function getIcons()
    {
        $icon_file_types = [
            'icon-file-picture' => ['png', 'jpeg', 'jpg', 'tiff', 'gif'],
            'icon-file-video' => ['avi', 'mp4', 'm4v', 'mkv', 'webm', 'mov', 'wmv', 'mpg', 'ogv'],
            'icon-file-music' => ['mp3', 'm4a', 'ogg', 'wav'],
            'icon-file-zip' => ['zip'],
            'icon-file-pdf' => ['pdf'],
            'icon-file-word' => ['doc', 'docx'],
            'icon-file-excel' => ['xls', 'xlsx', 'csv']
        ];
        $file_type_icons = [];
        foreach ($icon_file_types as $icon => $extensions) {
            foreach ($extensions as $extension) {
                if (isset($file_type_icons[$extension])) {
                    throw new AppException('File extension assigned to multiple icons');
                }
                $file_type_icons[$extension] = $icon;
            }
        }
        return $file_type_icons;
    }

    public function viewAll()
    {
        $user = Auth::user();
        if (!(new CompanyFeatureService($user->companyID))->has(Feature::MEDIA_LIBRARY, true)) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $scope = Scope::make()
            ->fields(['name', 'description'])
            ->filter('status', 'eq', MediaResource::STATUS_ACTIVE)
            ->sort('name', 'asc')
            ->with(['file_media_urls', 'file' => ['fields' => ['extension', 'size']]]);
        $_items = MediaResource::make(Auth::acl())->collection()
            ->scope($scope)->run();
        $items = [];
        $icons = $this->getIcons();
        foreach ($_items as $_item) {
            $items[] = [
                'icon' => (isset($icons[$_item['file']['extension']]) ? $icons[$_item['file']['extension']] : 'icon-file-empty'),
                'name' => $_item['name'],
                'file_size' => File::formatFilesize($_item['file']['size']),
                'description' => $_item['description'],
                'view_link' => $_item['file_media_urls']['original']
            ];
        }

        return Response::view('pages.app.media-library', [
            'items' => $items
        ]);
    }
}
