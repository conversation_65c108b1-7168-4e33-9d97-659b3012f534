<?php

namespace Common\Models\Common;

use Common\Models\BidItemLineItem;
use Common\Models\BidItemProductMaterialLineItem;
use Common\Models\BidItemProductAdditionalCostLineItem;
use Common\Models\Company;
use Common\Models\ProductItem;
use Common\Models\ProductItemPrice;
use Common\Models\Unit;
use Common\Traits\DB\UuidTrait;

trait BidItemProductLineItemCommon
{
    use UuidTrait;

    protected $castMap = [
        'isIntangible' => 'bool',
        'adjustmentMode' => 'int',
        'adjustmentType' => 'int',
        'isComponentAdjustment' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function lineItem()
    {
        return $this->morphOne(BidItemLineItem::class, 'lineItem', 'itemType', 'itemID');
    }

    public function productItem()
    {
        return $this->belongsTo(ProductItem::class, 'productItemID', 'productItemID');
    }

    public function productItemPrice()
    {
        return $this->belongsTo(ProductItemPrice::class, 'productItemPriceID', 'productItemPriceID');
    }

    public function materials()
    {
        return $this->hasMany(BidItemProductMaterialLineItem::class, 'bidItemProductLineItemID');
    }

    public function additionalCosts()
    {
        return $this->hasMany(BidItemProductAdditionalCostLineItem::class, 'bidItemProductLineItemID');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unitID', 'unitID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('user', 'user.userID', '=', "{$this->table}.createdByUserID");
        return $query->where('user.companyID', $company);
    }
}
