<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Company\CustomReport;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use Core\Classes\File;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;

/**
 * Class ResultFileHandler
 *
 * @package App\ResourceMediaHandlers\Company\CustomReport
 */
class ResultFileHandler extends BaseCompanyFileHandler
{
    /**
     * Get response for media controller
     *
     * @param string $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function getResponse($id, array $config = []): FileResponse
    {
        $result = $this->resource->findOrFail($id);

        $path = $this->getPathFromFileField('file_id', $result);
        $response = Response::file($path)
            ->contentType($result->file->contentType)
            ->filename(File::sanitizeName("{$result->customReport->name}_{$result->createdAt->format('Ymd_His')}", $result->file->extension));
        if (isset($config['download'])) {
            $response->download();
        }
        return $response;
    }
}
